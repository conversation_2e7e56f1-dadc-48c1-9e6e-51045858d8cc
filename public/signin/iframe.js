var ACCESS_TOKEN = 'Access-Token'
var PERSON_ID = 'PERSON_ID'
var OPERATOR = 'OPERATOR'
var ORG_ID = 'ORG_ID'

Vue.use(VueStorage, {
    namespace: 'nbggoods__',
    name: 'ls',
    storage: 'local'
})
window.addEventListener(
  'message',
  function (e) {
    if (e.source === window.parent) {
      try {
        var DATA = JSON.parse(e.data) || {}
        var DATA_ACCESS_TOKEN = DATA.ACCESS_TOKEN
        var DATA_PERSON_ID = DATA.PERSON_ID
        var DATA_OPERATOR = DATA.OPERATOR
        var DATA_ORG_ID = DATA.ORG_ID
        let a = {"value" : DATA_ACCESS_TOKEN,"expire" : null}
        let b = {"value" : DATA_PERSON_ID,"expire" : null}
        let c = {"value" : DATA_OPERATOR,"expire" : null}
        let d = {"value" : DATA_ORG_ID,"expire" : null}
        localStorage.setItem("nbggoods__" + ACCESS_TOKEN, JSON.stringify(a))
        localStorage.setItem("nbggoods__" + PERSON_ID, JSON.stringify(b))
        localStorage.setItem("nbggoods__" + OPERATOR, JSON.stringify(c))
        localStorage.setItem("nbggoods__" + ORG_ID, JSON.stringify(d))
        window.parent.postMessage('success', '*')
      } catch (e) {
        window.parent.postMessage('error', '*')
      }
    } else {
      window.parent.postMessage('error', '*')
    }
  },
  false
)