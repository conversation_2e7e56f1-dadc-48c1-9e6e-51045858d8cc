
/* eslint-disable */
/**
 * 该文件是为了按需加载
 * 剔除掉了一些不需要的框架组件
 * 减少了编译支持库包大小
 * 当需要更多组件依赖时
 * 在该文件加入即可
 */
import Vue from 'vue'
import {
  Carousel,
  Transfer,
  Cascader,
  ConfigProvider,
  LocaleProvider,
  Layout,
  Input,
  InputNumber,
  Button,
  Switch,
  Radio,
  Checkbox,
  Select,
  TreeSelect,
  AutoComplete,
  Card,
  Form,
  Row,
  Col,
  Modal,
  Table,
  Tabs,
  Icon,
  Badge,
  Popover,
  Dropdown,
  List,
  Avatar,
  Breadcrumb,
  Steps,
  Spin,
  Menu,
  Drawer,
  Tooltip,
  Alert,
  Tag,
  Divider,
  DatePicker,
  TimePicker,
  Upload,
  Progress,
  Skeleton,
  Popconfirm,
  Message,
  Notification,
  Descriptions,
  Timeline,
  Empty,
  Tree
} from 'ant-design-vue'

Vue.use(Carousel)
Vue.use(Transfer)
Vue.use(Cascader)
Vue.use(ConfigProvider)
Vue.use(LocaleProvider)
Vue.use(Layout)
Vue.use(Input)
Vue.use(InputNumber)
Vue.use(Button)
Vue.use(Switch)
Vue.use(Radio)
Vue.use(Checkbox)
Vue.use(Select)
Vue.use(TreeSelect)
Vue.use(AutoComplete)
Vue.use(Card)
Vue.use(Form)
Vue.use(Row)
Vue.use(Col)
Vue.use(Modal)
Vue.use(Table)
Vue.use(Tabs)
Vue.use(Icon)
Vue.use(Badge)
Vue.use(Popover)
Vue.use(Dropdown)
Vue.use(List)
Vue.use(Avatar)
Vue.use(Breadcrumb)
Vue.use(Steps)
Vue.use(Spin)
Vue.use(Menu)
Vue.use(Drawer)
Vue.use(Tooltip)
Vue.use(Alert)
Vue.use(Tag)
Vue.use(Divider)
Vue.use(DatePicker)
Vue.use(TimePicker)
Vue.use(Upload)
Vue.use(Progress)
Vue.use(Skeleton)
Vue.use(Popconfirm)
Vue.use(Notification)
Vue.use(Descriptions)
Vue.use(Timeline)
Vue.use(Empty)
Vue.use(Tree)

Notification.config({
  duration: 2
})

Vue.prototype.$message = Message
Vue.prototype.$notification = Notification
Vue.prototype.$success = Modal.$success
Vue.prototype.$confirm = Modal.confirm
Vue.prototype.$warning = Modal.warning
Vue.prototype.$error = Modal.error
Vue.prototype.$info = Modal.info