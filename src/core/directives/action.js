import Vue from 'vue'
import store from '@/store'

/**
 * Action 权限指令
 *  指令用法：
 *    - 在需要控制 action 级别权限的组件上使用 v-action:[method]
 *
 *     有权限显示元素
 *       <a-button v-action:add >添加用户</a-button>
 *       <a-button v-action:delete>删除用户</a-button>
 *
 *     无权限显示元素
 *       <a-button v-noAction:add >添加用户</a-button>
 *       <a-button v-noAction:delete>删除用户</a-button>
 */
Vue.directive('action', {
  update: function (el, binding, vnode) {
    // const actionName = binding.arg
    const roles = store.getters.roles
    const elVal = vnode.context.$route.meta.permission
    const permissionId = elVal instanceof String && [elVal] || elVal

    for (const p of roles.permissions) {
      if (!permissionId) {
        return
      }
      if (!permissionId.includes(p.permissionId)) {
        continue
      }

      // 弱校验
      // if (p.actionList && !p.actionList.includes(actionName)) {
      //   el.parentNode && el.parentNode.removeChild(el) || (el.style.display = 'none')
      // }

      // 严格校验
      // if (!p.actionList || !p.actionList.includes(actionName)) {
      //   el.parentNode && el.parentNode.removeChild(el) || (el.style.display = 'none')
      // }
      return
    }

    // 严格校验
    el.parentNode && el.parentNode.removeChild(el) || (el.style.display = 'none')
  },
  inserted: function (el, binding, vnode) {
    const actionName = binding.arg
    const roles = store.getters.roles
    const elVal = vnode.context.$route.meta.permission
    const permissionId = elVal instanceof String && [elVal] || elVal
    for (const p of roles.permissions) {
      if (!permissionId) {
        return
      }
      if (!permissionId.includes(p.permissionId)) {
        continue
      }

      // 弱校验
      // if (p.actionList && !p.actionList.includes(actionName)) {
      //   el.parentNode && el.parentNode.removeChild(el) || (el.style.display = 'none')
      // }

      // 严格校验
      if (!p.actionList || !p.actionList.includes(actionName)) {
        el.parentNode && el.parentNode.removeChild(el) || (el.style.display = 'none')
      }
      return
    }

    // 严格校验
    el.parentNode && el.parentNode.removeChild(el) || (el.style.display = 'none')
  }
})

Vue.directive('noAction', {
  update: function (el, binding, vnode) {
    const actionName = binding.arg
    const roles = store.getters.roles
    const elVal = vnode.context.$route.meta.permission
    const permissionId = elVal instanceof String && [elVal] || elVal
    for (const p of roles.permissions) {
      if (!permissionId) {
        return
      }
      if (permissionId.includes(p.permissionId)) {
        continue
      }

      // 弱校验
      // if (!p.actionList || p.actionList.includes(actionName)) {
      //   el.parentNode && el.parentNode.removeChild(el) || (el.style.display = 'none')
      // }

      // 严格校验
      if (p.actionList && p.actionList.includes(actionName)) {
        el.parentNode && el.parentNode.removeChild(el) || (el.style.display = 'none')
      }

      return
    }

    // 弱校验
    // el.parentNode && el.parentNode.removeChild(el) || (el.style.display = 'none')
  },
  inserted: function (el, binding, vnode) {
    const actionName = binding.arg
    const roles = store.getters.roles
    const elVal = vnode.context.$route.meta.permission
    const permissionId = elVal instanceof String && [elVal] || elVal
    for (const p of roles.permissions) {
      if (!permissionId) {
        return
      }
      if (permissionId.includes(p.permissionId)) {
        continue
      }

      // 弱校验
      // if (!p.actionList || p.actionList.includes(actionName)) {
      //   el.parentNode && el.parentNode.removeChild(el) || (el.style.display = 'none')
      // }

      // 严格校验
      if (p.actionList && p.actionList.includes(actionName)) {
        el.parentNode && el.parentNode.removeChild(el) || (el.style.display = 'none')
      }

      return
    }

    // 弱校验
    // el.parentNode && el.parentNode.removeChild(el) || (el.style.display = 'none')
  }
})
