<template>
  <a-modal
    :title="modalTitle"
    :visible="visible"
    :confirm-loading="confirmLoading"
    @cancel="handleCancel"
    :width="900"
    :footer="null"
    :destroyOnClose="true"
  >
    <a-tabs default-active-key="basic" :animated="false" :tab-position="'left'" @change="handleTabChange">
      <a-tab-pane key="basic" tab="基础信息">
        <a-divider orientation="left">车辆基本信息</a-divider>
        <a-form :form="form" :label-col="{ style: { width: '105px' } }" :wrapper-col="{ span: 16 }">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="车牌号" required>
                <a-input
                  v-decorator="[
                    'plateNumber',
                    {
                      rules: [{ required: true, message: '请输入车牌号!' }],
                      initialValue: currentVehicle.plateNumber
                    }
                  ]"
                  :class="{ 'pointNone': !showSubmitButton}"
                  placeholder="请输入车牌号"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="车辆类别" required>
                <a-select
                  v-decorator="[
                    'vehicleType',
                    {
                      rules: [{ required: true, message: '请选择车辆类别!' }],
                      initialValue: currentVehicle.vehicleType
                    }
                  ]"
                  :class="{ 'pointNone': !showSubmitButton}"
                  placeholder="请选择车辆类别"
                >
                  <a-select-option
                    v-for="(item, index) in vehicleTypes"
                    :value="item.value"
                    :key="index"
                    :label="item.label"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="车辆型号">
                <a-input
                  v-decorator="[
                    'model',
                    {
                      initialValue: currentVehicle.model
                    }
                  ]"
                  :class="{ 'pointNone': !showSubmitButton}"
                  placeholder="请输入车辆型号"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="车辆状态" required>
                <a-radio-group
                  :class="{ 'pointNone': !showSubmitButton}"
                  v-decorator="[
                    'vehicleStatus',
                    {
                      rules: [{ required: true, message: '请选择车辆状态!' }],
                      initialValue: currentVehicle.vehicleStatus || '1'
                    }
                  ]"
                >
                  <a-radio value="1">正常</a-radio>
                  <a-radio value="0">停用</a-radio>
                  <a-radio value="3">报废</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="车辆识别号(VIN)">
                <a-input
                  v-decorator="[
                    'vin',
                    {
                      rules: [{ required: true, message: '车辆识别号(VIN)!' }],
                      initialValue: currentVehicle.vin
                    }
                  ]"
                  placeholder="请输入车辆识别号"
                  :class="{ 'pointNone': !showSubmitButton}"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="发动机号码">
                <a-input
                  v-decorator="[
                    'engineNumber',
                    {
                      initialValue: currentVehicle.engineNumber
                    }
                  ]"
                  placeholder="请输入发动机号码"
                  :class="{ 'pointNone': !showSubmitButton}"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="行驶总里程" required>
                <a-input
                  :class="{ 'pointNone': !showSubmitButton}"
                  type="number"
                  v-decorator="[
                    'totalMileage',
                    {
                      initialValue: currentVehicle.totalMileage
                    }
                  ]"
                  :min="0"
                  suffix="km"
                  style="width: 100%"
                  placeholder="请填写行驶总里程"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="载重/载客量">
                <a-input
                  v-decorator="[
                    'capacity',
                    {
                      initialValue: currentVehicle.capacity
                    }
                  ]"
                  style="width: 100%"
                  placeholder="请输入载重/载客量"
                  :class="{ 'pointNone': !showSubmitButton}"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="所属部门" required>
                <a-select
                  showSearch
                  optionFilterProp="label"
                  v-decorator="[
                    'departmentSysId',
                    {
                      rules: [{ required: true, message: '请选择所属部门!' }],
                      initialValue: currentVehicle.departmentSysId
                    }
                  ]"
                  @change="deptChange"
                  placeholder="请选择所属部门"
                  :class="{ 'pointNone': !showSubmitButton}"
                >
                  <a-select-option
                    v-for="(item, index) in depts"
                    :value="item.value"
                    :key="index"
                    :label="item.label"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="车辆管理员" required>
                <a-select
                  showSearch
                  optionFilterProp="label"
                  v-decorator="['managerId', {
                    rules: [{ type: 'string', required: true, message: '请选择车辆管理员' }],
                    initialValue: currentVehicle.managerId
                  }]"
                  :class="{ 'pointNone': !showSubmitButton}"
                >
                  <a-select-option
                    v-for="(item, index) in deptPersons"
                    :value="item.value"
                    :label="item.label"
                    :key="index"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="所有人" required>
                <a-input
                  v-decorator="[
                    'owner',
                    {
                      initialValue: currentVehicle.owner
                    }
                  ]"
                  style="width: 100%"
                  placeholder="请输入所有人"
                  :class="{ 'pointNone': !showSubmitButton}"
                />
              </a-form-item>
            </a-col>
            <!-- <a-col :span="12">
              <a-form-item label="购买日期">
                <a-date-picker
                  v-decorator="[
                    'purchaseDate',
                    {
                      initialValue: currentVehicle.purchaseDate ? moment(currentVehicle.purchaseDate) : null
                    }
                  ]"
                  style="width: 100%"
                  placeholder="请选择购买日期"
                />
              </a-form-item>
            </a-col> -->
          </a-row>

          <!-- <a-form-item label="车辆图片">
            <a-upload
              name="file"
              list-type="picture-card"
              :file-list="fileList"
              :customRequest="customUploadRequest"
              :action="uploadAction"
              @preview="handlePreview"
              @change="handleChange"
              :before-upload="beforeUpload"
            >
              <div v-if="fileList.length < 5">
                <a-icon type="plus" />
                <div class="ant-upload-text">上传</div>
              </div>
            </a-upload>
            <div class="upload-tip">支持JPG、PNG格式，单张图片不超过2MB，最多上传5张</div>
          </a-form-item> -->

          <a-divider orientation="left">保养与年检设置</a-divider>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="保养时间周期" required>
                <a-input
                  type="number"
                  v-decorator="[
                    'maintenanceMonthCycle',
                    {
                      rules: [{ required: true, message: '请输入保养时间周期!' }],
                      initialValue: currentVehicle.maintenanceMonthCycle || 12
                    }
                  ]"
                  suffix="月"
                  :min="1"
                  style="width: 100%"
                  placeholder="请输入保养时间周期"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="最近保养时间">
                <a-date-picker
                  class="pointNone"
                  v-decorator="[
                    'lastMaintenanceTime',
                    {
                      initialValue: currentVehicle.lastMaintenanceTime ? moment(currentVehicle.lastMaintenanceTime) : null
                    }
                  ]"
                  style="width: 100%"
                  placeholder=""
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="保养里程周期" required>
                <a-input
                  v-decorator="[
                    'maintenanceMileageCycle',
                    {
                      rules: [{ required: true, message: '请输入保养里程周期!' }],
                      initialValue: currentVehicle.maintenanceMileageCycle || 5000
                    }
                  ]"
                  type="number"
                  suffix="km"
                  :min="0"
                  style="width: 100%"
                  placeholder="请输入保养里程周期"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="最近保养里程">
                <a-input
                  class="pointNone"
                  type="number"
                  v-decorator="[
                    'lastMaintenanceMileage',
                    {
                      initialValue: currentVehicle.lastMaintenanceMileage
                    }
                  ]"
                  suffix="km"
                  :min="0"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="年检时间周期" required>
                <a-input
                  v-decorator="[
                    'inspectionCycle',
                    {
                      rules: [{ required: true, message: '请输入年检时间周期!' }],
                      initialValue: currentVehicle.inspectionCycle || 12
                    }
                  ]"
                  type="number"
                  suffix="月"
                  :min="1"
                  style="width: 100%"
                  placeholder="请输入年检时间周期"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="最近年检时间">
                <a-date-picker
                  class="pointNone"
                  v-decorator="[
                    'lastInspectionTime',
                    {
                      initialValue: currentVehicle.lastInspectionTime ? moment(currentVehicle.lastInspectionTime) : null
                    }
                  ]"
                  style="width: 100%"
                  placeholder=""
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="备注">
            <a-textarea
              v-decorator="[
                'remarks',
                {
                  initialValue: currentVehicle.remarks
                }
              ]"
              :rows="4"
              placeholder="请输入备注信息"
              :class="{ 'pointNone': !showSubmitButton}"
            />
          </a-form-item>

          <div class="form-actions" v-if="showSubmitButton">
            <a-button @click="handleCancel">取消</a-button>
            <a-button type="primary" @click="handleOk" :loading="confirmLoading">保存</a-button>
          </div>
          <div class="form-actions" v-else>
            <a-button @click="handleCancel">关闭</a-button>
          </div>
        </a-form>
      </a-tab-pane>

      <a-tab-pane key="maintenance" tab="保养记录" :disabled="!editMode">
        <div class="tab-content">
          <a-spin :spinning="tabLoading">
            <a-empty v-if="!tabLoading && vehicleMaintenances.length === 0" description="暂无保养记录" />
            <a-timeline v-else>
              <a-timeline-item v-for="record in vehicleMaintenances" :key="record.uuid" :color="getTimelineColor(record.maintenanceTime)">
                <div class="timeline-content">
                  <div class="timeline-header">
                    <span class="timeline-title">保养日期: {{ record.maintenanceTime }}</span>
                    <span class="timeline-extra">保养里程数: {{ record.maintenanceMileage }} km</span>
                  </div>
                  <div class="timeline-body">
                    <p>登记人: {{ record.registrantName }}</p>
                    <p>登记时间: {{ record.registrationTime }}</p>
                    <p v-if="record.remarks">备注: {{ record.remarks }}</p>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-spin>
        </div>
      </a-tab-pane>

      <a-tab-pane key="inspection" tab="年检记录" :disabled="!editMode">
        <div class="tab-content">
          <a-spin :spinning="tabLoading">
            <a-empty v-if="!tabLoading && vehicleInspections.length === 0" description="暂无年检记录" />
            <a-timeline v-else>
              <a-timeline-item v-for="record in vehicleInspections" :key="record.uuid" :color="getTimelineColor(record.inspectionTime)">
                <div class="timeline-content">
                  <div class="timeline-header">
                    <span class="timeline-title">年检日期: {{ record.inspectionTime }}</span>
                  </div>
                  <div class="timeline-body">
                    <p>登记人: {{ record.registrantName }}</p>
                    <p>登记时间: {{ record.registrationTime }}</p>
                    <p v-if="record.remarks">备注: {{ record.remarks }}</p>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-spin>
        </div>
      </a-tab-pane>

      <a-tab-pane key="mileage" tab="里程记录" :disabled="!editMode">
        <div class="tab-content">
          <a-spin :spinning="tabLoading">
            <a-empty v-if="!tabLoading && vehicleMileages.length === 0" description="暂无里程记录" />
            <a-timeline v-else>
              <a-timeline-item v-for="record in vehicleMileages" :key="record.uuid" :color="getTimelineColor(record.registrationTime)">
                <div class="timeline-content">
                  <div class="timeline-header">
                    <span class="timeline-title">登记日期: {{ record.registrationTime }}</span>
                    <span class="timeline-extra">里程数: {{ record.currentMileage }} km</span>
                  </div>
                  <div class="timeline-body">
                    <p>登记人: {{ record.registrantName }}</p>
                    <p v-if="record.mileageChange">里程变化: <span :style="{ color: record.mileageChange > 0 ? '#52c41a' : '#999' }">{{ record.mileageChange > 0 ? '+' : '' }}{{ record.mileageChange }} km</span></p>
                    <p v-if="record.remarks">备注: {{ record.remarks }}</p>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-spin>
        </div>
      </a-tab-pane>

      <a-tab-pane key="repair" tab="维修记录" :disabled="!editMode">
        <div class="tab-content">
          <a-spin :spinning="tabLoading">
            <a-empty v-if="!tabLoading && vehicleRepairs.length === 0" description="暂无维修记录" />
            <a-timeline v-else>
              <a-timeline-item v-for="record in vehicleRepairs" :key="record.uuid" :color="getTimelineColor(record.repairDate)">
                <div class="timeline-content">
                  <div class="timeline-header">
                    <span class="timeline-title">维修日期: {{ record.repairDate }}</span>
                    <span class="timeline-extra">维修金额: {{ record.repairAmount }} 元</span>
                  </div>
                  <div class="timeline-body">
                    <p>维修内容: {{ record.repairContent }}</p>
                    <p>维修单位: {{ record.repairCompany }}</p>
                    <p>维修时里程: {{ record.mileage }} km</p>
                    <p v-if="record.registrantName">登记人: {{ record.registrantName }}</p>
                    <p v-if="record.registrationTime">登记时间: {{ record.registrationTime }}</p>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-spin>
        </div>
      </a-tab-pane>

      <a-tab-pane key="gas" tab="加油记录" :disabled="!editMode">
        <div class="tab-content">
          <a-spin :spinning="tabLoading">
            <a-empty v-if="!tabLoading && vehicleGases.length === 0" description="暂无加油记录" />
            <a-timeline v-else>
              <a-timeline-item v-for="record in vehicleGases" :key="record.uuid" :color="getTimelineColor(record.tradingDate)">
                <div class="timeline-content">
                  <div class="timeline-header">
                    <span class="timeline-title">交易日期: {{ record.tradingDate }}</span>
                    <span class="timeline-extra">加油量: {{ record.litres }} L</span>
                  </div>
                  <div class="timeline-body">
                    <p>卡号: {{ record.cardNum }}</p>
                    <p>油品: {{ record.oil }}</p>
                    <p>交易网点: {{ record.tradingOutlets }}</p>
                    <p v-if="record.registrantName">登记人: {{ record.registrantName }}</p>
                    <p v-if="record.registrationTime">登记时间: {{ record.registrationTime }}</p>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-spin>
        </div>
      </a-tab-pane>

      <a-tab-pane key="reminder" tab="到期提醒" :disabled="!editMode">
        <div class="tab-content">
          <a-row :gutter="16">
            <a-col :span="24">
              <a-card :bordered="false" class="status-overview-card">
                <div class="status-header">
                  <h3>车辆状态概览</h3>
                </div>
                <div class="status-content">
                  <div class="status-item" :class="getStatusClass(currentVehicle.maintenanceStatus)">
                    <div class="status-icon">
                      <a-icon :type="getStatusIcon(currentVehicle.maintenanceStatus)" />
                    </div>
                    <div class="status-info">
                      <div class="status-title">保养状态</div>
                      <div class="status-value">{{ currentVehicle.maintenanceStatus }}</div>
                    </div>
                  </div>
                  <div class="status-item" :class="getStatusClass(currentVehicle.inspectionStatus)">
                    <div class="status-icon">
                      <a-icon :type="getStatusIcon(currentVehicle.inspectionStatus)" />
                    </div>
                    <div class="status-info">
                      <div class="status-title">年检状态</div>
                      <div class="status-value">{{ currentVehicle.inspectionStatus }}</div>
                    </div>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>

          <a-row :gutter="16" style="margin-top: 16px">
            <a-col :span="24">
              <a-card title="保养信息" :bordered="false" class="detail-card">
                <a-alert
                  :message="currentVehicle.maintenanceDetailStatus"
                  :description="getMaintenanceDetailDescription(currentVehicle)"
                  :type="getAlertType(currentVehicle.maintenanceStatus)"
                  show-icon
                  style="margin-bottom: 16px"
                />

                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-card
                      title="保养时间"
                      size="small"
                      :bordered="false"
                      class="reminder-card"
                      :headStyle="getCardHeadStyle(currentVehicle.maintenanceTimeStatus)">
                      <a-descriptions bordered :column="1">
                        <a-descriptions-item label="最近保养时间">
                          <span>{{ currentVehicle.lastMaintenanceTime || '未记录' }}</span>
                        </a-descriptions-item>
                        <a-descriptions-item label="保养时间周期">
                          <span>{{ currentVehicle.maintenanceMonthCycle || 12 }} 个月</span>
                        </a-descriptions-item>
                        <a-descriptions-item label="下次保养到期时间">
                          <span :class="getValueClass(currentVehicle.maintenanceTimeStatus)">
                            {{ currentVehicle.lastMaintenanceExpireTime || '未计算' }}
                          </span>
                        </a-descriptions-item>
                        <a-descriptions-item label="下次保养到期提醒时间">
                          <span>{{ currentVehicle.lastMaintenanceRemindTime || '未计算' }}</span>
                        </a-descriptions-item>
                      </a-descriptions>
                    </a-card>
                  </a-col>
                  <a-col :span="12">
                    <a-card
                      title="保养里程"
                      size="small"
                      :bordered="false"
                      class="reminder-card"
                      :headStyle="getCardHeadStyle(currentVehicle.maintenanceMileageStatus)">
                      <a-descriptions bordered :column="1">
                        <a-descriptions-item label="当前总里程">
                          <span>{{ currentVehicle.totalMileage || 0 }} km</span>
                        </a-descriptions-item>
                        <a-descriptions-item label="保养里程周期">
                          <span>{{ currentVehicle.maintenanceMileageCycle || 5000 }} km</span>
                        </a-descriptions-item>
                        <a-descriptions-item label="下次保养到期里程">
                          <span :class="getValueClass(currentVehicle.maintenanceMileageStatus)">
                            {{ currentVehicle.lastMaintenanceExpireMileage || '未计算' }} km
                          </span>
                        </a-descriptions-item>
                        <a-descriptions-item label="下次保养到期里程提醒点">
                          <span>{{ currentVehicle.lastMaintenanceRemindMileage || '未计算' }} km</span>
                        </a-descriptions-item>
                      </a-descriptions>
                    </a-card>
                  </a-col>
                </a-row>
              </a-card>
            </a-col>
          </a-row>

          <a-row :gutter="16" style="margin-top: 16px">
            <a-col :span="24">
              <a-card
                title="年检信息"
                :bordered="false"
                class="detail-card">
                <a-alert
                  :message="currentVehicle.inspectionStatus || '未设置'"
                  :description="getInspectionDescription(currentVehicle)"
                  :type="getAlertType(currentVehicle.inspectionStatus)"
                  show-icon
                  style="margin-bottom: 16px"
                />

                <a-descriptions bordered :column="2">
                  <a-descriptions-item label="最近年检时间">
                    <span>{{ currentVehicle.lastInspectionTime || '未记录' }}</span>
                  </a-descriptions-item>
                  <a-descriptions-item label="年检周期">
                    <span>{{ currentVehicle.inspectionCycle || 12 }} 个月</span>
                  </a-descriptions-item>
                  <a-descriptions-item label="年检到期时间">
                    <span :class="getValueClass(currentVehicle.inspectionStatus)">
                      {{ currentVehicle.lastInspectionExpireTime || '未计算' }}
                    </span>
                  </a-descriptions-item>
                  <a-descriptions-item label="提醒时间">
                    <span>{{ currentVehicle.lastInspectionRemindTime || '未计算' }}</span>
                  </a-descriptions-item>
                </a-descriptions>
              </a-card>
            </a-col>
          </a-row>
        </div>
      </a-tab-pane>
    </a-tabs>

    <!-- 图片预览 -->
    <a-modal :visible="previewVisible" :footer="null" @cancel="previewVisible = false">
      <img alt="预览图片" style="width: 100%" :src="previewImage" >
    </a-modal>
  </a-modal>
</template>

<script>
import { ORG_NAME } from '@/store/mutation-types'
import * as baseApi from '@/api/system/base'
import moment from 'moment'
import { requestBuilder } from '@/utils/util'
import * as vehicleApi from '@/api/device/vehicle'
import { Empty } from 'ant-design-vue'
import Vue from 'vue'
const USER_ORG_NAME = Vue.ls.get(ORG_NAME)

export default {
  name: 'VehicleInfoModal',
  props: {
    showSubmitButton: {
      type: Boolean,
      default: true
    },
    visible: {
      type: Boolean,
      required: true
    },
    viewMode: {
      type: Boolean,
      default: false
    },
    editMode: {
      type: Boolean,
      default: false
    },
    vehicleData: {
      type: Object,
      default: () => ({})
    },
    depts: {
      type: Array,
      default: () => []
    },
    vehicleTypes: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      uploadAction: process.env.VUE_APP_API_BASE_URL + '/file/fileUpload',
      moment,
      simpleEmptyImage: Empty.PRESENTED_IMAGE_SIMPLE,
      confirmLoading: false,
      currentVehicle: {},
      fileList: [],
      tempFiles: [], // 暂存上传的文件
      previewVisible: false,
      previewImage: '',
      activeTabKey: 'basic',
      form: this.$form.createForm(this),
      deptPersons: [],
      vehicleMaintenances: [],
      vehicleInspections: [],
      vehicleMileages: [],
      vehicleRepairs: [],
      vehicleGases: [],
      tabLoading: false
    }
  },
  computed: {
    modalTitle () {
      return this.editMode ? `编辑车辆 - ${this.currentVehicle.plateNumber}` : '新增车辆'
    }
    // 保养记录、年检记录和里程记录已移到data中，通过API加载
  },
  watch: {
    visible (newVal) {
      if (newVal) {
        this.currentVehicle = { ...this.vehicleData }
        this.fileList = this.currentVehicle.vehicleImage ? [
          {
            uid: '-1',
            name: 'vehicle.png',
            status: 'done',
            url: this.currentVehicle.vehicleImage
          }
        ] : []
        this.activeTabKey = 'basic' // Reset tab on open
        this.$nextTick(() => {
          this.form.resetFields()
          // Set initial values after reset
          this.form.setFieldsValue({
            plateNumber: this.currentVehicle.plateNumber,
            vehicleType: this.currentVehicle.vehicleType,
            departmentId: this.currentVehicle.departmentSysId,
            managerId: this.currentVehicle.managerId,
            model: this.currentVehicle.model,
            vin: this.currentVehicle.vin,
            engineNumber: this.currentVehicle.engineNumber,
            totalMileage: this.currentVehicle.totalMileage,
            capacity: this.currentVehicle.capacity,
            vehicleStatus: this.currentVehicle.vehicleStatus || '1',
            maintenanceMonthCycle: this.currentVehicle.maintenanceMonthCycle || 12,
            maintenanceMileageCycle: this.currentVehicle.maintenanceMileageCycle || 5000,
            inspectionCycle: this.currentVehicle.inspectionCycle || 12,
            lastMaintenanceTime: this.currentVehicle.lastMaintenanceTime ? moment(this.currentVehicle.lastMaintenanceTime) : null,
            lastMaintenanceMileage: this.currentVehicle.lastMaintenanceMileage,
            lastInspectionTime: this.currentVehicle.lastInspectionTime ? moment(this.currentVehicle.lastInspectionTime) : null,
            remarks: this.currentVehicle.remarks,
            owner: this.currentVehicle.owner ? this.currentVehicle.owner : USER_ORG_NAME
          })
          if (this.currentVehicle.departmentSysId) {
            this.queryPersonByDept(this.currentVehicle.departmentSysId)
          }
        })
      }
    }
  },
  methods: {
    handleTabChange (key) {
      this.activeTabKey = key

      // 只有在编辑模式下且有车辆UUID时才加载数据
      if (this.editMode && this.currentVehicle.uuid) {
        if (key === 'maintenance') {
          this.loadMaintenanceRecords()
        } else if (key === 'inspection') {
          this.loadInspectionRecords()
        } else if (key === 'mileage') {
          this.loadMileageRecords()
        } else if (key === 'repair') {
          this.loadRepairRecords()
        } else if (key === 'gas') {
          this.loadGasRecords()
        }
      }
    },

    // 加载保养记录
    loadMaintenanceRecords () {
      this.tabLoading = true
      this.vehicleMaintenances = []

      vehicleApi.queryVehicleMaintenance(requestBuilder('', { vehicleUuid: this.currentVehicle.uuid, pageTag: false }))
        .then(res => {
          if (res.code === '0000') {
            this.vehicleMaintenances = res.result.data || []
          } else {
            this.$message.error('加载保养记录失败：' + res.message)
          }
        })
        .catch(err => {
          console.error('Load maintenance records error:', err)
          this.$message.error('加载保养记录失败')
        })
        .finally(() => {
          this.tabLoading = false
        })
    },

    // 加载年检记录
    loadInspectionRecords () {
      this.tabLoading = true
      this.vehicleInspections = []

      vehicleApi.queryVehicleInspection(requestBuilder('', { vehicleUuid: this.currentVehicle.uuid, pageTag: false }))
        .then(res => {
          if (res.code === '0000') {
            this.vehicleInspections = res.result.data || []
          } else {
            this.$message.error('加载年检记录失败：' + res.message)
          }
        })
        .catch(err => {
          console.error('Load inspection records error:', err)
          this.$message.error('加载年检记录失败')
        })
        .finally(() => {
          this.tabLoading = false
        })
    },

    // 加载里程记录
    loadMileageRecords () {
      this.tabLoading = true
      this.vehicleMileages = []

      vehicleApi.queryVehicleMileage(requestBuilder('', { vehicleUuid: this.currentVehicle.uuid, pageTag: false }))
        .then(res => {
          if (res.code === '0000') {
            this.vehicleMileages = res.result.data || []
          } else {
            this.$message.error('加载里程记录失败：' + res.message)
          }
        })
        .catch(err => {
          console.error('Load mileage records error:', err)
          this.$message.error('加载里程记录失败')
        })
        .finally(() => {
          this.tabLoading = false
        })
    },

    // 加载维修记录
    loadRepairRecords () {
      this.tabLoading = true
      this.vehicleRepairs = []

      vehicleApi.queryVehicleRepair(requestBuilder('', { vehicleUuid: this.currentVehicle.uuid, pageTag: false }))
        .then(res => {
          if (res.code === '0000') {
            this.vehicleRepairs = res.result.data || []
          } else {
            this.$message.error('加载维修记录失败：' + res.message)
          }
        })
        .catch(err => {
          console.error('Load repair records error:', err)
          this.$message.error('加载维修记录失败')
        })
        .finally(() => {
          this.tabLoading = false
        })
    },

    // 加载加油记录
    loadGasRecords () {
      this.tabLoading = true
      this.vehicleGases = []

      vehicleApi.queryVehicleGas(requestBuilder('', { vehicleUuid: this.currentVehicle.uuid, pageTag: false }))
        .then(res => {
          if (res.code === '0000') {
            this.vehicleGases = res.result.data || []
          } else {
            this.$message.error('加载加油记录失败：' + res.message)
          }
        })
        .catch(err => {
          console.error('Load gas records error:', err)
          this.$message.error('加载加油记录失败')
        })
        .finally(() => {
          this.tabLoading = false
        })
    },
    handleCancel () {
      this.$emit('close')
    },
    handleOk () {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.confirmLoading = true
          const param = values
          if (this.fileList.length > 0) {
            param.vehicleImage = this.fileList[0].url || (this.fileList[0].response && this.fileList[0].response.url)
          } else {
            param.vehicleImage = null
          }
          if (this.editMode && this.currentVehicle.uuid) {
            param.uuid = this.currentVehicle.uuid
          }
          const action = this.editMode ? 'update' : 'insert'
          vehicleApi.modifyVehicleInfo(requestBuilder(action, [param])).then(res => {
            if (res.code !== '0000') {
              this.$notification.error({
                message: '系统消息',
                description: `${this.editMode ? '更新' : '新增'}车辆信息失败！${res.message ? '：' + res.message : ''}`
              })
            } else {
              // 如果是新增数据保存成功后处理附件
              if (!this.editMode) {
                // 新增模式：获取返回的 uuid，用于附件上传
                const newUuid = res.result
                console.log('newUuid', newUuid)
                console.log('this.tempFiles', this.tempFiles)
                if (newUuid) {
                  // 更新当前车辆对象的 uuid
                  this.currentVehicle.uuid = newUuid
                  // 如果有暂存的文件，调用接口上传
                  if (this.tempFiles.length > 0) {
                    this.uploadTempFiles(newUuid)
                  }
                }
              }

              this.$notification.success({
                message: '系统消息',
                description: `${this.editMode ? '更新' : '新增'}车辆信息成功！`
              })
              this.$emit('close')
              this.$emit('refresh')
            }
          }).catch(err => {
            console.error('Save vehicle error:', err)
            this.$notification.error({
              message: '系统消息',
              description: `${this.editMode ? '更新' : '新增'}车辆信息失败！`
            })
          }).finally(() => {
            this.confirmLoading = false
          })
        }
      })
    },

    // 上传暂存的文件
    uploadTempFiles (uuid) {
      if (!uuid || this.tempFiles.length === 0) return

      // 这里可以调用文件上传接口，将暂存的文件上传到服务器
      // 例如：
      this.tempFiles.forEach(file => {
        if (file.response && file.response.url) {
          // 调用接口将文件与业务数据关联
          const fileData = {
            uuid: uuid,
            fileUrl: file.response.url,
            fileName: file.name,
            fileType: file.type
          }

          // 调用接口保存文件信息
          vehicleApi.saveVehicleFile(requestBuilder('insert', [fileData])).then(res => {
            if (res.code !== '0000') {
              this.$notification.error({
                message: '系统消息',
                description: '文件关联失败！'
              })
            }
          }).catch(err => {
            console.error('Save file error:', err)
          })
        }
      })

      // 清空暂存文件列表
      this.tempFiles = []
    },
    // Image handling methods
    handlePreview (file) {
      this.previewImage = file.url || file.thumbUrl
      this.previewVisible = true
    },
    handleChange ({ fileList, file }) {
      this.fileList = fileList
      console.log('file status:', file ? file.status : 'no file', file)

      // 如果文件上传成功，添加到暂存列表中
      if (file) {
        if (file.status === 'done' && file.response) {
          // 文件上传成功
          this.tempFiles.push(file)
          console.log('文件上传成功，添加到暂存列表', file)
        } else if (file.status === 'error') {
          // 文件上传失败
          console.error('文件上传失败', file)
          this.$message.error('文件上传失败，请重试')
        } else if (file.status === 'uploading') {
          // 文件正在上传
          console.log('文件正在上传', file)
        } else if (file.status === 'removed') {
          // 文件被移除
          console.log('文件被移除', file)
          // 从暂存列表中移除
          this.tempFiles = this.tempFiles.filter(item => item.uid !== file.uid)
        }
      }
    },
    beforeUpload (file) {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
      if (!isJpgOrPng) {
        this.$message.error('只能上传JPG或PNG格式的图片!')
        return false
      }
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        this.$message.error('图片大小不能超过2MB!')
        return false
      }
      return true // 允许上传
    },
    // Status and timeline color methods (copied from parent)
    getMaintenanceStatusColor (record) {
      const status = record.maintenanceStatus
      if (status === '已过期') {
        return 'red'
      } else if (status === '即将到期') {
        return 'orange'
      } else if (status === '正常') {
        return 'green'
      }
    },
    getMaintenanceStatusText (record) {
      return record.maintenanceStatus
    },
    getInspectionStatusColor (record) {
      const status = record.inspectionStatus
      if (status === '已过期') {
        return 'red'
      } else if (status === '即将到期') {
        return 'orange'
      } else if (status === '正常') {
        return 'green'
      }
    },
    getInspectionStatusText (record) {
      return record.inspectionStatus
    },
    getTimelineColor () {
      return 'blue'
    },

    // 获取状态对应的CSS类名
    getStatusClass (status) {
      if (!status) return 'status-unknown'

      switch (status) {
        case '正常':
          return 'status-normal'
        case '即将到期':
          return 'status-warning'
        case '已过期':
          return 'status-expired'
        default:
          return 'status-unknown'
      }
    },

    // 获取状态对应的图标
    getStatusIcon (status) {
      if (!status) return 'question-circle'

      switch (status) {
        case '正常':
          return 'check-circle'
        case '即将到期':
          return 'clock-circle'
        case '已过期':
          return 'exclamation-circle'
        default:
          return 'question-circle'
      }
    },

    // 获取Alert组件的类型
    getAlertType (status) {
      if (!status) return 'info'

      switch (status) {
        case '正常':
          return 'success'
        case '即将到期':
          return 'warning'
        case '已过期':
          return 'error'
        default:
          return 'info'
      }
    },

    // 获取卡片头部样式
    getCardHeadStyle (status) {
      if (!status) return {}

      const baseStyle = {
        borderTopLeftRadius: '4px',
        borderTopRightRadius: '4px',
        fontWeight: 'bold'
      }

      switch (status) {
        case '正常':
          return { ...baseStyle, backgroundColor: '#f6ffed', color: '#52c41a' }
        case '即将到期':
          return { ...baseStyle, backgroundColor: '#fffbe6', color: '#faad14' }
        case '已过期':
          return { ...baseStyle, backgroundColor: '#fff1f0', color: '#f5222d' }
        default:
          return { ...baseStyle, backgroundColor: '#f0f2f5', color: 'rgba(0, 0, 0, 0.65)' }
      }
    },

    // 获取值的CSS类名
    getValueClass (status) {
      if (!status) return ''

      switch (status) {
        case '正常':
          return 'value-normal'
        case '即将到期':
          return 'value-warning'
        case '已过期':
          return 'value-expired'
        default:
          return ''
      }
    },

    // 获取保养详细描述
    getMaintenanceDetailDescription (record) {
      if (!record || !record.maintenanceStatus) return '未设置保养信息'

      const maintenanceStatus = record.maintenanceStatus

      let description = ''

      if (maintenanceStatus.includes('已过期')) {
        if (record.maintenanceDetailStatus.includes('保养时间')) {
          description += `保养时间已过期，应在 ${record.lastMaintenanceExpireTime} 前完成保养。`
        } else {
          description += `${description ? ' ' : ''}保养里程已超出，当前里程 ${record.totalMileage} km，保养里程为 ${record.lastMaintenanceExpireMileage} km。`
        }
      } else if (maintenanceStatus.includes('即将到期')) {
        if (record.maintenanceDetailStatus.includes('保养时间')) {
          description += `保养时间即将到期，将在 ${record.lastMaintenanceExpireTime} 到期。`
        } else {
          description += `${description ? ' ' : ''}保养里程即将到期，当前里程 ${record.totalMileage} km，保养里程为 ${record.lastMaintenanceExpireMileage} km。`
        }
      } else if (maintenanceStatus.includes('正常')) {
        description = '车辆保养状态正常'
      } else {
        description = '未配置保养记录'
      }
      return description
    },

    // 获取年检描述
    getInspectionDescription (record) {
      if (!record || !record.inspectionStatus) return '未设置年检信息'

      switch (record.inspectionStatus) {
        case '正常':
          return `车辆年检状态正常，下次年检时间为 ${record.lastInspectionExpireTime || '--'}。`
        case '即将到期':
          return `车辆年检即将到期，请在 ${record.lastInspectionExpireTime} 前完成年检。`
        case '已过期':
          return `车辆年检已过期，应在 ${record.lastInspectionExpireTime} 前完成年检，请尽快处理！`
        default:
          return '未设置年检信息'
      }
    },
    deptChange (departmentSysId) {
      this.queryPersonByDept(departmentSysId)
      // 清楚表单的用户
      if (this.form) {
        this.form.setFieldsValue({ managerId: null })
      }
    },
    queryPersonByDept (departmentSysId) {
      baseApi.getCommboxById({ id: 'personByDeptSysId', sqlParams: { departmentSysId } }).then(res => {
        if (res.code === '0000') {
          this.deptPersons = res.result
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.ant-form-explain {
  display: none;
}
.pointNone {
  pointer-events: none;
}
.form-actions {
  display: flex;
  float: right;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
}

.upload-tip {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  margin-top: 8px;
}

.tab-content {
  padding: 16px 0;
  min-height: 300px;
}

.timeline-content {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 4px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.timeline-title {
  font-weight: bold;
}

.timeline-extra {
  color: #1890ff;
}

.timeline-body p {
  margin-bottom: 4px;
}

.reminder-card {
  height: 100%;
  background-color: #fafafa;
  border-radius: 4px;
}

.reminder-card .ant-card-head {
  background-color: #f0f2f5;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.reminder-card .ant-card-head-title {
  font-weight: 500;
  font-size: 14px;
}

.reminder-card .ant-descriptions-item-label,
.reminder-card .ant-descriptions-item-content {
  font-size: 13px
}

// 状态概览卡片样式
.status-overview-card {
  margin-bottom: 16px;
  background-color: #fafafa;
  border-radius: 8px;
}

.status-header {
  margin-bottom: 16px;

  h3 {
    font-size: 16px;
    font-weight: bold;
    margin: 0;
  }
}

.status-content {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  flex: 1;
  min-width: 200px;

  .status-icon {
    font-size: 32px;
    margin-right: 16px;
  }

  .status-info {
    flex: 1;

    .status-title {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
      margin-bottom: 4px;
    }

    .status-value {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 4px;
    }

    .status-detail {
      font-size: 12px;
    }
  }
}

// 状态样式
.status-normal {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;

  .status-icon {
    color: #52c41a;
  }

  .status-value {
    color: #52c41a;
  }
}

.status-warning {
  background-color: #fffbe6;
  border: 1px solid #ffe58f;

  .status-icon {
    color: #faad14;
  }

  .status-value {
    color: #faad14;
  }
}

.status-expired {
  background-color: #fff1f0;
  border: 1px solid #ffa39e;

  .status-icon {
    color: #f5222d;
  }

  .status-value {
    color: #f5222d;
  }
}

.status-unknown {
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;

  .status-icon {
    color: #bfbfbf;
  }
}

// 详情卡片样式
.detail-card {
  margin-bottom: 16px;
  border-radius: 8px;

  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
  }
}

// 值样式
.value-normal {
  color: #52c41a;
  font-weight: bold;
}

.value-warning {
  color: #faad14;
  font-weight: bold;
}

.value-expired {
  color: #f5222d;
  font-weight: bold;
}
</style>
