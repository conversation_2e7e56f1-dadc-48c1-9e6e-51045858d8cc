<template>
  <section>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="grid.gutter">
            <a-col
              :xl="grid.xl"
              :md="grid.md"
              :sm="grid.sm"
            >
              <a-form-item label="车牌号">
                <a-input v-model="queryParam.plateNumber" placeholder="请输入车牌号" allowClear @pressEnter="doSearch" />
              </a-form-item>
            </a-col>
            <a-col
              :xl="grid.xl"
              :md="grid.md"
              :sm="grid.sm"
            >
              <a-form-item label="车辆类别">
                <a-select
                  v-model="queryParam.vehicleType"
                  allowClear
                  optionFilterProp="label"
                  @change="doSearch"
                  showSearch
                >
                  <a-select-option
                    v-for="(item, index) in vehicleTypes"
                    :value="item.value"
                    :key="index"
                    :label="item.label"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :xl="grid.xl"
              :md="grid.md"
              :sm="grid.sm"
            >
              <a-form-item label="车辆状态">
                <a-select
                  v-model="queryParam.vehicleStatus"
                  placeholder="请选择状态"
                  allowClear
                  @change="doSearch">
                  <a-select-option
                    v-for="(item, index) in vehicleStatus"
                    :value="item.value"
                    :key="index"
                    :label="item.label"
                  >{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :xl="grid.xl2"
              :md="grid.md"
              :sm="grid.sm"
            >
              <a-form-item label="维修日期">
                <a-range-picker v-model="queryParam.dateRange" style="width: 100%" @change="doSearch" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="table-operator">
          <a-button type="primary" icon="search" @click="doSearch" v-action:query>
            查询
          </a-button>
          <a-button type="primary" icon="plus" @click="showRepairModal" v-action:add>
            新增维修记录
          </a-button>
          <a-button type="primary" v-action:import icon="upload" @click="showImportModal">
            批量导入
          </a-button>
          <a-button icon="reload" @click="resetSearch">
            重置
          </a-button>
          <a-dropdown>
            <a-menu slot="overlay">
              <a-menu-item key="1" @click="exportData" :loading="loading">
                <a-icon type="download" />导出数据
              </a-menu-item>
              <a-menu-item key="2" @click="showImportModal">
                <a-icon type="upload" />批量导入
              </a-menu-item>
            </a-menu>
            <a-button v-action:export :loading="loading">
              更多操作 <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
      </div>
    </a-card>

    <a-card :bordered="false" class="table-card">
      <div slot="title">
        <a-badge status="processing" text="维修记录列表" />
        <span class="table-total">共 {{ dataSource }} 条记录</span>
      </div>
      <s-table
        ref="table"
        :data="loadData"
        :scroll="scroll"
        :columns="columns"
        :customRow="rowClick"
        rowKey="uuid"
        :showPagination="showPagination"
        :pageSizeOptions="pageSizeOptions"
        :pageSize="defaultPageSize"
        :immediate="immediate"
      >
        <template slot="tradingDate" slot-scope="text">
          {{ text }}
        </template>

        <template slot="action" slot-scope="text, record">
          <a-tooltip title="编辑记录">
            <a-button type="default" shape="circle" size="small" @click="() => editRepair(record)" v-action:edit>
              <a-icon type="edit" style="color: #52c41a"/>
            </a-button>
          </a-tooltip>
          <a-tooltip title="删除记录">
            <a-button type="danger" shape="circle" size="small" @click="() => deleteRepair(record)" v-action:del>
              <a-icon type="delete" />
            </a-button>
          </a-tooltip>
        </template>
        <template slot="vehicleStatus" slot-scope="text,record">
          <a-tag :color="getStatusColor(record)">{{ text }}</a-tag>
        </template>
      </s-table>
    </a-card>

    <!-- 使用新的维修编辑模态框组件 -->
    <repair-edit-modal
      :visible="repairModalVisible"
      :edit-mode="editMode"
      :showSubmitButton="showSubmitButton"
      :repair-data="currentRepair"
      :vehicles="vehicles"
      @refresh="doSearch"
      @close="repairModalVisible = false"
    />

    <!-- 批量导入模态框 -->
    <import-excel-modal
      title="批量导入维修记录"
      :visible="importModalVisible"
      :showDownTemplate="true"
      @cancel="importModalVisible = false"
      @refresh="doSearch"
      :uploadApi="vehicleApi.uploadTemplateVehicleRepair"
      :downloadTemplateApi="vehicleApi.exportTemplateVehicleRepair"
    />
  </section>
</template>

<script>
import moment from 'moment'
import { STable } from '@/components'
import RepairEditModal from './modules/RepairEditModal.vue'
import ImportExcelModal from '@/views/system/components/ImportExcelModal.vue'
import * as vehicleApi from '@/api/device/vehicle'
import * as baseApi from '@/api/system/base'
import { requestBuilder, deepUpdate } from '@/utils/util'

export default {
  name: 'VehicleRepair',
  components: {
    STable,
    RepairEditModal,
    ImportExcelModal
  },
  data () {
    return {
      moment,
      loading: false,
      confirmLoading: false,
      editMode: false,

      // 排版设置
      grid: {
        xl: 4,
        xl2: 6,
        md: 8,
        sm: 24,
        gutter: 16
      },

      // 表格设置
      scroll: {
        x: '100%',
        y: 'calc(70vh)',
        scrollToFirstRowOnChange: false
      },
      showSubmitButton: true,
      vehicles: [],
      dataSource: [],
      pageSizeOptions: ['10', '15', '20', '25', '30'],
      defaultPageSize: 10,
      showPagination: true,
      immediate: true,
      vehicleTypes: [],
      vehicleStatus: [],
      depts: [],

      // 查询参数
      queryParam: {
        plateNumber: '',
        departmentSysId: '',
        vehicleType: '',
        vehicleStatus: '1',
        startDate: null,
        endDate: null,
        dateRange: []
      },

      // 表格列定义
      columns: [
        { title: '车牌号', dataIndex: 'plateNumber', width: 120, align: 'center' },
        { title: '车辆类别', dataIndex: 'vehicleTypeName', width: 120, align: 'center' },
        {
          title: '车辆状态',
          dataIndex: 'vehicleStatusName',
          align: 'center',
          width: 100,
          scopedSlots: { customRender: 'vehicleStatus' }
        },
        { title: '型号', dataIndex: 'model', width: 120, align: 'center' },
        // { title: '部门', dataIndex: 'departmentName', width: 120, align: 'center' },
        { title: '维修日期', dataIndex: 'repairDate', width: 140, align: 'center', sorter: true },
        { title: '维修内容', dataIndex: 'repairContent', width: 200, align: 'center' },
        { title: '维修金额(元)', dataIndex: 'repairAmount', width: 180, align: 'center', sorter: true },
        { title: '维修单位', dataIndex: 'repairCompany', width: 140, align: 'center', scopedSlots: { customRender: 'tradingDate' } },
        { title: '里程(km)', dataIndex: 'mileage', width: 180, align: 'center', sorter: true },
        { title: '操作', key: 'action', width: 100, scopedSlots: { customRender: 'action' }, align: 'center' }
      ],

      // 模态框状态
      repairModalVisible: false,
      importModalVisible: false,

      // 当前编辑的数据
      currentRepair: {},
      vehicleApi,
      rowClick: record => ({
        on: {
          dblclick: () => {
            // 双击时消单击事件
            clearTimeout(this.clickTimer)
            this.selectedRows = [record]
            this.selectedRowKeys = [record.uuid]
            this.$refs.table.triggerSelect(this.selectedRowKeys, this.selectedRows)
            // 编辑
            this.editMode = true
            this.currentRepair = { ...record }
            this.repairModalVisible = true
          }
        }
      }),
      // 数据加载方法
      loadData: parameter => {
        const queryParam = {
          ...this.queryParam
        }

        // 处理日期范围
        if (queryParam.dateRange && queryParam.dateRange.length === 2) {
          queryParam.startDate = moment(queryParam.dateRange[0]).format('YYYY-MM-DD')
          queryParam.endDate = moment(queryParam.dateRange[1]).format('YYYY-MM-DD')
          delete queryParam.dateRange
        }
        const param = requestBuilder(
          '',
          deepUpdate(
            {
              activity: 'Y',
              plateNumber: '',
              departmentSysId: '',
              vehicleType: '',
              vehicleStatus: '',
              startDate: null,
              endDate: null
            },
            queryParam
          ),
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder
        )

        return vehicleApi.queryVehicleRepair(param).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '查询失败！'
            })
            return Promise.reject(res)
          }
          this.dataSource = res.result.totalCount
          return res.result
        })
      }
    }
  },
  created () {
    // 初始化选项获取
    this.initOptions()
  },
  methods: {
    initOptions () {
      baseApi.getCommboxById({ id: 'dept', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.depts = res.result
        }
      })
      baseApi.getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'vehicle_status' } }).then(res => {
        if (res.code === '0000') {
          this.vehicleStatus = res.result
        }
      })
      baseApi.getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'vehicle_type' } }).then(res => {
        if (res.code === '0000') {
          this.vehicleTypes = res.result
        }
      })
      this.queryAllVehicle()
    },
    // 维修管理方法
    showRepairModal () {
      this.editMode = false
      this.currentRepair = {}
      this.showSubmitButton = true // 新增时始终显示保存按钮
      this.repairModalVisible = true
    },

    editRepair (record) {
      this.editMode = true
      this.currentRepair = { ...record }
      this.showSubmitButton = true // 编辑时也显示保存按钮
      this.repairModalVisible = true
    },

    deleteRepair (record) {
      this.$confirm({
        title: '确定要删除该维修记录吗?',
        content: '删除后无法恢复。',
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => {
          const param = requestBuilder('delete', [record])
          vehicleApi.modifyVehicleRepair(param).then(res => {
            if (res.code === '0000') {
              this.$message.success('车辆维修记录删除成功！')
              this.doSearch()
            } else {
              this.$message.error('车辆维修记录删除失败！')
            }
          })
        }
      })
    },

    // 搜索方法
    doSearch () {
      this.$refs.table.refresh()
      this.queryAllVehicle()
    },

    resetSearch () {
      this.queryParam = {
        plateNumber: '',
        vehicleType: '',
        vehicleStatus: '1',
        dateRange: []
      }
      this.doSearch()
    },

    // 显示导入模态框
    showImportModal () {
      this.importModalVisible = true
    },

    // 导出数据
    exportData () {
      this.loading = true
      // this.queryParam.year = this.queryParam.year ? this.queryParam.year.format('YYYY') : null
      const param = {
        ...this.queryParam
      }
      vehicleApi.exportVehicleRepair(requestBuilder('', param)).then(res => {
        if (res && res.data && res.headers['content-disposition']) {
          const link = document.createElement('a')
          const url = window.URL.createObjectURL(res.data)
          const contentDisposition = res.headers['content-disposition']
          let filename = contentDisposition.replace(/(.*;)?filename=([^;]+).*/i, '$2')
          try {
          // filename = decodeURIComponent(filename)
            filename = decodeURIComponent(escape(filename))
          } catch (e) {
          // console.log(e)
          }
          document.body.appendChild(link)
          link.style.display = 'none'
          link.download = filename
          link.href = url
          link.click()
          link.remove()
          window.URL.revokeObjectURL(url)
        } else {
          this.$notification.error({
            message: '系统消息',
            description: '下载失败！'
          })
        }
      }).finally(() => {
        this.loading = false
      })
    },
    getStatusColor (record) {
      switch (record.vehicleStatus) {
        case '1':
          return 'green'
        case '0':
          return 'red'
      }
    },

    queryAllVehicle () {
      // 查询所有车辆信息
      vehicleApi.queryVehicleInfo(requestBuilder('', { activity: 'Y', pageTag: false }, null, null)).then(res => {
        if (res.code === '0000') {
          this.vehicles = res.result.data
        }
      })
    }
  }
}
</script>

<style scoped>

.table-card {
  margin-bottom: 24px;
}

.table-total {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}
</style>
