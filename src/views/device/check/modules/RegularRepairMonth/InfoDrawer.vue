<template>
  <section>
    <a-drawer
      class="no-transform"
      width="1200"
      :title="title"
      :visible="visible"
      :mask="false"
      :maskClosable="false"
      :getContainer="false"
      @close="doClose()"
    >
      <a-spin :spinning="loading">
        <a-form
          :form="form"
          layout="horizontal"
          labelAlign="right"
          class="customize-label"
          style="position: relative; z-index: 0"
        >
          <div class="pane-container">
            <div class="pane-content">
              <a-row>
                <a-col
                  :xl="queryGrid.xl"
                  :md="queryGrid.md"
                  :sm="queryGrid.sm"
                >
                  <a-form-item label="设备大类:">
                    <a-select
                      v-decorator="['equipmentDType', {
                        rules:[{ required: false, message: '请选择设备大类' }]
                      }]"
                      allowClear
                      @change="getSbxls()"
                    >
                      <a-select-option
                        v-for="(item, index) in queryOptions.sbdls"
                        :disabled="item.disabled"
                        :value="item.value"
                        :key="index"
                      >{{ item.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="queryGrid.xl"
                  :md="queryGrid.md"
                  :sm="queryGrid.sm"
                >
                  <a-form-item label="设备小类:">
                    <a-select
                      v-decorator="['equipmentXType', {
                        rules:[{ required: false, message: '请选择设备小类' }]
                      }]"
                      allowClear
                    >
                      <a-select-option
                        v-for="(item, index) in queryOptions.sbxls"
                        :disabled="item.disabled"
                        :value="item.value"
                        :key="index"
                      >{{ item.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="queryGrid.xl"
                  :md="queryGrid.md"
                  :sm="queryGrid.sm"
                >
                  <a-form-item label="设备名称:">
                    <a-input
                      v-decorator="['equipmentName', {
                        rules:[{ required: false,message: '请输入' }]
                      }]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="queryGrid.xl"
                  :md="queryGrid.md"
                  :sm="queryGrid.sm"
                >
                  <a-form-item label="单位:">
                    <a-input
                      v-decorator="['unit', {
                        rules:[{ required: false,message: '请输入' }]
                      }]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="queryGrid.xl"
                  :md="queryGrid.md"
                  :sm="queryGrid.sm"
                >
                  <a-form-item label="数量:">
                    <a-input
                      v-decorator="['num', {
                        rules:[{ required: false,message: '请输入' }]
                      }]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="queryGrid.xl"
                  :md="queryGrid.md"
                  :sm="queryGrid.sm"
                >
                  <a-form-item label="预计金额(万元):">
                    <a-input
                      v-decorator="['estimatedAmount', {
                        rules:[{ required: false,message: '请输入' }]
                      }]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="queryGrid.xl"
                  :md="queryGrid.md"
                  :sm="queryGrid.sm"
                >
                  <a-form-item label="联络者:">
                    <a-input
                      v-decorator="['contactPerson', {
                        rules:[{ required: false,message: '请输入' }]
                      }]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="queryGrid.xl"
                  :md="queryGrid.md"
                  :sm="queryGrid.sm"
                >
                  <a-form-item label="首报时间:">
                    <a-date-picker v-decorator="['reportDate']" />
                  </a-form-item>
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="queryGrid.xl"
                  :md="queryGrid.md"
                  :sm="queryGrid.sm"
                >
                  <a-form-item label="进度安排:">
                    <a-input
                      v-decorator="['schedule', {
                        rules:[{ required: false,message: '请输入' }]
                      }]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="queryGrid.xl"
                  :md="queryGrid.md"
                  :sm="queryGrid.sm"
                >
                  <a-form-item label="施工单位:">
                    <a-input
                      v-decorator="['constructionOrgId', {
                        rules:[{ required: false,message: '请输入' }]
                      }]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="queryGrid.xl"
                  :md="queryGrid.md"
                  :sm="queryGrid.sm"
                >
                  <a-form-item label="立项依据:">
                    <a-input
                      v-decorator="['projectBasis', {
                        rules:[{ required: false,message: '请输入' }]
                      }]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="queryGrid.xl"
                  :md="queryGrid.md"
                  :sm="queryGrid.sm"
                >
                  <a-form-item label="是否需要生产配合停机:">
                    <a-select
                      v-decorator="['isNeedShutdown', {
                        rules:[{ required: false, message: '请选择是否需要生产配合停机' }]
                      }]"
                      allowClear
                    >
                      <a-select-option
                        v-for="(item, index) in isNeedShutdownArray"
                        :disabled="item.disabled"
                        :value="item.label"
                        :key="index"
                      >{{ item.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="queryGrid.xl"
                  :md="queryGrid.md"
                  :sm="queryGrid.sm"
                >
                  <a-form-item label="选择月份">
                    <a-month-picker
                      allowClear
                      v-decorator="['planMonth']"
                      placeholder
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-form>
      </a-spin>
      <div class="drawer-footer">
        <div class="footer-fixed">
          <a-button @click="doClose()">取消</a-button>
          <a-button
            type="primary"
            :loading="loading"
            @click="doModifyeData"
          >保存</a-button>
        </div>
      </div>
    </a-drawer>
  </section>
</template>

<script>
// import * as baseApi from '@/api/system/base'
import moment from 'moment'
import * as baseApi from '@/api/system/base'
import * as regularRepairApi from '@/api/device/regularRepair'
import { requestBuilder } from '@/utils/util'
import { mapGetters } from 'vuex'

export default {
  name: 'InfoDrawer',
  components: {
  },
  props: {
    getApproveId: {
      type: Function,
      default: function () {}
    },
    drawerClosed: {
      type: Function,
      default: function () {}
    },
    dataChanged: {
      type: Function,
      default: function () {}
    }
  },
  data () {
    return {
      // 下拉框
      status: [],
      companyArr: [],
      // 网格布局
      queryGrid: {
        xl: 6,
        md: 6,
        sm: 24,
        gutter: 10
      },
      queryOptions: {
        sbdls: [],
        sbxls: [],
        allSbxls: []
      },
      timer: {},
      record: {},
      title: '',
      action: '',
      form: this.$form.createForm(this),
      visible: false,
      loading: false,
      isNeedShutdownArray: [
        {
          label: '是'
        },
        {
          label: '否'
        }
      ]
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  created () {
    // 初始化选项获取
    this.initOptions()
  },
  methods: {
    // 下拉框数据获取
    initOptions () {
      // 填报单位
      // baseApi.getCommboxById({ id: 'org' }).then(res => {
      //   if (res.code === '0000') {
      //     for (const item of res.result) {
      //       this.companyArr.push({
      //         value: item.value,
      //         label: item.shortName || item.label
      //       })
      //     }
      //   }
      // })
      // 设备大类/小类
      baseApi.getTreeById({ id: 'basCodeByClassIdAndOrgId', sqlParams: { codeClassId: 'asset_sb_fl' } }).then(res => {
        if (res.code === '0000') {
          for (const item of res.result) {
            // 设施大类
            this.queryOptions.sbdls.push({
              ...item,
              children: undefined
            })
            // 设施小类
            if (item.children) {
              for (const node of item.children) {
                this.queryOptions.sbxls.push({
                  ...node,
                  children: undefined
                })
              }
            }
            this.queryOptions.allSbxls = this.queryOptions.sbxls
          }
        }
      })
    },
    // 设备信息新增
    doAdd () {
      this.doOpen('insert', {})
    },
    // 设备信息修改
    doEdit (records) {
      this.doOpen('update', records[0])
    },
    // 设备信息删除
    doDel (records) {
      this.doDeleteData(records)
    },
    // 打开 弹框
    doOpen (action, record) {
      // 根据 action 判断
      switch (action) {
        case 'insert': {
          this.add = true
          this.title = '新增设备委外点检数据'
          this.action = 'insert'
          break
        }
        case 'update': {
          this.add = false
          this.title = '修改设备委外点检数据'
          this.action = 'update'
          this.record = record
          // 缓存累计
          break
        }
      }
      // 显示修改弹框
      this.visible = true
      // 重置 form表单
      this.form.resetFields()
      // 初始化form表单
      this.form.setFieldsValue({
        ...record,
        reportDate: record.reportDate ? moment(record.reportDate) : '',
        planMonth: record.planMonth ? moment(record.planMonth) : ''
      })
    },
    // 关闭 弹框
    doClose () {
      this.timer = {}
      this.record = {}
      this.title = ''
      this.action = ''
      this.form.resetFields()
      this.visible = false
      this.loading = false
      this.add = false
      this.drawerClosed()
      this.dataChanged()
      this.queryOptions.sbxls = this.queryOptions.allSbxls
    },
    // 修改 信息
    doModifyeData (refresh) {
      const toSubmit = (action, param, notice) => {
        return regularRepairApi.insertRegularRepairMonthInfo(requestBuilder(action, param)).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || notice.error
            })
            return Promise.reject(res)
          }
          this.$notification.success({
            message: '系统消息',
            description: notice.success
          })
          return Promise.resolve(param[0])
        })
      }
      return new Promise((resolve, reject) => {
        if (this.action === 'insert') {
          this.form.validateFields((errors, values) => {
            if (errors) {
              return
            }
            const action = this.action
            const records = []
            const notice = {
              error: '新增失败！',
              success: '新增成功！'
            }
            records.push({
              ...values,
              activity: 'Y',
              nature: '委外'
            })
            console.log(records)
            toSubmit(action, records, notice)
              .then(record => {
                resolve(record)
                this.doClose()
              })
              .finally(() => {
                this.loading = false
              })
            this.loading = true
          })
        }
        if (this.action === 'update') {
          this.form.validateFields((errors, values) => {
            if (errors) {
              return
            }
            this.loading = true
            const action = this.action
            const record = this.record
            const records = [
              {
                ...values,
                reportDate: values.reportDate ? values.reportDate.format('YYYY-MM-DD') : '',
                planMonth: values.planMonth ? values.planMonth.format('YYYY-MM') : '',
                uuid: record.uuid
              }
            ]
            console.log(records)
            const notice = {
              error: '修改失败！',
              success: '修改成功！'
            }
            toSubmit(action, records, notice)
              .then(() => {
                resolve(record)
                this.doClose()
              })
              .finally(() => {
                this.loading = false
              })
          })
        }
      })
    },
    getSbxls () {
      setTimeout(() => {
        console.log(this.form.getFieldValue('equipmentDType'))
        this.form.setFieldsValue({
          equipmentXType: ''
        })
        this.queryOptions.sbxls = []
        baseApi.getTreeById({ id: 'basCodeByClassIdAndOrgId', sqlParams: { codeClassId: 'asset_sb_fl' } }).then(res => {
          if (res.code === '0000') {
            for (const item of res.result) {
              // 设施小类
              if (item.value === this.form.getFieldValue('equipmentDType')) {
                for (const node of item.children) {
                  this.queryOptions.sbxls.push({
                    ...node,
                    children: undefined
                  })
                }
              }
            }
          }
        })
      })
    },
    // 删除 信息
    doDeleteData (records) {
      const param = requestBuilder('delete', records)
      regularRepairApi.deleteRegularRepairInfo(param).then(res => {
        if (res.code !== '0000') {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '删除失败！'
          })
          return Promise.reject(res)
        }
        this.$notification.success({
          message: '系统消息',
          description: '删除成功！'
        })
        this.dataChanged(true)
      })
    }
  }
}
</script>

<style lang="less" scoped>
// 禁用字体
::v-deep {
  .ant-input-disabled {
    color: rgba(0, 0, 0, 0.6);
  }
  .ant-select-disabled {
    .ant-select-selection {
      color: rgba(0, 0, 0, 0.6);
    }
    .ant-select-selection--multiple .ant-select-selection__choice {
      color: rgba(0, 0, 0, 0.6);
    }
  }
}
// 抽屉样式
::v-deep {
  .ant-drawer-left.ant-drawer-open,
  .ant-drawer-right.ant-drawer-open {
    &.no-mask {
      width: 100%;
    }
  }
}
.customize-label {
  ::v-deep {
    .ant-form-item {
      & > .ant-form-item-label {
        width: 155px;
        padding-bottom: 20px;
      }
      & > .ant-form-item-control-wrapper {
        width: calc(100% - 75px);
      }
    }
    .col-forms > .ant-form-item {
      & > .ant-form-item-label {
        width: 145px;
        margin-bottom: 20px;
      }
      & > .ant-form-item-control-wrapper {
        width: calc(100% - 148px);
      }
    }
  }
}
.flex-row {
  display: flex;
  flex-flow: row wrap;
  border-top: dashed 1px #cfcfcf;
  & > .flex-row-col {
    // min-height: 180px;
    position: relative;
    width: 33.33333333%;
    padding: 8px 15px 8px 0;
    &:nth-child(3n + 3) {
      padding-right: 2px;
    }
    &:not(:nth-child(3n + 1)) {
      padding-left: 8px;
    }
    &:not(:nth-child(3n + 3))::after {
      content: '';
      position: absolute;
      width: 0;
      height: calc(100% - 20px);
      top: 10px;
      right: 0;
      border-right: dashed 1px #cfcfcf;
    }
    &::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 0;
      top: auto;
      right: 0;
      bottom: -1px;
      border-bottom: dashed 1px #cfcfcf;
    }
    & > .col-header {
      height: 30px;
      padding-left: 12px;
      line-height: 30px;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
}
.flex-footer {
  padding-top: 5px;
  border-top: dashed 1px #cfcfcf;
  background-color: #ffffff;
  position: relative;
  z-index: 1;
}
.upload {
  ::v-deep {
    .ant-upload {
      float: left;
      padding-left: 10px;
      margin-bottom: 3px;
    }
    .ant-upload-list {
      width: 100%;
      clear: both;
      border-top: dashed 1px #cfcfcf;
    }
  }
}
// form 抽屉框
section {
  ::v-deep {
    .ant-drawer {
      .ant-drawer-header-no-title {
        .ant-drawer-close {
          display: none;
        }
        & + .ant-drawer-body {
          padding: 0 20px 10px;
          overflow: visible;
          .ant-tabs {
            margin-top: 10px;
            overflow: visible;
            & > .ant-tabs-bar {
              background-color: #ffffff;
              position: sticky;
              padding-top: 10px;
              z-index: 10;
              top: 0;
            }
            .ant-upload {
              float: left;
              padding-left: 5px;
              margin-bottom: 1px;
            }
            .ant-upload-list {
              width: 100%;
              clear: both;
              border-top: dashed 1px #cfcfcf;
            }
          }
        }
      }
    }
  }
}
.pane-container {
  width: 100%;
  margin-bottom: 30px;
  .pane-header {
    display: flex;
    width: 100%;
    height: 30px;
    padding-left: 5px;
    margin-bottom: 10px;
    line-height: 30px;
    font-size: 13px;
    color: #303133;
    border-bottom: dashed 1px #cfcfcf;
    & > .pane-header-title {
      flex: 1 1 auto;
      padding-left: 12px;
      // line-height: 32px;
      line-height: 80px;
    }
  }
  .pane-content {
    padding: 0 3px;
    margin-bottom: 10px;
  }
}
</style>
