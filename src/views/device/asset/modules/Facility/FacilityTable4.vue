<template>
  <section>
    <s-table
      ref="table"
      :data="loadData"
      :scroll="scroll"
      :columns="columns"
      :customRow="rowClick"
      :rowSelection="rowSelection"
      :clearSelection="clearSelection"
      :showPagination="showPagination"
      :pageSizeOptions="pageSizeOptions"
      :pageSize="defaultPageSize"
      :rowClassName="rowClassName"
      :immediate="immediate"
      :bordered="bordered"
      rowKey="uuid"
    />
    <a-drawer
      width="100vw"
      class="no-transform"
      :visible="visible"
      :mask="false"
      :maskClosable="false"
      :getContainer="false"
      @close="doClose()"
    >
      <!-- 关闭图标 -->
      <a-icon
        type="close"
        style="position: absolute; top: 13px; right: 10px; z-index: 25; cursor: pointer;"
        @click="doClose"
      />

      <a-tabs
        v-model="tabsKey"
        tabPosition="top"
      >
        <a-tab-pane
          key="1"
          tab="详细信息"
          :forceRender="true"
        >
          <s-form
            ref="form"
            :grid="grid"
            :watch="watch"
            :attrs="attrs"
            :groups="groups"
            :options="options"
            :disabled="disabled"
            :readonly="readonly"
            :spinning="loading"
          />
        </a-tab-pane>
        <a-tab-pane
          key="2"
          tab="履历册"
          :forceRender="true"
        >
          <resume-table
            :assetUuid="assetSsUuid"
            assetType="asset_ss"
          />
        </a-tab-pane>
      </a-tabs>
      <div class="drawer-footer">
        <div class="footer-fixed">
          <a-button @click="doClose()">取消</a-button>
          <a-button
            v-if="!readonly && !disabled"
            type="primary"
            :loading="loading"
            @click="doSave"
          >保存</a-button>
        </div>
      </div>
    </a-drawer>
  </section>
</template>

<script>
import { STable, SForm } from '@/components'
import { requestBuilder, deepUpdate, takeTreeByKey } from '@/utils/util'
import * as assetSsApi from '@/api/device/assetSs'
import ResumeTable from '../Resume/InfoTable'

// 导入表单配置
import FormConfig from './FacilityTable4.js'

export default {
  name: 'FacilityTable4',
  components: {
    STable,
    SForm,
    ResumeTable
  },
  mixins: [FormConfig],
  props: {
    queryOptions: {
      type: Object,
      default: function () {
        return {
          assetSsDl: [],
          assetSsZl: []
        }
      }
    },
    selectedNodes: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  data () {
    return {
      assetSsUuid: '',
      // 台账参数
      queryParam: {},
      // 表格配置
      columns: [
        {
          title: '设施名称',
          dataIndex: 'assetSsName',
          ellipsis: true,
          sorter: true,
          width: 120
        },
        {
          title: '所在地',
          dataIndex: 'address',
          ellipsis: true,
          sorter: true,
          width: 150
        },
        {
          title: '用途',
          dataIndex: 'useFor',
          ellipsis: true,
          sorter: true,
          width: 120
        },
        {
          title: '建成投产日期',
          dataIndex: 'completeDate',
          ellipsis: true,
          sorter: true,
          width: 120
        },
        {
          title: '使用单位',
          dataIndex: 'useUnits',
          ellipsis: true,
          sorter: true,
          width: 120
        },
        {
          title: '设计单位',
          dataIndex: 'designUnit',
          ellipsis: true,
          sorter: true,
          width: 120
        },
        {
          title: '施工单位',
          dataIndex: 'constructionUnit',
          ellipsis: true,
          sorter: true,
          width: 120
        },
        {
          title: '资产编号',
          dataIndex: 'finAssetNum',
          ellipsis: true,
          sorter: true,
          width: 120
        },
        {
          title: '资产原值',
          dataIndex: 'initialValue',
          ellipsis: true,
          sorter: true,
          width: 120
        },
        {
          title: '目前技术等级',
          dataIndex: 'techGrade',
          ellipsis: true,
          sorter: true,
          width: 120
        },
        {
          title: '竣工验收质量等级',
          dataIndex: 'completedQuaGrade',
          ellipsis: true,
          sorter: true,
          width: 120
        },
        {
          title: '地基基础',
          dataIndex: 'djjc',
          ellipsis: true,
          sorter: true,
          width: 120
        },
        {
          title: '面层结构',
          dataIndex: 'surfaceStructure',
          ellipsis: true,
          sorter: true,
          width: 120
        }
      ],
      rowSelection: {
        type: 'checkbox',
        onSelect: this.onSelectHandle,
        onChange: this.onSelectChange
      },
      scroll: {
        x: 'max-content',
        scrollToFirstRowOnChange: false
      },
      dataSource: [],
      selectedRows: [],
      selectedRowKeys: [],
      pageSizeOptions: ['10', '15', '20', '25', '30'],
      defaultPageSize: 20,
      clearSelection: true,
      showPagination: true,
      clickTimer: null,
      immediate: false,
      bordered: false,
      loadData: parameter => {
        const queryParam = {
          ...this.queryParam,
          completeStartDate: this.queryParam.completeDate.length === 2 ? this.queryParam.completeDate[0].format('YYYY-MM-DD') : '',
          completeEndDate: this.queryParam.completeDate.length === 2 ? this.queryParam.completeDate[1].format('YYYY-MM-DD') : ''
        }
        const param = requestBuilder(
          '',
          deepUpdate(
            {
              activity: 'Y',
              assetSsDlList: [],
              assetSsZlList: [],
              assetSsNum: '',
              assetSsName: '',
              completeStartDate: '',
              completeEndDate: '',
              ssdw: '',
              address: '',
              roleType: 'device'
            },
            queryParam
          ),
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder
        )
        return assetSsApi.queryFacility(param).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '查询失败！'
            })
            return Promise.reject(res)
          }
          this.dataSource = [...res.result.data] || []
          return res.result
        })
      },
      rowClick: record => ({
        on: {
          click: () => {
            // 双击时消单击事件
            clearTimeout(this.clickTimer)
            this.selectedRows = [record]
            this.selectedRowKeys = [record.uuid]
            this.$refs.table.triggerSelect(this.selectedRowKeys, this.selectedRows)
            this.doEdit([record])
          },
          oldClick: () => {
            // 限制频繁触发单击事件
            clearTimeout(this.clickTimer)
            this.clickTimer = setTimeout(() => {
              // 在单击效果为单选情况
              if (this.rowSelection.type === 'radio') {
                // 编辑模式下
                if (this.visible) {
                  this.doEdit([record])
                }
              }
              // 在单击效果为多选情况
              if (this.rowSelection.type === 'checkbox') {
                const index = this.selectedRowKeys.indexOf(record.uuid)
                if (index > -1) {
                  this.selectedRowKeys.splice(index, 1)
                  this.selectedRows.splice(index, 1)
                } else {
                  this.selectedRowKeys.push(record.uuid)
                  this.selectedRows.push(record)
                }
                const dataSource = this.dataSource.filter(item => {
                  return this.selectedRowKeys.includes(item.uuid)
                })
                this.selectedRows = dataSource.map(item => item)
                this.selectedRowKeys = dataSource.map(item => item.uuid)
                this.$refs.table.triggerSelect(this.selectedRowKeys, this.selectedRows)
              }
            }, 300)
          }
        }
      }),
      rowClassName: () => {
        return 'cursor-pointer'
      },
      // 标签页
      tabsKey: '1',
      // 台账抽屉框
      title: '',
      action: '',
      visible: false,
      loading: false,
      isAdd: true
    }
  },
  created () {},
  methods: {
    // 获取下拉框选项文本
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || key
    },
    // 表格手动勾选
    onSelectHandle (record, selected, selectedRows) {
      this.visible && this.doEdit([record])
    },
    // 表格勾选更改
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRows = selectedRows
      this.selectedRowKeys = selectedRowKeys
    },
    // 表格加载中
    doReady (state) {
      this.$refs.table.ready(state)
    },
    // 表格数据清空
    doClear () {
      this.$refs.table.clear()
    },
    // 表格数据刷新
    doSearch (hidden) {
      if (hidden === true) {
        this.doClose()
      }
      this.$refs.table.refresh(true)
    },
    // 信息新增
    doAdd () {
      this.selectedRows = []
      this.selectedRowKeys = []
      this.$refs.table.triggerSelect([], [])
      this.$refs.table.rowSelection.type = 'radio'
      this.doOpen('insert')
    },
    // 信息修改
    doEdit (records) {
      if (records) {
        this.selectedRows = records
        this.selectedRowKeys = records.map(item => item.uuid)
        this.$refs.table.triggerSelect(this.selectedRowKeys, this.selectedRows)
      }
      this.$refs.table.rowSelection.type = 'radio'
      this.doOpen('update')
    },
    // 信息删除
    doDel () {
      if (this.visible) {
        this.$message.error('新增/修改模式下，不可进行删除操作！')
        return
      }
      if (this.selectedRows.length === 0) {
        this.$message.error('请选择所要删除的设施！')
        return
      }
      this.$confirm({
        title: '系统提示',
        content: '确定删除吗？',
        onOk: () => {
          assetSsApi.deleteFacility(requestBuilder('delete', [...this.selectedRows])).then(res => {
            if (res.code !== '0000') {
              this.$notification.error({
                message: '系统消息',
                description: res.message || '删除失败！'
              })
              return Promise.reject(res)
            }
            this.$notification.success({
              message: '系统消息',
              description: '删除成功！'
            })
            this.doSearch(true)
          })
        }
      })
    },
    // 打开信息弹框
    doOpen (action) {
      let assetSsDl = ''
      let assetSsZl = ''
      const { selectedRows = [] } = this
      const [record = {}] = selectedRows
      switch (action) {
        case 'insert': {
          if (this.selectedNodes.length > 0) {
            // 判断子选项个数
            const nodeCount = this.selectedNodes.reduce((total, node) => {
              return total + (node.level === 3 ? 1 : 0)
            }, 0)
            // 获取第一个元素
            const firstNode = this.selectedNodes[0]
            // 获取一个子元素
            const childNode = this.selectedNodes.find(item => item.level === 3)

            // 设施大类/小类
            if (nodeCount === 1) {
              assetSsDl = childNode.parentNode.value
              assetSsZl = childNode.value
            } else {
              switch (firstNode.level) {
                case 2: {
                  assetSsDl = firstNode.value
                  break
                }
                case 3: {
                  assetSsDl = firstNode.parentNode.value
                  break
                }
              }
            }
          }
          this.isAdd = true
          this.title = '新增'
          this.action = 'insert'
          break
        }
        case 'update': {
          this.isAdd = false
          this.title = '修改'
          this.action = 'update'
          this.assetSsUuid = record.uuid
          break
        }
      }
      // 处理表单
      const base = {
        assetSsDl: assetSsDl,
        assetSsZl: assetSsZl
      }
      this.$refs.form.resetFields()
      this.$refs.form.setFieldsValue(record, this.isAdd ? base : {})
      // 显示弹框
      this.visible = true
    },
    // 关闭信息弹框
    doClose () {
      this.title = ''
      this.action = ''
      this.isAdd = false
      this.loading = false
      this.visible = false
      this.assetSsUuid = ''
      this.$refs.form.resetFields()
      this.$refs.table.triggerSelect([], [])
      this.$refs.table.rowSelection.type = 'checkbox'
    },
    // 保存信息
    doSave () {
      const toSubmit = (action, param, notice) => {
        return assetSsApi.modifyFacility(requestBuilder(action, param)).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || notice.error
            })
            return Promise.reject(res)
          }
          this.$notification.success({
            message: '系统消息',
            description: notice.success
          })
        })
      }
      if (this.action === 'insert') {
        this.$refs.form.validateFields(errors => {
          if (errors) {
            return
          }
          const action = this.action
          const notice = {
            error: '新增失败！',
            success: '新增成功！'
          }
          const records = [
            {
              ...this.$refs.form.getFieldsValue()
            }
          ]
          toSubmit(action, records, notice)
            .then(() => {
              this.doSearch(true)
              this.loading = false
            })
            .catch(() => {
              this.loading = false
            })
          this.loading = true
        })
      }
      if (this.action === 'update') {
        this.$refs.form.validateFields(errors => {
          if (errors) {
            return
          }
          const action = this.action
          const records = []
          const notice = {
            error: '修改失败！',
            success: '修改成功！'
          }
          for (const record of this.selectedRows) {
            records.push({
              ...record,
              ...this.$refs.form.getFieldsValue()
            })
          }
          toSubmit(action, records, notice)
            .then(() => {
              this.doSearch(true)
              this.loading = false
            })
            .catch(() => {
              this.loading = false
            })
          this.loading = true
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
// form 抽屉框
section {
  ::v-deep {
    .ant-drawer {
      .ant-drawer-header-no-title {
        .ant-drawer-close {
          display: none;
        }
        & + .ant-drawer-body {
          padding: 0 20px 10px;
          overflow: visible;
          .ant-tabs {
            margin-top: 10px;
            overflow: visible;
            & > .ant-tabs-bar {
              background-color: #ffffff;
              position: sticky;
              padding-top: 10px;
              z-index: 10;
              top: 0;
            }
            .ant-upload {
              float: left;
              padding-left: 20px;
              margin-bottom: 3px;
            }
            .ant-upload-list {
              width: calc(100% - 40px);
              clear: both;
              margin-left: 20px;
              border-top: solid 1px #e0e0e0;
            }
          }
        }
      }
    }
  }
}
section {
  ::v-deep {
    .s-form-container {
      .ant-form-inline,
      .ant-form-horizontal {
        .ant-form-item {
          & > .ant-form-item-label {
            width: 115px;
          }
        }
      }
    }
  }
}
</style>
