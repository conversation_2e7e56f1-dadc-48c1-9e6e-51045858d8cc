// helper: validator and extender
import { validator, extender } from '@/components/Form/helper'

import Vue from 'vue'
import { ORG_ID } from '@/store/mutation-types'
import { ORG_ID_JYS, ORG_ID_TMG } from '@/store/variable-types'
const NOW_ORG_ID = Vue.ls.get(ORG_ID)
const IS_JYS_ENV = NOW_ORG_ID === ORG_ID_JYS
const IS_TMG_ENV = NOW_ORG_ID === ORG_ID_TMG

// 基本信息
const FORM_BASE = [
  {
    type: 'AGroup',
    slot: 'title1',
    field: 'title1',
    title: '基本信息',
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'createByName',
    field: 'createByName',
    label: '创建人',
    decorator: {},
    attrs: {
      disabled: true
    }
  },
  {
    type: 'AInput',
    slot: 'createDate',
    field: 'createDate',
    label: '创建日期',
    decorator: {},
    attrs: {
      disabled: true
    }
  },
  {
    type: 'AInput',
    slot: 'selfNumber',
    field: 'selfNumber',
    label: '系统编码',
    decorator: {},
    attrs: {
      disabled: true
    }
  },
  {
    type: 'AInput',
    slot: 'phone',
    field: 'phone',
    label: '联系电话',
    decorator: {},
    attrs: {
      disabled: true
    }
  }
  // {
  //   type: 'ASelect',
  //   slot: 'responsibleGroup',
  //   field: 'responsibleGroup',
  //   label: '负责组',
  //   decorator: {
  //     rules: [{ type: 'string', required: true, message: '请选择负责组' }]
  //   },
  //   attrs: {
  //     placeholder: '请选择负责组...',
  //     showSearch: true,
  //     optionFilterProp: 'label'
  //   }
  // }
]

// 详细信息
const FORM_DETAIL = [
  {
    type: 'AGroup',
    slot: 'title2',
    field: 'title2',
    title: '详细信息',
    attrs: {},
    grid: {}
  },
  {
    type: 'ASelect',
    slot: 'sbdl',
    field: 'sbdl',
    label: '设备大类',
    decorator: {
      rules: [{ type: 'string', required: true, message: '请选择设备大类' }]
    },
    attrs: {
      placeholder: '请选择设备大类...',
      showSearch: true,
      optionFilterProp: 'label'
    }
  },
  {
    type: 'ASelect',
    slot: 'sbxl',
    field: 'sbxl',
    label: '设备子类',
    decorator: {
      rules: [{ type: 'string', required: true, message: '请选择设备子类' }]
    },
    attrs: {
      placeholder: '请选择设备子类...',
      showSearch: true,
      optionFilterProp: 'label'
    }
  },
  {
    type: 'AInput',
    slot: 'assetSbNum',
    field: 'assetSbNum',
    label: '设备编号',
    decorator: {
      rules: [{ type: 'string', required: false, message: '请输入设备编号' }]
    },
    attrs: {
      placeholder: '请输入设备编号...'
    }
  },
  {
    type: 'ASelect',
    slot: 'useDept',
    field: 'useDept',
    label: '所属部门',
    decorator: {
      rules: [{ type: 'string', required: true, message: '请选择' }]
    },
    attrs: {
      placeholder: '请选择使用单位...'
    }
  },
  {
    type: 'ASelect',
    slot: 'assetStatus',
    field: 'assetStatus',
    label: '设备状态',
    decorator: {},
    attrs: {
      // disabled: true
    }
  },
  {
    type: 'AMonthPicker',
    slot: 'statusChangeDate',
    field: 'statusChangeDate',
    label: '状态变更时间',
    decorator: {
      rules: [{ required: false, message: '状态变更时间' }]
    },
    attrs: {
      allowClear: true,
      placeholder: '状态变更时间...'
    }
  },
  {
    type: 'AInput',
    slot: 'cqdw',
    field: 'cqdw',
    label: '产权单位',
    decorator: {
      rules: [{ type: 'string', required: false }]
    },
    attrs: {
      placeholder: '请输入产权单位...'
    }
  },
  {
    type: 'AInput',
    slot: 'source',
    field: 'source',
    label: '来源',
    decorator: {
      rules: [{ required: false, message: '请输入来源' }]
    },
    attrs: {
      placeholder: '请输入来源...'
    }
  },
  ...(IS_JYS_ENV ? [
  ] : [{
    type: 'AMonthPicker',
    slot: 'purdate',
    field: 'purdate',
    label: '购入日期',
    decorator: {
      rules: [{ required: false, message: '请选择购入日期' }]
    },
    attrs: {
      allowClear: true,
      placeholder: '请选择购置日期...'
    }
  }]),
  {
    type: 'AInput',
    slot: 'originValue',
    field: 'originValue',
    label: '原值',
    decorator: {},
    attrs: {
      suffix: '万元',
      placeholder: '请输入原值...'
    }
  },
  {
    type: 'AInput',
    slot: 'createNum',
    field: 'createNum',
    label: '出厂编号',
    decorator: {},
    attrs: {
      placeholder: '请输入出厂编号...'
    }
  },
  {
    type: 'AInput',
    slot: 'model',
    field: 'model',
    label: '规格型号',
    decorator: {},
    attrs: {
      placeholder: '请输入规格型号...'
    }
  },
  {
    type: 'AInput',
    slot: 'sccj',
    field: 'sccj',
    label: '生产厂家',
    decorator: {
      rules: [{ type: 'string', required: false, message: '请输入生产厂家' }]
    },
    attrs: {
      placeholder: '请输入生产厂家...'
    }
  },
  {
    type: 'AMonthPicker',
    slot: 'produceDate',
    field: 'produceDate',
    label: '制造年月',
    decorator: {},
    attrs: {
      allowClear: true,
      placeholder: '请选择制造年月...'
    }
  },
  {
    type: 'ASelect',
    slot: 'dkpp',
    field: 'dkpp',
    label: '电控品牌',
    decorator: {},
    attrs: {
      placeholder: '请选择电控品牌...'
    }
  },
  {
    type: 'ASelect',
    slot: 'isZdsb',
    field: 'isZdsb',
    label: '是否重点设备',
    decorator: {},
    attrs: {
      placeholder: '请选择是否重点设备...'
    }
  },
  {
    type: 'ASelect',
    slot: 'isSmartcar',
    field: 'isSmartcar',
    label: '是否智能集卡',
    decorator: {},
    attrs: {
      placeholder: '请选择是否智能集卡...'
    }
  },
  {
    type: 'ASelect',
    slot: 'isRemote',
    field: 'isRemote',
    label: '是否远控',
    decorator: {},
    attrs: {
      placeholder: '请选择是否远控...'
    }
  },
  {
    type: 'ASelect',
    slot: 'isSpecialEquipment',
    field: 'isSpecialEquipment',
    label: '是否特种设备',
    decorator: {},
    attrs: {
      placeholder: '请选择...'
    }
  },
  {
    type: 'ASelect',
    slot: 'isFireEquipment',
    field: 'isFireEquipment',
    label: '是否消防设备',
    decorator: {},
    attrs: {
      placeholder: '请选择...'
    }
  },
  {
    type: 'AInput',
    slot: 'numberPlate',
    field: 'numberPlate',
    label: '车牌号',
    decorator: {},
    attrs: {
      placeholder: '请输入车牌号...'
    }
  },
  {
    type: 'ATextarea',
    slot: 'jszb',
    field: 'jszb',
    label: '技术指标',
    decorator: {},
    attrs: {
      rows: 3
    },
    grid: {
      xs: 8,
      newline: true
    }
  },
  {
    type: 'ATextarea',
    slot: 'remark',
    field: 'remark',
    label: '备注',
    decorator: {},
    attrs: {
      rows: 3
    },
    grid: {
      xs: 8
    }
  }
]

// 更多信息
const FORM_MORE = [
  {
    type: 'AGroup',
    slot: 'title3',
    field: 'title3',
    title: '更多信息',
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'selfNumber',
    field: 'selfNumber',
    label: '自编号',
    decorator: {},
    attrs: {
      placeholder: '请输入自编号...'
    }
  },
  {
    type: 'AInput',
    slot: 'brand',
    field: 'brand',
    label: '品牌',
    decorator: {},
    attrs: {
      placeholder: '请输入品牌...'
    }
  },
  {
    type: 'AInput',
    slot: 'model',
    field: 'model',
    label: '规格型号',
    decorator: {},
    attrs: {
      placeholder: '请输入规格型号...'
    }
  },
  {
    type: 'AMonthPicker',
    slot: 'purdate',
    field: 'purdate',
    label: '购入日期',
    decorator: {
      rules: [{ required: true, message: '请选择购入日期' }]
    },
    attrs: {
      allowClear: true,
      placeholder: '请选择购置日期...'
    }
  },
  {
    type: 'AInput',
    slot: 'description',
    field: 'description',
    label: '名称',
    decorator: {},
    attrs: {
      placeholder: '请输入名称...'
    }
  },
  {
    type: 'AInput',
    slot: 'vin',
    field: 'vin',
    label: '车架号',
    decorator: {},
    attrs: {
      placeholder: '请输入车架号...'
    }
  },
  {
    type: 'ASelect',
    slot: 'vehicleOwnership',
    field: 'vehicleOwnership',
    label: '作业性质',
    decorator: {},
    attrs: {
      placeholder: '请选择作业性质...'
    }
  },
  {
    type: 'ASelect',
    slot: 'fuelType',
    field: 'fuelType',
    label: '燃料类型',
    decorator: {},
    attrs: {
      placeholder: '请选择燃料类型...'
    }
  },
  {
    type: 'ASelect',
    slot: 'drivingForm',
    field: 'drivingForm',
    label: '驱动形式',
    decorator: {},
    attrs: {
      placeholder: '请选择驱动形式...'
    }
  },
  {
    type: 'AInput',
    slot: 'engineNumber',
    field: 'engineNumber',
    label: '发动机编号',
    decorator: {},
    attrs: {
      placeholder: '请输入发动机编号...'
    }
  },
  {
    type: 'AInput',
    slot: 'engineType',
    field: 'engineType',
    label: '发动机型号',
    decorator: {},
    attrs: {
      placeholder: '请输入发动机型号...'
    }
  },
  {
    type: 'AInput',
    slot: 'gearboxType',
    field: 'gearboxType',
    label: '变速箱型号',
    decorator: {},
    attrs: {
      placeholder: '请选择变速箱型号...'
    }
  },
  {
    type: 'AInput',
    slot: 'axleBrand',
    field: 'axleBrand',
    label: '车轴品牌',
    decorator: {},
    attrs: {
      placeholder: '请输入车轴品牌...'
    }
  },
  {
    type: 'ASelect',
    slot: 'axleNumber',
    field: 'axleNumber',
    label: '车轴数',
    decorator: {},
    attrs: {
      placeholder: '请选择车轴数...'
    }
  },
  {
    type: 'ASelect',
    slot: 'fixedPosition',
    field: 'fixedPosition',
    label: '固定部位',
    decorator: {},
    attrs: {
      placeholder: '请选择固定部位...'
    }
  },
  {
    type: 'ASelect',
    slot: 'businessType',
    field: 'businessType',
    label: '业务类型',
    decorator: {},
    attrs: {
      placeholder: '请选择业务类型...'
    }
  },
  {
    type: 'ADatePicker',
    slot: 'useDate',
    field: 'useDate',
    label: '投用日期',
    decorator: {},
    attrs: {
      format: 'YYYY-MM-DD',
      placeholder: '请选择投用日期'
    }
  },
  {
    type: 'ADatePicker',
    slot: 'acquiringDate',
    field: 'acquiringDate',
    label: '收购日期',
    decorator: {},
    attrs: {
      format: 'YYYY-MM-DD',
      placeholder: '请选择收购日期'
    }
  },
  {
    type: 'ADatePicker',
    slot: 'scrapDate',
    field: 'scrapDate',
    label: '报废日期',
    decorator: {},
    attrs: {
      format: 'YYYY-MM-DD',
      placeholder: '请选择报废日期'
    }
  },
  {
    type: 'AInput',
    slot: 'durableYears',
    field: 'durableYears',
    label: '使用年限',
    decorator: {
      rules: [
        validator.number({
          type: 'string',
          message: '使用年限为必填项',
          validator: '格式为纯数字类型'
        })
      ]
    },
    attrs: {
      suffix: '年',
      placeholder: '请输入使用年限...'
    }
  },
  {
    type: 'AInput',
    slot: 'environmentalInfo',
    field: 'environmentalInfo',
    label: '环保信息',
    decorator: {},
    attrs: {
      placeholder: '例如：国五'
    },
    grid: {
    }
  }
]

// 运行工况
const FORM_TMG = [
  {
    type: 'AGroup',
    slot: 'title4',
    field: 'title4',
    title: '运行工况',
    attrs: {},
    grid: {}
  },
  {
    type: 'ASelect',
    slot: 'operatingConditions',
    field: 'operatingConditions',
    label: '运行工况',
    decorator: {
      rules: [{ required: true, message: '请选择' }]
    },
    attrs: {
      placeholder: '请选择...'
    }
  }
]
// 导出配置
export default {
  data () {
    // 处理
    const groups = [
      ...FORM_BASE,
      ...FORM_DETAIL,
      ...(IS_JYS_ENV ? FORM_MORE : []),
      ...(IS_TMG_ENV ? FORM_TMG : [])
    ]

    return {
      // 布局
      grid: {
        gutter: 10,
        xs: 4
      },

      // 属性
      attrs: {
        labelAlign: 'right'
      },

      // 监听
      watch: {
        onValuesChange (props, values) {
          // 创建更改区
          const changed = {}

          if (!IS_JYS_ENV) {
          // 监听 设备编号
            if (values.hasOwnProperty('assetSbNum')) {
              if (values.assetSbNum) {
                Object.assign(changed, {
                  selfNumber: values.assetSbNum
                })
              } else {
                Object.assign(changed, {
                  selfNumber: ''
                })
              }
            }
          }

          // 监听 设备大类
          if (values.sbdl) {
            const sbdl = values.sbdl
            const sbxl = this.getFieldValue('sbxl')
            const sbxls = this.options.sbxl.selectOptions
            if (sbdl && !sbxls.some(item => sbxl === item.value && sbdl === item.valueParent)) {
              Object.assign(changed, {
                sbxl: ''
              })
            }
          }

          // 是否进行赋值 (一次性赋值，提升性能)
          if (Object.keys(changed).length > 0) {
            this.setFieldsValue(changed)
          }
        }
      },

      // 配置
      groups: [
        ...extender.groups(groups, (group, index, groups) => {
          // 完善 groups
          if (group.field === 'assetStatus') {
            group.attrs.disabled = () => this.isDisabled('update')
          }
        })
      ],

      // 扩展
      options: {
        ...extender.options(groups, (group, index, groups, region) => {
          // 默认值
          const option = {}

          // 下拉框处理
          if (['ASelect'].includes(group.type)) {
            Object.assign(option, {
              selectOptions: []
            })
          }

          // 设备子类
          if (group.field === 'sbxl') {
            Object.assign(option, {
              selectOptionsRender (options, { Utils, form }) {
                return options.filter(opt => {
                  const sbdl = form.getFieldValue('sbdl')
                  return !sbdl || opt.valueParent === sbdl
                })
              }
            })
          }

          // 更多信息处理
          if (region.field === 'title3' && group.type !== 'AGroup') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const sbxl = form.getFieldValue('sbxl')
                const TYPE_LARGE = '004012'
                if (sbxl !== '') {
                  if (sbxl !== TYPE_LARGE) {
                    return [
                      'selfNumber',
                      'brand',
                      'fuelType',
                      'drivingForm',
                      'purdate',
                      'vin',
                      'engineNumber',
                      'engineType',
                      'gearboxType',
                      'useDate',
                      'acquiringDate',
                      'scrapDate',
                      'environmentalInfo'
                    ].includes(item.field)
                  } else if (sbxl === TYPE_LARGE) {
                    return [
                      'selfNumber',
                      'description',
                      'brand',
                      'purdate',
                      'model',
                      'durableYears'
                    ].includes(item.field)
                  }
                } else {
                  return false
                }
              }
            })
          }

          return option
        })
      },

      // 禁用 (已从props接受)
      // disabled: false,

      // 只读 (已从props接受)
      // readonly: false,

      // 查询中
      spinning: false
    }
  }
}
