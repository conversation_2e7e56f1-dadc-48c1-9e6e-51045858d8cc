
<template>
  <section>
    <!-- 表单信息 -->
    <s-form
      ref="form"
      :grid="grid"
      :watch="watch"
      :attrs="attrs"
      :groups="groups"
      :options="options"
      :disabled="disabled"
      :readonly="readonly"
      :spinning="spinning"
      class="form"
    >
      <!-- 年检信息 -->
      <template #after>
        <div class="form-item">
          <div class="form-header">
            <div style="display: flex; width: 100%; max-width: 1000px; position: relative">
              <div class="titles">
                <div style="width: 15%; padding-left: 13px">年检类型</div>
                <div style="width: 14%; padding-left: 13px">品牌</div>
                <div style="width: 14%; padding-left: 13px">编号</div>
                <div style="width: 14%; padding-left: 13px">型号</div>
                <div style="width: 15%; padding-left: 13px">年检日期</div>
                <div style="width: 14%; padding-left: 13px">年检周期</div>
                <div style="width: 14%; padding-left: 13px">提醒期限</div>
              </div>
              <div class="buttons">
                <a-button
                  v-if="!disabled && !readonly"
                  type="link"
                  class="button"
                  @click="doAsEntitiesAdd()"
                >新增</a-button
                >
              </div>
            </div>
          </div>
          <div
            v-if="assetSbAsEntities.length > 0"
            style="width: 100%; max-width: 1000px; margin-bottom: 10px; position: relative"
            class="form-bodyer"
          >
            <div v-for="(item, index) of assetSbAsEntities" :key="index" class="form-every">
              <div class="inputs">
                <div style="width: 15%; padding: 0 8px">
                  <a-select v-model="item.codeId" :disabled="item.action !== 'insert'" style="width: 100%">
                    <a-select-option v-for="(item2, index2) in annualInspections" :value="item2.value" :key="index2">{{
                      item2.label
                    }}</a-select-option>
                  </a-select>
                </div>
                <div style="width: 14%; padding: 0 8px">
                  <a-select v-model="item.brand" :disabled="item.action !== 'insert' || item.codeId !== '0001'" style="width: 100%">
                    <a-select-option v-for="(item2, index2) in cylinderBrands" :value="item2.value" :key="index2">{{
                      item2.label
                    }}</a-select-option>
                  </a-select>
                </div>
                <div style="width: 14%; padding: 0 8px">
                  <a-input v-model="item.number" :disabled="item.action !== 'insert'" style="width: 100%" />
                </div>
                <div style="width: 14%; padding: 0 8px">
                  <a-input v-model="item.model" :disabled="item.action !== 'insert'" style="width: 100%" />
                </div>
                <div style="width: 15%; padding: 0 8px">
                  <a-date-picker
                    v-model="item.annualInspectionDate"
                    :disabled="item.action !== 'insert'"
                    style="width: 100%"
                  />
                </div>
                <div style="width: 14%; padding: 0 8px">
                  <a-input v-model="item.period" :disabled="item.action !== 'insert'" style="width: 100%" suffix="天" />
                </div>
                <div style="width: 14%; padding: 0 8px">
                  <a-input
                    v-model="item.remindDeadline"
                    :disabled="item.action !== 'insert'"
                    style="width: 100%"
                    suffix="天"
                  />
                </div>
              </div>
              <div class="buttons">
                <a-button
                  v-if="!disabled && !readonly"
                  type="link"
                  class="button"
                  style="color: #f34d4d"
                  @click="doAsEntitiesDel(item)"
                >移除</a-button
                >
              </div>
            </div>
          </div>
          <div v-if="!assetSbAsEntities.length" style="margin-bottom: 10px" class="form-bodyer">
            <div class="form-empty">暂无年检信息...</div>
          </div>
        </div>
      </template>
    </s-form>
  </section>
</template>

<script>
import Vue from 'vue'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { SForm } from '@/components'
import { ORG_ID } from '@/store/mutation-types'
import { requestBuilder } from '@/utils/util'
import * as baseApi from '@/api/system/base'
import * as assetSbApi from '@/api/device/assetSb'

// 导入表单配置
import FormConfig from './InfoForm.js'

// 启用状态
const ASSET_STATUS_ENABLE = 'change_type1'

// 操作人 userNo
const USER_ORG_ID = Vue.ls.get(ORG_ID)

export default {
  name: 'InfoForm',
  components: {
    SForm
  },
  mixins: [FormConfig],
  props: {
    isDisabled: {
      type: Function,
      default: function () {}
    },
    assetSbUuid: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    isAdd: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      // 操作类型
      action: '',
      // 年检信息
      assetSbAsEntities: [],
      lastAssetSbAsEntities: [],
      annualInspections: [],
      cylinderBrands: [],
      yn: [ {
        label: '是',
        value: 'Y'
      },
      {
        label: '否',
        value: 'N'
      }]
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  created () {
    this.queryOptions()
  },
  watch: {
    loading: {
      handler (loading) {
        this.spinning = !!loading
      },
      immediate: true
    },
    spinning: {
      handler (spinning) {
        this.$emit('update:loading', !!spinning)
      }
    },
    assetSbUuid: {
      handler (spinning) {
        this.queryAsEntities()
      }
    }
  },
  methods: {
    // 查询下拉框选项
    queryOptions () {
      // 大类/小类
      baseApi.getTreeById({ id: 'basCodeByClassIdAndOrgId', sqlParams: { codeClassId: 'asset_sb_fl' } }).then(res => {
        if (res.code === '0000') {
          if (this.options.sbdl) {
            for (const item of res.result) {
              // 设施大类
              this.options.sbdl.selectOptions.push({
                ...item,
                children: undefined
              })
              // 设施小类
              if (item.children) {
                if (this.options.sbxl) {
                  for (const node of item.children) {
                    this.options.sbxl.selectOptions.push({
                      ...node,
                      children: undefined
                    })
                  }
                }
              }
            }
          }
        }
      })
      // 负责组
      // baseApi.getCommboxById({ id: 'basCodeByClassIdAndOrgId', sqlParams: { codeClassId: 'responsible_group' } }).then(res => {
      //   if (res.code === '0000') {
      //     this.options.responsibleGroup.selectOptions = res.result
      //   }
      // })
      // 设备状态
      baseApi.getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'asset_status' } }).then(res => {
        if (res.code === '0000') {
          if (this.options.assetStatus) {
            for (const item of res.result) {
              this.options.assetStatus.selectOptions.push({
                ...item
              })
            }
          }
        }
      })
      if (USER_ORG_ID === '1.100.117') {
        baseApi.getCommboxById({ id: 'basCodeByClassIdAndOrgId', sqlParams: { codeClassId: 'check_group_num' } }).then(res => {
          if (res.code === '0000') {
            this.options.useDept.selectOptions = res.result
          }
        })
      } else {
        baseApi.getCommboxById({ id: 'dept', sqlParams: { isAll: '1' } }).then(res => {
          if (res.code === '0000') {
            this.options.useDept.selectOptions = res.result
          }
        })
      }
      // 电控品牌
      baseApi.getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'dkpp' } }).then(res => {
        if (res.code === '0000') {
          if (this.options.dkpp) {
            for (const item of res.result) {
              this.options.dkpp.selectOptions.push({
                ...item
              })
            }
          }
        }
      })
      if (USER_ORG_ID === '1.109.100') {
        // 运行工况
        baseApi.getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'operating_conditions' } }).then(res => {
          if (res.code === '0000') {
            if (this.options.operatingConditions) {
              for (const item of res.result) {
                this.options.operatingConditions.selectOptions.push({
                  ...item
                })
              }
            }
          }
        })
      }
      // 是否重点设备
      this.options.isZdsb.selectOptions = this.yn
      this.options.isSmartcar.selectOptions = this.yn
      this.options.isRemote.selectOptions = this.yn
      this.options.isSpecialEquipment.selectOptions = this.yn
      this.options.isFireEquipment.selectOptions = this.yn
      // 作业性质
      baseApi
        .getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'asset_vehicle_ownership' } })
        .then(res => {
          if (res.code === '0000') {
            if (this.options.vehicleOwnership) {
              for (const item of res.result) {
                this.options.vehicleOwnership.selectOptions.push({
                  ...item
                })
              }
            }
          }
        })
      // 燃料类型
      baseApi.getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'asset_fuel_type' } }).then(res => {
        if (res.code === '0000') {
          if (this.options.fuelType) {
            for (const item of res.result) {
              this.options.fuelType.selectOptions.push({
                ...item
              })
            }
          }
        }
      })
      // 驱动形式
      baseApi.getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'asset_drive_form' } }).then(res => {
        if (res.code === '0000') {
          if (this.options.drivingForm) {
            for (const item of res.result) {
              this.options.drivingForm.selectOptions.push({
                ...item
              })
            }
          }
        }
      })
      // 车轴数
      baseApi
        .getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'asset_axle_number' } })
        .then(res => {
          if (res.code === '0000') {
            if (this.options.axleNumber) {
              for (const item of res.result) {
                this.options.axleNumber.selectOptions.push({
                  ...item
                })
              }
            }
          }
        })
      // 固定部位
      baseApi
        .getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'asset_fixed_position' } })
        .then(res => {
          if (res.code === '0000') {
            if (this.options.fixedPosition) {
              for (const item of res.result) {
                this.options.fixedPosition.selectOptions.push({
                  ...item
                })
              }
            }
          }
        })
      // 业务类型
      baseApi
        .getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'asset_business_type' } })
        .then(res => {
          if (res.code === '0000') {
            if (this.options.businessType) {
              for (const item of res.result) {
                this.options.businessType.selectOptions.push({
                  ...item
                })
              }
            }
          }
        })
      // 气瓶品牌
      baseApi
        .getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'asset_gas_cylinders_brand' } })
        .then(res => {
          this.cylinderBrands = res.result
        })
      // 年检类型
      baseApi
        .getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'asset_annual_inspection' } })
        .then(res => {
          if (res.code === '0000') {
            for (const item of res.result) {
              this.annualInspections.push({
                ...item
              })
            }
          }
        })
    },
    // 查询年检信息
    queryAsEntities () {
      if (!this.assetSbUuid) {
        this.assetSbAsEntities = []
        this.lastAssetSbAsEntities = []
      }
      if (!this.spinning && this.assetSbUuid) {
        // 调用接口
        assetSbApi
          .queryAssetSbAs(requestBuilder('', { assetSbUuid: this.assetSbUuid, assetObject: 'asset_sb' }))
          .then(res => {
            if (res.code !== '0000') {
              this.$notification.error({
                message: '系统消息',
                description: res.message || '年检信息查询失败失败！'
              })
              return Promise.reject(res)
            }
            this.assetSbAsEntities = res.result.map(item => {
              return {
                ...item,
                annualInspectionDate: item.annualInspectionDate
                  ? moment(item.annualInspectionDate, 'YYYY-MM-DD')
                  : null
              }
            })
            this.lastAssetSbAsEntities = res.result.map(item => {
              return {
                ...item,
                annualInspectionDate: item.annualInspectionDate
                  ? moment(item.annualInspectionDate, 'YYYY-MM-DD')
                  : null
              }
            })
          })
          .finally(() => {
            this.spinning = false
          })

        // 查询中
        this.spinning = true
      }
    },
    // 表单重置
    resetFields () {
      return this.$refs.form.resetFields(...arguments)
    },
    // 表单赋值
    setFieldsValue () {
      let count = 3
      const repeat = () => {
        this.$refs.form.setFieldsValue(...arguments)
        this.$nextTick(() => --count > 0 && repeat())
      }
      repeat()
    },
    // 表单取值
    getFieldsValue () {
      return this.$refs.form.getFieldsValue(...arguments)
    },
    // 表单取值
    getFieldValue () {
      return this.$refs.form.getFieldValue(...arguments)
    },
    // 表单校验
    validateFields () {
      return this.$refs.form.validateFields(...arguments)
    },
    // 表单渲染处理
    doCreateForm (action = '', record = {}) {
      let base = {}
      switch (action) {
        case 'insert': {
          base = {
            isZdsb: 'N',
            isSmartcar: 'N',
            isRemote: 'N',
            assetStatus: ASSET_STATUS_ENABLE,
            createByName: this.userInfo.personName,
            createDate: moment().format('YYYY-MM-DD HH:mm:ss'),
            phone: this.userInfo.mobilePhone
          }
          this.action = action
          this.assetSbAsEntities = []
          this.lastAssetSbAsEntities = []
          break
        }
        case 'update': {
          if (!record.assetSbAsEntities) {
            record.assetSbAsEntities = []
          }
          this.action = action
          break
        }
      }
      this.resetFields()
      this.setFieldsValue(record, base)
    },
    // 表单销毁处理
    doDestroyForm () {
      this.resetFields()
      this.lastAssetSbAsEntities = []
      this.assetSbAsEntities = []
      this.action = ''
    },
    // 新增年检信息
    doAsEntitiesAdd () {
      this.assetSbAsEntities.push({
        assetSbUuid: this.assetSbUuid,
        annualInspectionDate: null,
        remindDeadline: '',
        period: '',
        codeId: '',
        brand: '',
        number: '',
        model: '',
        action: 'insert'
      })
    },
    // 删除年检信息
    doAsEntitiesDel (record) {
      this.assetSbAsEntities = this.assetSbAsEntities.filter(item => item !== record)
    },
    // 删除表单数据
    doDeleteData (records) {
      return assetSbApi.deleteEquipment(requestBuilder('delete', [...records])).then(res => {
        if (res.code !== '0000') {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '删除失败！'
          })
          return Promise.reject(res)
        }
        this.$notification.success({
          message: '系统消息',
          description: '删除成功！'
        })
      })
    },
    // 校验表单数据
    doValidateData (record) {
      return new Promise((resolve, reject) => {
        this.validateFields(errors => {
          if (errors) {
            this.$message.error('请完善表单信息!')
            reject(errors)
            return
          }
          for (const item of this.assetSbAsEntities) {
            if (!item.codeId) {
              this.$message.error('请正确完善年检信息!')
              return
            }
            if (!item.annualInspectionDate) {
              this.$message.error('请正确完善年检信息!')
              return
            }
            if (!/^\d+(\.\d*)?$/.test(item.period)) {
              this.$message.error('请正确填写年检周期!')
              return
            }
            if (!/^\d+(\.\d*)?$/.test(item.remindDeadline)) {
              this.$message.error('请正确填写提醒期限!')
              return
            }
          }
          // 实时的年检信息
          const assetSbAsEntities = []
          for (const item of this.assetSbAsEntities) {
            assetSbAsEntities.push({
              ...item
            })
          }
          // 年检信息是否有删除
          for (const item of this.lastAssetSbAsEntities) {
            if (!this.assetSbAsEntities.some(current => current.uuid === item.uuid)) {
              assetSbAsEntities.push({
                ...item,
                action: 'delete'
              })
            }
          }
          // 信息
          resolve([
            {
              ...record,
              ...this.getFieldsValue(),
              assetSbAsEntities: assetSbAsEntities.map(item => {
                return {
                  ...item,
                  annualInspectionDate: item.annualInspectionDate ? item.annualInspectionDate.format('YYYY-MM-DD') : ''
                }
              })
            }
          ])
        })
      })
    },
    // 更新表单数据
    doUpdateData (record) {
      return this.doValidateData(record).then(records => {
        if (!this.spinning) {
          // 更新中
          this.spinning = true

          // 参数处理
          const notice = {
            error: this.action === 'insert' ? '新增失败！' : '更新失败！',
            success: this.action === 'insert' ? '新增成功！' : '更新成功！'
          }

          // 调用接口
          return assetSbApi
            .modifyEquipment(requestBuilder(this.action, records))
            .then(res => {
              if (res.code !== '0000') {
                this.$notification.error({
                  message: '系统消息',
                  description: res.message || notice.error
                })
                return Promise.reject(res)
              }
              this.$notification.success({
                message: '系统消息',
                description: notice.success
              })
            })
            .finally(() => {
              this.spinning = false
            })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep {
  .s-form-container {
    .ant-form-horizontal {
      .ant-form-item {
        & > .ant-form-item-label {
          width: 100px;
        }
      }
    }
    .ant-form-inline {
      .ant-form-item {
        & > .ant-form-item-label {
          width: 100px;
        }
      }
    }
  }
  .ant-upload {
    float: left;
    padding-left: 6px;
    margin-bottom: 3px;
  }
  .ant-upload-list {
    clear: both;
    border-top: dashed 1px #cfcfcf;
  }
}
.form-item {
  width: 100%;
  margin-bottom: 20px;
  .form-header {
    display: flex;
    width: 100%;
    height: 30px;
    padding-left: 5px;
    margin-bottom: 7px;
    line-height: 30px;
    font-size: 13px;
    color: #303133;
    border-bottom: dashed 1px #cfcfcf;
    .titles {
      display: flex;
      width: calc(100% - 50px);
      padding-left: 8px;
      line-height: 32px;
    }
    .buttons {
      width: 50px;
    }
  }
  .form-bodyer {
    padding: 0 3px;
    margin-bottom: 7px;
    overflow: hidden;
    .form-every {
      display: flex;
      width: 100%;
      margin: 3px 0 7px;
      & > .inputs {
        display: flex;
        width: calc(100% - 50px);
      }
      & > .buttons {
        width: 50px;
        & > .button {
          margin-top: 0;
          margin-bottom: 0;
        }
      }
    }
  }
  .form-empty {
    margin-bottom: 20px;
    padding-left: 7px;
    font-size: 13px;
  }
  .form {
    margin-bottom: 60px;
  }
}
</style>
