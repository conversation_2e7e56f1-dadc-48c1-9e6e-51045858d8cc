<template>
  <section>
    <div>
      <div>
        <a-card :bordered="false">
          <div class="table-page-search-wrapper">
            <a-form layout="inline">
              <a-row :gutter="10">
                <a-col
                  :xl="6"
                  :md="6"
                  :sm="6"
                >
                  <a-form-item label="名称">
                    <a-input
                      v-model="queryParam.name"
                      @pressEnter="doSearch"
                      placeholder
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :xl="6"
                  :md="6"
                  :sm="6"
                >
                  <a-button
                    v-action:query
                    type="primary"
                    icon="search"
                    @click="doSearch"
                  >查询</a-button>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </a-card>
        <a-card
          class="flex-column"
          style="height: calc(100% - 96px)"
          :bordered="false"
        >
          <!-- 表格 -->
          <s-table
            ref="table"
            :data="loadData"
            :scroll="scroll"
            :columns="columns"
            :rowClassName="rowClassName"
            :showPagination="true"
            :pageSizeOptions="pageSizeOptions"
            :pageSize="defaultPageSize"
            :immediate="false"
            :bordered="false"
            :rowKey="(record) => record.uuid"
          >
            <div
              slot="action"
              slot-scope="text, record"
            >
              <a
                href="javascript:void(0)"
                style="margin: 0 3px; color: #40a9ff;"
                @click.stop="openUploadDrawer(record)"
              >二维码查询</a>
            </div>
          </s-table>
        </a-card>
      </div>
      <div class="nodes-container">
        <div
          v-for="item in generate"
          :key="item.level"
          :style="{ width: 100 / series + '%' }"
          class="nodes-group"
        >
          <div class="nodes-header">
            <span class="title">{{ item.text }}</span>
            <a
              v-action:add
              v-if="showAddButton(item.level)"
              href="javascript:void(0)"
              @click="doAdd(item.level)"
            >新增</a>
          </div>
          <div class="nodes-body">
            <div
              v-if="doRender(item.level).length > 0"
              class="list"
            >
              <div
                v-for="(item2, index2) in doRender(item.level)"
                :class="{ active: isActiveStatus(item.level, item2), disabled: isDisabledStatus(item2) }"
                :key="index2"
                @click="doChange(item2)"
                class="item"
              >
                <a-icon
                  v-if="isDisabledStatus(item2)"
                  class="icon"
                  type="minus-circle"
                />
                <a-icon
                  v-else
                  class="icon"
                  type="check-circle"
                />
                <span class="text">{{ item2.name }}</span>
                <a
                  v-action:edit
                  href="javascript:void(0)"
                  class="button"
                  @click="doOpen(item2,'update')"
                >修改</a>
                <a
                  v-action:edit
                  href="javascript:void(0)"
                  class="button"
                  @click="doOpen(item2,'browse')"
                >浏览</a>
              </div>
            </div>
            <a-empty
              v-else
              class="empty"
              :description="false"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- 侧边滑动栏 -->
    <a-drawer
      :title="title"
      :width="360"
      :visible="visible"
      :mask="true"
      :bodyStyle="{ paddingBottom: '80px' }"
      @close="handleCancel"
    >
      <a-spin :spinning="confirmLoading">
        <a-form
          :form="form"
          layout="vertical"
          hideRequiredMark
        >
          <a-form-item
            label="名称:"
          >
            <a-input
              :disabled="type === 'browse'"
              v-decorator="['name', {rules: [{type: 'string',required: true, message: '请输入至少一个字符！'}]}]"
            />
          </a-form-item>
          <a-form-item
            label="是否部件:"
          >
            <a-select
              :disabled="type === 'browse'"
              v-decorator="['isPart', {rules: [{required: true, message: '请选择是或否'}]}]"
            >
              <a-select-option value="Y">是</a-select-option>
              <a-select-option value="N">否</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item
            label="状态:"
            v-show="form.getFieldValue('isPart') ==='Y'"
          >
            <a-select
              :disabled="type === 'browse'"
              v-decorator="['status']"
            >
              <a-select-option value="正常使用">正常使用</a-select-option>
              <a-select-option value="待维修">待维修</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item
            label="更换时间:"
            v-show="form.getFieldValue('isPart') ==='Y'">
            <a-date-picker
              :disabled="type === 'browse'"
              v-decorator="['replacementTime']" />
          </a-form-item>
          <a-form-item
            label="厂家:"
            v-show="form.getFieldValue('isPart') ==='Y'">
            <a-input
              :disabled="type === 'browse'"
              v-decorator="['factory']"/>
          </a-form-item>
          <a-form-item
            label="规格型号:"
            v-show="form.getFieldValue('isPart') ==='Y'">
            <a-input
              :disabled="type === 'browse'"
              v-decorator="['model']"/>
          </a-form-item>
          <a-form-item
            label="价格:"
            v-show="form.getFieldValue('isPart') ==='Y'">
            <a-input
              :disabled="type === 'browse'"
              v-decorator="['price']"/>
          </a-form-item>
          <a-form-item label="是否激活">
            <a-select
              :disabled="type === 'browse'"
              v-decorator="['activity', {rules: [{required: true, message: '请选择'}]}]"
            >
              <a-select-option value="Y">有效</a-select-option>
              <a-select-option value="N">无效</a-select-option>
            </a-select>
          </a-form-item>
          <!-- <a-form-item
            label="点检标准"
            v-show="form.getFieldValue('isPart') !=='Y'">
            <a-select
              showSearch
              allowClear
              optionFilterProp="label"
              :disabled="type === 'browse'"
              v-decorator="['checkStandard', {rules: [{required: false, message: '请选择'}]}]"
            >
              <a-select-option
                v-if="!item.disabled"
                v-for="(item, index) in checkStandards"
                :value="item.label"
                :label="item.label"
                :key="index"
              >{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item> -->
        </a-form>
      </a-spin>
      <div
        :style="{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
          zIndex: 1,
        }"
      >
        <a-button
          :style="{ marginRight: '8px' }"
          @click="handleCancel"
        >取消</a-button>
        <a-button
          @click="handleOk"
          type="primary"
        >确认</a-button>
      </div>
    </a-drawer>
    <parts-qr-inform ref="PartsQrInform"/>
  </section>
</template>

<script>
import { requestBuilder } from '@/utils/util'
import * as baseApi from '@/api/system/base'
import * as assetSbApi from '@/api/device/assetSb'
import PartsQrInform from './PartsQrInform.vue'
import moment from 'moment'
import { STable } from '@/components'

export default {
  name: 'Parts',
  components: {
    STable,
    PartsQrInform
  },
  props: {
    assetUuid: {
      type: String,
      default: ''
    }
  },
  watch: {
    assetUuid: {
      handler (assetUuid) {
        assetUuid ? this.getParts() : this.doClear()
      }
    }
  },
  data () {
    return {
      show: false,
      // 层级数量
      series: 0,
      // 激活节点
      actives: [],
      // 层级数据
      nodes: [],
      // 点检标准模板
      checkStandards: [],
      // 弹框配置
      node: null,
      parent: null,
      visible: false,
      level: 1,
      title: '',
      // 提交类型
      type: '',
      form: this.$form.createForm(this),
      confirmLoading: false,
      scroll: {
        x: '100%',
        y: 'calc(42vh)',
        scrollToFirstRowOnChange: false
      },
      queryParam: {
        isPart: 'Y',
        assetSbUuid: this.assetUuid
      },
      pageSizeOptions: ['10', '15', '20', '25', '30'],
      defaultPageSize: 15,
      loadData: parameter => {
        const param = requestBuilder('', Object.assign(this.queryParam), parameter.pageNo, parameter.pageSize)
        return assetSbApi.queryAssetSbPart(param).then(res => res.result)
      },
      rowClassName: record => {
        return 'cursor-pointer'
      },
      columns: [
        {
          title: '名称',
          dataIndex: 'name',
          width: 100
        },
        {
          title: '状态',
          dataIndex: 'status',
          width: 100
        },
        {
          title: '更换时间',
          dataIndex: 'replacementTime',
          width: 100
        },
        {
          title: '厂家',
          dataIndex: 'factory',
          width: 100
        },
        {
          title: '规格型号',
          dataIndex: 'model',
          width: 100
        },
        {
          title: '价格',
          dataIndex: 'price',
          width: 100
        },
        {
          title: '操作',
          key: 'action',
          scopedSlots: { customRender: 'action' },
          align: 'center',
          fixed: 'right',
          width: 120
        }
      ]
    }
  },
  computed: {
    // 创建层级dom（仅支持 1～99层级）
    generate () {
      let level = 0
      const result = []
      // const library = ['十', '一', '二', '三', '四', '五', '六', '七', '八', '九']
      const library = ['十', '部件', '构件', '备用列']
      while (level < this.series) {
        let text = ''
        const srting = String(++level)
        const length = srting.length
        for (let i = 0; i < length; i++) {
          if (length === 1) {
            text += library[srting[i]]
          }
          if (length === 2) {
            switch (i) {
              case 0: {
                text += srting[i] === '1' ? '十' : library[srting[i]]
                break
              }
              case 1: {
                text +=
                  srting[0] !== '1'
                    ? srting[i] === '0'
                      ? '十'
                      : '十' + library[srting[i]]
                    : srting[i] === '0'
                      ? ''
                      : library[srting[i]]
                break
              }
            }
          }
        }
        result.push({ level, text })
      }
      return result
    }
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      baseApi.getCommboxById({ id: 'basCodeByClassId', sqlParams: { codeClassId: 'check_standard' } }).then(res => {
        if (res.code === '0000') {
          this.checkStandards = res.result
        }
      })
    },
    // 查询计划
    getParts () {
      assetSbApi.queryAssetSbPart(requestBuilder('', { assetSbUuid: this.assetUuid }, null, null)).then(res => {
        if (res.code !== '0000') {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '查询失败！'
          })
          return
        }
        this.queryParam.assetSbUuid = this.assetUuid
        this.doSearch()
        this.nodes = []
        const data = res.result.data
        const tree = this.doTrees(data)
        this.series = this.getDeep(tree) + 1
        this.doArray(tree)
        this.doActive()
      })
    },
    getDeep (tree) {
      let deep = 0
      tree.forEach(item => {
        if (item.children) {
          deep = Math.max(deep, this.getDeep(item.children) + 1)
        } else {
          deep = Math.max(deep, 1)
        }
      })
      return deep
    },
    doClear () {
      this.$refs.table.clear()
      this.series = 0
    },
    // 是否显示新增按钮
    showAddButton (level) {
      // return level === 1 || this.actives[level - 2]
      return level === 1 || level === 2 || this.actives[level - 1]
    },
    // 显示禁用状态
    isDisabledStatus (node) {
      return node.activity !== 'Y'
    },
    // 显示选中状态
    isActiveStatus (level, node) {
      const index = level - 1
      const actives = this.actives
      const activeNode = actives[index]
      const activeId = activeNode && activeNode.uuid
      const nodeId = node.uuid
      return activeId === nodeId
    },
    // 储存选中的节点
    doActive () {
      let index = 0
      const nodes = this.nodes
      const series = this.series
      const actives = this.actives
      while (index < series) {
        const currentNodes = nodes[index] || []
        const currentActiveNode = actives[index] || null
        const parentActiveNode = actives[index - 1] || null
        const parentActiveNodeId = parentActiveNode && parentActiveNode.uuid
        const currentActiveNodeId = currentActiveNode && currentActiveNode.uuid
        const parentNodeId = currentActiveNode && currentActiveNode.parent && currentActiveNode.parent.uuid
        const filterNodes = currentNodes.filter(item => index === 0 || item.parent.uuid === parentActiveNodeId)
        if (parentActiveNodeId !== parentNodeId) {
          this.$set(actives, index, undefined)
        }
        if (actives[index]) {
          this.$set(
            actives,
            index,
            filterNodes.find(item => item.uuid === currentActiveNodeId)
          )
        }
        if (!actives[index]) {
          index < series && this.$set(actives, index, filterNodes[0])
        }
        index++
      }
    },
    // 渲染指定层级的dom
    doRender (level) {
      const array = []
      const index = level - 1
      const nodes = this.nodes
      const actives = this.actives
      const current = nodes[index] || []
      for (const node of current) {
        const last = index - 1
        const nodeId = actives[last] && actives[last].uuid
        const parentNodeId = node.parent && node.parent.uuid
        if (index === 0 || nodeId === parentNodeId) {
          array.push(node)
        }
      }
      return array
    },
    // 扁平化结构 转换 树形结构
    doTrees (array) {
      const cache = {}
      const trees = []
      for (const node of array) {
        const parentId = node.parentId
        const nodeId = node.uuid
        if (!cache[nodeId]) {
          cache[nodeId] = {}
        }
        if (!cache[parentId]) {
          cache[parentId] = { children: [] }
        }
        if (!cache[parentId].children) {
          cache[parentId].children = []
        }
        if (!cache[parentId].children.includes(cache[nodeId])) {
          cache[parentId].children.push(cache[nodeId])
        }
        Object.assign(cache[nodeId], { ...node })
      }
      for (const key in cache) {
        if (cache[key] && !cache[key].parentId && cache[key].children) {
          trees.push(...cache[key].children)
        }
      }
      return trees
    },
    // tree 结构 转换为 层级数据
    doArray (tree, parent) {
      const empty = { level: 0 }
      const upper = parent || empty
      const level = upper.level + 1
      const index = level - 1
      if (tree && level <= this.series) {
        if (!this.nodes[index]) {
          this.$set(this.nodes, index, [])
        }
        for (const node of tree) {
          if (parent && !parent.children) {
            parent.children = [node]
          }
          if (parent && !parent.children.includes(node)) {
            parent.children.push(node)
          }
          this.nodes[index].push(
            Object.assign(node, {
              level: level || 1,
              parent: parent || null
            })
          )
          this.doArray(node.children, node)
        }
      }
    },
    // 选中节点更改，相应层级数据更改
    doChange (node) {
      const level = node.level
      const index = level - 1
      const actives = this.actives
      this.$set(actives, index, node)
      this.doActive()
    },
    // 打开编辑弹框
    doOpen (node, type) {
      if (type === 'update' && node.parent && node.parent.activity !== 'Y') {
        this.$message.error('当前父节点已禁用，不可修改！')
        return
      }
      this.node = node
      this.parent = node.parent
      this.title = type === 'update' ? '修改' : '浏览'
      this.type = type
      this.$nextTick(() => {
        this.form.setFieldsValue({
          name: node.name,
          isPart: node.isPart,
          status: node.status || null,
          replacementTime: node.replacementTime ? moment(node.replacementTime) : null,
          factory: node.factory || '',
          model: node.model || '',
          price: node.price || '',
          activity: node.activity
          // checkStandard: node.checkStandard
        })
      })
      this.visible = true
    },
    // 打开新增弹框
    doAdd (level) {
      const index = level - 1
      const lastIndex = level - 2
      this.node = this.actives[index]
      this.parent = this.actives[lastIndex]
      if (this.parent && this.parent.activity !== 'Y') {
        this.$message.error('当前父节点已禁用，不可新增！')
        return
      }
      this.title = '新增'
      this.type = 'insert'
      this.visible = true
      this.form.setFieldsValue({
        isPart: level === 1 ? 'Y' : 'N'
      })
    },
    handleOk () {
      if (this.type !== 'browse') {
        const {
          form: { validateFields }
        } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
        // 如果输入数据都合规
          if (!errors) {
            const notice = {
              error: this.type === 'insert' ? '新增失败！' : '更新失败！',
              success: this.type === 'insert' ? '新增成功！' : '更新成功！'
            }
            console.log(this.parent)
            const params = [
              {
                ...values,
                assetSbUuid: this.assetUuid,
                replacementTime: values.replacementTime ? values.replacementTime.format('YYYY-MM-DD') : '',
                parentId: (this.parent && this.parent.uuid) || 0
              }
            ]
            if (this.type === 'update') {
              params[0].uuid = this.node.uuid
            }
            assetSbApi.modifyAssetSbPart(requestBuilder(this.type, params, '0', '0'))
              .then(res => {
                if (res.code !== '0000') {
                  this.$notification.error({
                    message: '系统消息',
                    description: res.message || notice.error
                  })
                  return Promise.reject(res)
                }
                this.$notification.success({
                  message: '系统消息',
                  description: notice.success
                })
                this.handleCancel()
                this.getParts()
              }).finally(() => {
                this.confirmLoading = false
              })
          } else {
            this.confirmLoading = false
          }
        })
      } else {
        this.handleCancel()
      }
    },
    // 取消(新增/更改)
    handleCancel () {
      this.form.resetFields()
      this.title = ''
      this.type = ''
      this.text = ''
      this.state = ''
      this.node = null
      this.parent = null
      this.visible = false
    },
    doSearch () {
      this.$refs.table.updateSelect([], [])
      this.$refs.table.refresh()
    },
    openUploadDrawer (record) {
      this.$refs.PartsQrInform.openModal(record)
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep {
  .ant-empty-image {
    height: 65px;
  }
}
section {
  width: 100%;
  height: 100%;
  position: absolute;
  background-color: #ffffff;
}
.nodes-container {
  height: 90vh;
  padding: 20px 10px 5px;
  box-sizing: border-box;
  flex-flow: row nowrap;
  overflow: auto;
  display: flex;
  & > .nodes-group {
    min-width: 250px;
    padding: 15px 25px 5px;
    box-sizing: border-box;
    position: relative;
    flex: 0 0 auto;
    &:not(:last-child)::after {
      content: '';
      width: 1px;
      height: calc(100% - 20px);
      background: #eeeeee;
      position: absolute;
      top: 10px;
      right: 1px;
    }
    .nodes-header {
      padding: 5px 5px 8px;
      text-align: center;
      border-bottom: solid 1px #f0f0f0;
      & > a {
        font-size: 13px;
        margin: 0 8px;
      }
      & > .title {
        font-size: 16px;
        margin: 0 8px;
      }
    }
    .nodes-body {
      height: calc(90% - 40px);
      padding: 10px 3px;
      & > .list {
        height: 100%;
        overflow: auto;
        & > .item {
          padding: 8px 8px;
          display: flex;
          cursor: default;
          &:hover,
          &:active {
            background-color: #f9f9f9;
            & > .button {
              display: inline-block;
            }
          }
          &.disabled {
            & > .icon {
              color: #f34d4d;
            }
            & > .text {
              color: #f34d4d;
            }
          }
          &.active {
            background-color: #f3f3f3;
          }
          & > .icon {
            height: 16px;
            padding: 3.5px;
            font-size: 16px;
            vertical-align: middle;
            display: inline-block;
          }
          & > .text {
            width: 100%;
            font-size: 16px;
            vertical-align: middle;
            display: inline-block;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            flex: 0 1 auto;
          }
          & > .button {
            font-size: 13px;
            padding: 2px 3px;
            vertical-align: middle;
            flex: 0 0 auto;
            display: none;
          }
        }
      }
      & > .empty {
        padding: 10px 0;
      }
    }
  }
}
</style>
<style lang="less">
.small-modal {
  .ant-modal {
    top: calc(50% - 200px);
  }
  .ant-form-inline {
    .ant-form-item {
      & > .ant-form-item-label {
        width: 50px;
      }
    }
  }
}
</style>
