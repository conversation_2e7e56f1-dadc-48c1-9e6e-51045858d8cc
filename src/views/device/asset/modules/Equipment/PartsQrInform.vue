<template>
  <a-modal v-model="modalShow" title="部件二维码信息" cancelText="关闭" width="600px">
    <qr-code-table ref="QrCodeTable"/>
    <template slot="footer">
      <a-button @click="closeModal">关闭</a-button>
    </template>
  </a-modal>
</template>
<script>
import QrCodeTable from '../Resume/qrCodeTable.vue'
export default {
  name: 'PartsQrInform',
  components: {
    QrCodeTable
  },
  data () {
    return {
      modalShow: false
    }
  },
  methods: {
    openModal (record) {
      // 调用查询二维码方法
      this.modalShow = true
      this.$nextTick(() => {
        this.$refs.QrCodeTable.queryMaterialPartQrCode(record)
      })
    },
    closeModal () {
      this.$refs.QrCodeTable.doClear()
      this.modalShow = false
    }
  }
}
</script>

<style>

</style>
