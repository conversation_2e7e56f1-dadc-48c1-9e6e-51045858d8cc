<template>
  <section>
    <a-drawer
      width="100vw"
      class="no-transform"
      :visible="visible"
      :mask="false"
      :maskClosable="false"
      :getContainer="false"
      @close="doClose()"
    >
      <!-- 关闭图标 -->
      <a-icon
        type="close"
        style="position: absolute; top: 13px; right: 10px; z-index: 25; cursor: pointer;"
        @click="doClose"
      />

      <!-- 标签页 -->
      <a-tabs
        v-model="tabsKey"
        tabPosition="top"
        class="tabDrawer"
      >
        <a-tab-pane
          key="1"
          tab="装卸设备信息"
          :forceRender="true"
        >
          <info-form
            ref="form"
            :assetSbUuid="assetSbUuid"
            :loading.sync="loading"
            :disabled="disabled"
            :readonly="readonly"
            :isDisabled="isDisabled"
            :isAdd="isAdd"
          />
          <!-- <info-upload
            ref="upload"
            :readonly="readonly"
            :disabled="disabled"
          /> -->
          <appendix
            ref="appendix"
            text="整机图片"
            :directDisplay="true"
            v-show="showUploadFile"
          />
        </a-tab-pane>
        <!-- 原履历册改为维修记录 -->
        <a-tab-pane
          key="2"
          tab="维修记录"
          :forceRender="true"
        >
          <resume-table
            :assetUuid="assetSbUuid"
            :assetType="assetSbType"
          />
        </a-tab-pane>
        <a-tab-pane
          key="3"
          tab="二维码"
          :forceRender="true"
        >
          <qrCode-table
            :assetUuid="assetSbUuid"
          />
        </a-tab-pane>
        <a-tab-pane
          key="4"
          tab="配件"
          :forceRender="true"
        >
          <parts
            ref="parts"
            :assetUuid="assetSbUuid"
          />
        </a-tab-pane>
      </a-tabs>

      <!-- 按钮组 -->
      <div class="drawer-footer">
        <div class="footer-fixed">
          <a-button @click="doClose()">取消</a-button>
          <a-button
            v-if="!readonly && !disabled"
            type="primary"
            :loading="loading"
            @click="doSubmit()"
          >保存</a-button>
        </div>
      </div>
    </a-drawer>
  </section>
</template>

<script>
import Vue from 'vue'
import InfoForm from './InfoForm.vue'
import InfoUpload from './InfoUpload'
import Appendix from '@/views/system/components/Appendix'
import ResumeTable from '../Resume/InfoTable'
import { getCommboxById } from '@/api/system/base'
import { ORG_ID } from '@/store/mutation-types'
import { ORG_ID_JYS } from '@/store/variable-types'
import ResumeTableJys from '../Resume/ResumeJYS/InfoTable'
import Parts from './Parts'
import QrCodeTable from '../Resume/qrCodeTable.vue'

// 操作人 orgId
const USER_ORG_ID = Vue.ls.get(ORG_ID)

// 运行环境 ENV
const ENV_JYS = USER_ORG_ID === ORG_ID_JYS
export default {
  name: 'InfoDrawer',
  components: {
    InfoForm,
    InfoUpload,
    ResumeTable,
    QrCodeTable,
    ResumeTableJys,
    Appendix,
    Parts
  },
  props: {
    dataChanged: {
      type: Function,
      default: function () {}
    },
    drawerClosed: {
      type: Function,
      default: function () {}
    },
    disabled: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      workorderTypeArr: [],
      ENV_JYS,
      // 弹窗
      title: '',
      action: '',
      tabsKey: '1',
      assetSbUuid: '',
      assetSbType: 'asset_sb',
      isAdd: true,
      visible: false,
      loading: false,
      record: null,
      // 新增状态下禁止出现附件组件,新增前还不存在uuid无法查询
      showUploadFile: false
    }
  },
  methods: {
    // 是否禁用
    isDisabled (type) {
      if (type) {
        return type === this.action
      }
      return false
    },
    // 设备信息新增
    doAdd () {
      this.showUploadFile = false
      this.doOpen('insert', {})
    },
    // 设备信息修改
    doEdit (records) {
      this.showUploadFile = true
      this.doOpen('update', records[0])
    },
    // 设备信息删除
    doDel (records) {
      return this.$refs.form.doDeleteData(records).then(() => {
        this.dataChanged()
      })
    },
    // 打开信息弹框
    doOpen (action, record) {
      // 配置
      this.record = record
      this.action = action
      this.isAdd = this.action === 'insert'
      this.title = this.isAdd ? '设备新增' : this.readonly || this.disabled ? '设备预览' : '设备信息'
      this.assetSbUuid = record.uuid
      if (!this.isAdd && ENV_JYS) {
        getCommboxById({ id: 'assetWorkorderType', sqlParams: { assetUuid: this.assetSbUuid } }).then(res => {
          this.workorderTypeArr = res.result.map(item => item.value)
        })
      }
      // 处理 form表单
      this.$refs.form.doCreateForm(action, record)
      // 处理 附件文件
      if (this.showUploadFile) {
        this.$refs.appendix.doCreateUpload('assetSb', record)
      }
      // 显示弹框
      this.visible = true
    },
    // 关闭信息弹框
    doClose () {
      // 是否正上传中
      // for (const file of this.$refs.upload.uploadFileList) {
      //   if (file && file.status === 'uploading') {
      //     this.$message.error('有文件正在上传中..., 请稍后再试！')
      //     return
      //   }
      // }
      if (this.showUploadFile && !this.$refs.appendix.hiddenUploadDrawer()) {
        return
      }
      // 表单
      this.$refs.form.doDestroyForm()
      // 附件
      // this.$refs.upload.doDestroyUpload()
      // 弹窗
      this.title = ''
      this.action = ''
      if (ENV_JYS) {
        this.workorderTypeArr = []
      }
      this.tabsKey = '1'
      this.assetSbUuid = ''
      this.isAdd = false
      this.loading = false
      this.visible = false
      this.record = null
      // 回调
      this.drawerClosed()
    },
    // 保存设备信息
    doSubmit () {
      this.$refs.form.doUpdateData(this.record).then(() => {
        this.dataChanged()
      })
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep {
  .ant-drawer {
    .ant-drawer-header-no-title {
      .ant-drawer-close {
        display: none;
      }
      & + .ant-drawer-body {
        height: 100%;
        padding: 0 20px 10px;
        overflow: visible;
        .ant-tabs.ant-tabs-top.ant-tabs-line{
          height: 100%;
        }
        .ant-tabs {
          margin-top: 10px;
          overflow: visible;
          & > .ant-tabs-bar {
            background-color: #ffffff;
            position: sticky;
            padding-top: 10px;
            z-index: 10;
            top: 0;
          }
          .ant-upload {
            float: left;
            padding-left: 5px;
            margin-bottom: 1px;
          }
          .ant-upload-list {
            width: 100%;
            clear: both;
            border-top: dashed 1px #cfcfcf;
          }
        }
      }
    }
  }
  .ant-drawer-content {
    padding-bottom: 55px;
  }
  .ant-drawer-wrapper-body {
    overflow-x: hidden;
  }
}
</style>
