<template>
  <section>
    <!-- 表格 -->
    <s-table
      ref="table"
      :data="loadData"
      :scroll="scroll"
      :columns="columns"
      :customRow="rowClick"
      :rowSelection="rowSelection"
      :rowClassName="rowClassName"
      :showPagination="showPagination"
      :pageSizeOptions="pageSizeOptions"
      :pageSize="defaultPageSize"
      :immediate="immediate"
      :bordered="bordered"
      rowKey="uuid"
    >
      <span slot="assetStatus" slot-scope="key">{{ takeSelectLabel(queryOptions.assetStatus, key) }}</span>
      <span slot="sccj" slot-scope="key">{{ takeSelectLabel(queryOptions.sccjs, key) || key }}</span>
      <span slot="dkpp" slot-scope="key">{{ takeSelectLabel(queryOptions.dkpps, key) }}</span>
      <span slot="isZdsb" slot-scope="key">{{ takeSelectLabel(queryOptions.isZdsbs, key) || '' }}</span>
      <span slot="isSmartcar" slot-scope="key">{{ takeSelectLabel(queryOptions.isSmartcars, key) || '' }}</span>
      <span slot="isRemote" slot-scope="key">{{ takeSelectLabel(queryOptions.isRemotes, key) || '' }}</span>
      <span slot="useDept" slot-scope="key">{{ takeSelectLabel(queryOptions.useDepts, key) || '' }}</span>
      <span slot="operatingConditions" slot-scope="key">{{ takeSelectLabel(queryOptions.operatingConditions, key) || '' }}</span>
      <span slot="sync" slot-scope="key">{{ takeSelectLabel(queryOptions.isSync, key) || '' }}</span>
    </s-table>

    <!-- 信息框 -->
    <info-drawer
      ref="infoDrawer"
      :readonly="readonly"
      :disabled="disabled"
      :drawerClosed="drawerClosed"
      :dataChanged="dataChanged"
    />
  </section>
</template>

<script>
import Vue from 'vue'
import InfoDrawer from './InfoDrawer'
import { STable } from '@/components'
import { ORG_ID } from '@/store/mutation-types'
import { ORG_ID_JYS, ORG_ID_TMG } from '@/store/variable-types'
import { requestBuilder, deepUpdate, takeTreeByKey } from '@/utils/util'
import * as assetSbApi from '@/api/device/assetSb'

// 操作人 userNo
const USER_ORG_ID = Vue.ls.get(ORG_ID)

// 运行环境 ENV
const ENV_JYS = USER_ORG_ID === ORG_ID_JYS

// 运行环境 ENV
const ENV_TMG = USER_ORG_ID === ORG_ID_TMG

export default {
  name: 'InfoTable',
  components: {
    STable,
    InfoDrawer
  },
  props: {
    queryOptions: {
      type: Object,
      default: function () {
        return {}
      }
    },
    syncLoading: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      // 集运司
      ENV_JYS,
      ENV_TMG,
      // 设备表格 - 参数
      queryParam: {},
      // 设备表格 - 配置
      columns: [
        {
          title: '设备编号',
          dataIndex: 'assetSbNum',
          ellipsis: true,
          sorter: true,
          width: 200
        },
        {
          title: '原值(万元)',
          dataIndex: 'originValue',
          ellipsis: true,
          sorter: true,
          width: 100
        },
        ...(ENV_TMG ? [
          {
            title: '运行工况',
            dataIndex: 'operatingConditions',
            scopedSlots: { customRender: 'operatingConditions' },
            ellipsis: true,
            width: 120
          } ]
          : []),
        {
          title: '是否重点设备',
          dataIndex: 'isZdsb',
          scopedSlots: { customRender: 'isZdsb' },
          ellipsis: true,
          sorter: true,
          width: 100
        },
        {
          title: '是否智能集卡',
          dataIndex: 'isSmartcar',
          scopedSlots: { customRender: 'isSmartcar' },
          ellipsis: true,
          sorter: true,
          width: 100
        },
        {
          title: '是否远控',
          dataIndex: 'isRemote',
          scopedSlots: { customRender: 'isRemote' },
          ellipsis: true,
          sorter: true,
          width: 100
        },
        {
          title: '设备状态',
          dataIndex: 'assetStatus',
          scopedSlots: { customRender: 'assetStatus' },
          ellipsis: true,
          sorter: true,
          width: 100
        },
        {
          title: '购置日期',
          dataIndex: 'purdate',
          ellipsis: true,
          sorter: true,
          width: 90
        },
        {
          title: ENV_JYS ? '所属单位' : '所属部门',
          dataIndex: 'useDept',
          scopedSlots: { customRender: 'useDept' },
          ellipsis: true,
          sorter: true,
          width: 120
        },
        {
          title: '生产厂家',
          dataIndex: 'sccj',
          ellipsis: true,
          width: 120
        },
        {
          title: '制造年月',
          dataIndex: 'produceDate',
          ellipsis: true,
          sorter: true,
          width: 90
        },
        {
          title: '规格型号',
          dataIndex: 'model',
          ellipsis: true,
          sorter: true,
          width: 110
        },
        {
          title: '电控品牌',
          dataIndex: 'dkpp',
          sorter: true,
          scopedSlots: { customRender: 'dkpp' },
          width: 100
        },
        {
          title: '是否同步',
          dataIndex: 'sync',
          sorter: true,
          scopedSlots: { customRender: 'sync' },
          width: 100
        }
      ],
      scroll: {
        x: '100%',
        y: '560px',
        scrollToFirstRowOnChange: false
      },
      rowSelection: {
        type: 'checkbox',
        onSelect: this.doTableSelect,
        onChange: this.doTableChange
      },
      dataSource: [],
      selectedRows: [],
      selectedRowKeys: [],
      pageSizeOptions: ['10', '15', '20', '25', '30'],
      defaultPageSize: 15,
      showPagination: true,
      clickTimer: null,
      immediate: false,
      bordered: false,
      loadData: parameter => {
        // 初始参数
        const queryParam = {
          ...this.queryParam,
          cylinderDates: undefined,
          safetyValveDates: undefined,
          pressureDates: undefined
        }
        if (!queryParam.assetSbAsEntities) {
          queryParam.assetSbAsEntities = []
        }
        if (this.queryParam.cylinderDates.length > 0) {
          const dates = this.queryParam.cylinderDates
          const startDate = dates[0] ? dates[0].format('YYYY-MM-DD') : ''
          const endDate = dates[1] ? dates[1].format('YYYY-MM-DD') : ''
          queryParam.assetSbAsEntities.push({
            codeId: '0001',
            annualInspectionStartDate: startDate,
            annualInspectionEndDate: endDate
          })
        }
        if (this.queryParam.safetyValveDates.length > 0) {
          const dates = this.queryParam.safetyValveDates
          const startDate = dates[0] ? dates[0].format('YYYY-MM-DD') : ''
          const endDate = dates[1] ? dates[1].format('YYYY-MM-DD') : ''
          queryParam.assetSbAsEntities.push({
            codeId: '0002',
            annualInspectionStartDate: startDate,
            annualInspectionEndDate: endDate
          })
        }
        if (this.queryParam.pressureDates.length > 0) {
          const dates = this.queryParam.pressureDates
          const startDate = dates[0] ? dates[0].format('YYYY-MM-DD') : ''
          const endDate = dates[1] ? dates[1].format('YYYY-MM-DD') : ''
          queryParam.assetSbAsEntities.push({
            codeId: '0003',
            annualInspectionStartDate: startDate,
            annualInspectionEndDate: endDate
          })
        }
        // 接口调参数
        const param = requestBuilder(
          '',
          deepUpdate({
            pageTag: true,
            activity: 'Y',
            ...queryParam,
            // 处理日期参数
            produceStartDate: queryParam.produceDate[0] ? queryParam.produceDate[0].format('YYYY-MM') : '',
            produceEndDate: queryParam.produceDate[1] ? queryParam.produceDate[1].format('YYYY-MM') : '',
            produceDate: undefined,
            roleType: 'device'
          }),
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder,
          parameter.dataFlag = '2'
        )
        // 接口调用
        return assetSbApi.queryEquipment(param).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '设备获取失败！'
            })
            return Promise.reject(res.result)
          }
          this.dataSource = [...res.result.data] || []
          return res.result
        })
      },
      rowClick: record => ({
        on: {
          click: () => {
            // 双击时消单击事件
            clearTimeout(this.clickTimer)
            this.selectedRows = [record]
            this.selectedRowKeys = [record.uuid]
            this.$refs.table.triggerSelect(this.selectedRowKeys, this.selectedRows)
            this.doInfoDrawerEdit([record])
          },
          oldClick: () => {
            // 限制频繁触发单击事件
            clearTimeout(this.clickTimer)
            this.clickTimer = setTimeout(() => {
              // 在单击效果为单选情况
              if (this.rowSelection.type === 'radio') {
                // 编辑模式下
                if (this.visible) {
                  this.doInfoDrawerEdit([record])
                }
              }
              // 在单击效果为多选情况
              if (this.rowSelection.type === 'checkbox') {
                console.log(record)
                const index = this.selectedRowKeys.indexOf(record.uuid)
                if (index > -1) {
                  this.selectedRowKeys.splice(index, 1)
                  this.selectedRows.splice(index, 1)
                } else {
                  this.selectedRowKeys.push(record.uuid)
                  this.selectedRows.push(record)
                }
                const dataSource = this.dataSource.filter(item => {
                  return this.selectedRowKeys.includes(item.uuid)
                })
                this.selectedRows = dataSource.map(item => item)
                this.selectedRowKeys = dataSource.map(item => item.uuid)
                this.$refs.table.triggerSelect(this.selectedRowKeys, this.selectedRows)
              }
            }, 300)
          }
        }
      }),
      rowClassName: record => {
        return 'cursor-pointer'
      },
      // 禁用 与 只读
      disabled: false,
      readonly: false,
      childMessage: ''
    }
  },
  methods: {
    // 获取下拉框选项文本
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || key
    },
    // 抽屉框关闭事件
    drawerClosed () {
      this.$refs.table.rowSelection.type = 'checkbox'
      this.$refs.table.triggerSelect([], [])
    },
    // 数据更改事件
    dataChanged () {
      this.doTableQuery(true)
    },
    // 表格手动勾选
    doTableSelect (record, selected, selectedRows) {
      this.visible && this.doInfoDrawerEdit([record])
      // this.$emit('sendRecord', record)
      // console.log(record)
    },
    // 处理 table 勾选事件
    doTableChange (selectedRowKeys, selectedRows) {
      this.selectedRows = selectedRows
      this.selectedRowKeys = selectedRowKeys
      this.$emit('sendRecord', this.selectedRows)
    },
    // 表格加载中
    doTableReady () {
      this.$refs.table.ready()
    },
    // 表格数据清空
    doTableClear () {
      this.$refs.table.clear()
    },
    // 表格分页查询
    doTableQuery (force) {
      this.$refs.table.rowSelection.type = 'checkbox'
      this.$refs.table.triggerSelect([], [])
      this.$refs.table.refresh(true)
      this.doInfoDrawerClose()
    },
    // 抽屉框信息新增
    doInfoDrawerAdd () {
      this.selectedRows = []
      this.selectedRowKeys = []
      this.$refs.table.triggerSelect([], [])
      this.$refs.table.rowSelection.type = 'radio'
      this.$refs.infoDrawer.doAdd()
    },
    // 抽屉框信息修改
    doInfoDrawerEdit (records) {
      if (records) {
        this.selectedRows = records
        this.selectedRowKeys = records.map(item => item.uuid)
        this.$refs.table.triggerSelect(this.selectedRowKeys, this.selectedRows)
      }
      this.$refs.table.rowSelection.type = 'radio'
      this.$refs.infoDrawer.doEdit(records)
    },
    // 抽屉框信息删除
    doInfoDrawerDel () {
      if (this.visible) {
        this.$message.error('新增/修改模式下，不可进行删除操作！')
        return
      }
      if (this.selectedRows.length === 0) {
        this.$message.error('请选择所要删除的设备！')
        return
      }
      this.$confirm({
        title: '系统提示',
        content: '确定删除吗？',
        onOk: () => {
          this.$refs.infoDrawer.doDel(this.selectedRows)
        }
      })
    },
    // 抽屉框关闭
    doInfoDrawerClose () {
      this.$refs.infoDrawer.doClose()
    },
    doExport (type) {
      // 初始参数
      const queryParam = {
        ...this.queryParam,
        cylinderDates: undefined,
        safetyValveDates: undefined,
        pressureDates: undefined
      }
      if (!queryParam.assetSbAsEntities) {
        queryParam.assetSbAsEntities = []
      }
      if (this.queryParam.cylinderDates.length > 0) {
        const dates = this.queryParam.cylinderDates
        const startDate = dates[0] ? dates[0].format('YYYY-MM-DD') : ''
        const endDate = dates[1] ? dates[1].format('YYYY-MM-DD') : ''
        queryParam.assetSbAsEntities.push({
          codeId: '0001',
          annualInspectionStartDate: startDate,
          annualInspectionEndDate: endDate
        })
      }
      if (this.queryParam.safetyValveDates.length > 0) {
        const dates = this.queryParam.safetyValveDates
        const startDate = dates[0] ? dates[0].format('YYYY-MM-DD') : ''
        const endDate = dates[1] ? dates[1].format('YYYY-MM-DD') : ''
        queryParam.assetSbAsEntities.push({
          codeId: '0002',
          annualInspectionStartDate: startDate,
          annualInspectionEndDate: endDate
        })
      }
      if (this.queryParam.pressureDates.length > 0) {
        const dates = this.queryParam.pressureDates
        const startDate = dates[0] ? dates[0].format('YYYY-MM-DD') : ''
        const endDate = dates[1] ? dates[1].format('YYYY-MM-DD') : ''
        queryParam.assetSbAsEntities.push({
          codeId: '0003',
          annualInspectionStartDate: startDate,
          annualInspectionEndDate: endDate
        })
      }
      // 接口调参数
      const param = requestBuilder(
        '',
        deepUpdate({
          pageTag: true,
          activity: 'Y',
          type: type,
          ...queryParam,
          // 处理日期参数
          produceStartDate: queryParam.produceDate[0] ? queryParam.produceDate[0].format('YYYY-MM') : '',
          produceEndDate: queryParam.produceDate[1] ? queryParam.produceDate[1].format('YYYY-MM') : '',
          produceDate: undefined,
          roleType: 'device'
        }),
        null,
        null,
        null,
        null,
        '2'
      )
      assetSbApi.doExportAssetSb(param).then(res => {
        if (res && res.data && res.headers['content-disposition']) {
          const link = document.createElement('a')
          const url = window.URL.createObjectURL(res.data)
          const contentDisposition = res.headers['content-disposition']
          let filename = contentDisposition.replace(/(.*;)?filename=([^;]+).*/i, '$2')
          try {
            filename = decodeURIComponent(escape(filename))
          } catch (e) {
          }
          document.body.appendChild(link)
          link.style.display = 'none'
          link.download = filename
          link.href = url
          link.click()
          link.remove()
          window.URL.revokeObjectURL(url)
        } else {
          this.$notification.error({
            message: '系统消息',
            description: '下载失败！'
          })
        }
      })
    },
    getParam () {

    },
    transfer2ETMS () {
      if (this.selectedRows.length === 0) {
        this.$message.error('请至少选择一条数据同步到etms')
        return
      }
      for (const item of this.selectedRows) {
        if (item.sync === 'Y') {
          this.$message.error('选择中已存在同步完成数据,请重新选择!')
          return
        }
      }
      // 按钮菊花
      this.$emit('callBack', true)
      // 调用接口
      assetSbApi
        .transfer2ETMS(requestBuilder('insert', this.selectedRows))
        .then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '同步到etms失败!'
            })
            return Promise.reject(res)
          }
          this.$notification.success({
            message: '系统消息',
            description: res.message || '同步到etms成功!'
          })
        })
        .finally(() => {
          this.$emit('callBack', false)
        })
    }
  }
}
</script>
