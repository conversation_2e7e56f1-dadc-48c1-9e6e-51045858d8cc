<template>
  <a-upload
    class="upload-container"
    :fileList="uploadFiles"
    :customRequest="doFileUpload"
    :beforeUpload="beforeUpload"
  >
    <slot>
      <a-button>
        <a-icon type="upload" />导入
      </a-button>
    </slot>
  </a-upload>
</template>

<script>
import { ORG_ID, OPERATOR } from '@/store/mutation-types'
import * as assetSbApi from '@/api/device/assetSb'
import MIME from 'mime'
import Vue from 'vue'

// 上传附件允许类型
const UPLOAD_ACCEPT_LIST = [
  // Excel
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel'
]

// 上传文件允许最大大小(MB)
const UPLOAD_NUM = 50
const UPLOAD_UNIT = 1024 * 1024
const UPLOAD_MAX = UPLOAD_NUM * UPLOAD_UNIT

export default {
  name: 'InfoImport',
  props: {
    loading: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    doSuccess: {
      type: Function,
      default: function () {}
    }
  },
  data () {
    return {
      beforeUpload: file => {
        if (!UPLOAD_ACCEPT_LIST.includes(file.type || MIME.getType(file.name))) {
          this.$message.error('暂不支持该文件类型上传！')
          return false
        }
        if (file.size > UPLOAD_MAX) {
          this.$message.error('上传文件大小不可超过' + UPLOAD_NUM + 'M')
          return false
        }
      },
      uploadFiles: [],
      uploading: false
    }
  },
  watch: {
    uploading: {
      handler (uploading) {
        this.$emit('update:loading', uploading)
      }
    }
  },
  methods: {
    // 文件上传事件
    doFileUpload (options) {
      // 文件信息
      const file = {
        name: options.file.name,
        type: options.file.type,
        size: options.file.size,
        uid: options.file.uid,
        status: 'uploading',
        percent: 0
      }

      // 添加至文件上传区
      this.uploadFiles.push(file)

      // 获取参数
      const data = new FormData()
      const USER_ORG_ID = Vue.ls.get(ORG_ID)
      const USER_OPERATOR = Vue.ls.get(OPERATOR)

      // 设置参数
      data.append('orgId', USER_ORG_ID)
      data.append('operator', USER_OPERATOR)
      data.append('file', options.file, options.file.name)

      // 调用文件上传接口
      assetSbApi
        .uploadAssetSbItem(data, event => {
          // 设置实时进度
          const percent = ((event.loaded / event.total) * 100) | 0
          file.percent = percent < 99 ? percent : 99
        })
        .then(res => {
          if (res.code !== '0000') {
            return Promise.reject(res)
          }
          // 上传成功
          this.$message.success('上传成功！')
          this.uploading = false
          this.doSuccess(true)
          file.status = 'done'
          file.percent = 100
        })
        .catch(() => {
          this.$message.error('上传失败！')
          this.uploading = false
          file.status = 'error'
          file.percent = 0
        })

      // 正在调用接口
      this.uploading = true
    }
  }
}
</script>

<style lang="less" scoped>
.upload-container {
  ::v-deep {
    .ant-upload-list {
      display: none;
    }
  }
}
</style>
