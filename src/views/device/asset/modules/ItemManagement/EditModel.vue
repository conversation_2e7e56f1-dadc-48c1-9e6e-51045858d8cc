<template>
  <section>
    <a-modal
      :title="title"
      :visible="visible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol" @submit="handleOk" @submit.native.prevent>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="设备大类">
              <a-select
                v-model="form.sbdl"
                :disabled="action === 'update'"
                optionFilterProp="label"
                allowClear
                showSearch
                @change="getSbxl"
              >
                <a-select-option
                  v-for="(item, index) in sbdls"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="设备小类">
              <a-select
                v-model="form.sbxl"
                :disabled="action === 'update'"
                optionFilterProp="label"
                allowClear
                showSearch
              >
                <a-select-option
                  v-for="(item, index) in sbxls"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="构件名称">
              <a-input v-model="form.name" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
  </section>
</template>

<script>
import { takeTreeByKey, requestBuilder, deepUpdate } from '@/utils/util'
import * as baseApi from '@/api/system/base'
import * as assetSbTemplateApi from '@/api/device/assetCheckTemplate'
export default {
  props: {
    // 设备大类
    sbdls: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      visible: false,
      confirmLoading: false,
      action: '',
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      form: {
        sbdl: '',
        sbxl: '',
        name: '',
        assetSbItemUuid: ''
      },
      sbxls: []
    }
  },
  computed: {
    title () {
      return this.action === 'insert' ? '构件新增' : '构件修改'
    }
  },
  methods: {
    getSbxl () {
      this.sbxls = []
      baseApi.getCommboxById({ id: 'sbxl', sqlParams: { codeClassId: 'asset_sb_fl', parentid: this.form.sbdl } }).then(res => {
        if (res.code === '0000') {
          if (this.action === 'insert') {
            this.form.sbxl = ''
          }
          this.sbxls = res.result
        }
      })
    },
    showModal (record, action) {
      this.visible = true
      this.action = action
      this.form = deepUpdate(
        {
          sbdl: '',
          sbxl: '',
          name: '',
          assetSbItemUuid: ''
        },
        record
      )
      if (action === 'update') {
        this.getSbxl()
      }
    },
    handleOk (e) {
      this.confirmLoading = true
      this.cellConfirm(this.form)
      this.visible = false
    },
    handleCancel (e) {
      this.visible = false
      this.form = {
        sbdl: '',
        sbxl: '',
        name: '',
        assetSbItemUuid: ''
      }
      this.action = ''
    },
    // 获取下拉框选项文本
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || ''
    },
    cellConfirm (record, key) {
      this.confirmLoading = true
      assetSbTemplateApi.modifyAssetSbItem(requestBuilder(this.action, [record])).then(res => {
        if (res.code !== '0000') {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '修改失败！'
          })
        } else {
          this.$notification.success({
            message: '系统消息',
            description: res.message || '修改成功！'
          })
          this.$emit('doSearch')
        }
      }).finally(() => {
        this.confirmLoading = false
      })
    }
  }
}
</script>

<style lang="less" scoped>

</style>
