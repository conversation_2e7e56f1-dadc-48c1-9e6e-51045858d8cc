<template>
  <a-modal
    width="400px"
    :title="modelTitle"
    :visible="hisVisible"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <a-timeline mode="left" v-if="approHisRecord.length > 0">
      <a-timeline-item>
        <div>当前节点:  {{ this.takeSelectLabel(this.status,this.record.status) }}</div>
        <div v-if="![APP_ENUM_CODE_FINISH, APP_ENUM_CODE_CANCEL].includes(this.record.status) > 0">审批人:{{ this.takeSelectLabel(this.assigees,this.record.status === 'approval1009' ? this.record.createBy : this.record.userNext) }}</div>
      </a-timeline-item>
      <a-timeline-item v-for="(item, index) in approHisRecord" :key="index" color="green">
        <div>{{ item.flag }}</div>
        <div>{{ item.msg }}</div>
        <div>{{ item.statusName }}</div>
        <div>{{ item.createBy }}</div>
        <!-- <div>{{ takeSelectLabel(status, item.status) }}</div> -->
        <div>{{ item.createDate }}</div>
      </a-timeline-item>
    </a-timeline>
    <a-empty v-else description="暂无审批历史"/>
    <template slot="footer">
      <a-button type="primary" @click="handleCancel(false)">
        关闭
      </a-button>
    </template>
  </a-modal>
</template>

<script>
import { takeTreeByKey } from '@/utils/util'
import { APP_ENUM_CODE_FINISH, APP_ENUM_CODE_CANCEL, APP_ENUM_CODE_NOSTART } from '@/store/variable-types'
import * as approveApi from '@/api/system/approve'
import * as baseApi from '@/api/system/base'
export default {
  name: 'ApproHisModal',
  data () {
    return {
      APP_ENUM_CODE_NOSTART,
      APP_ENUM_CODE_FINISH,
      APP_ENUM_CODE_CANCEL,
      approHisRecord: [],
      status: [],
      assigees: [],
      modelTitle: '审批历史',
      hisVisible: false
    }
  },
  created () {
    this.initOptions()
  },
  methods: {
    initOptions () {
      // 审批状态
      baseApi.getTreeById({ id: 'appro' }).then(res => {
        if (res.code === '0000') {
          for (const item of res.result) {
            this.status.push(item)
          }
        }
      })
      baseApi.getCommboxById({ id: 'applyPersonWithNum' }).then(res => {
        if (res.code === '0000') {
          this.assigees = res.result
        }
      })
    },
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || ''
    },
    showModal (record) {
      this.record = record
      this.approHisRecord = []
      this.$nextTick(() => {
        approveApi.queryApproHis({ origin: 'assetXxhReceive', businessNum: record.uuid }).then(res => {
          this.approHisRecord = this.handle(res.result)
        })
        this.hisVisible = true
      })
    },
    handle (array) {
      // 插入新增节点
      array.forEach(item => {
        if (item.status === APP_ENUM_CODE_NOSTART) {
          item.statusName = '发起申请'
          item.flag = ''
        }
        if (item.status === APP_ENUM_CODE_CANCEL) {
          item.flag = ''
        }
      })
      return array
    },
    handleCancel (e) {
      this.hisVisible = false
      this.record = {}
    }
  }
}

</script>

<style>

</style>
