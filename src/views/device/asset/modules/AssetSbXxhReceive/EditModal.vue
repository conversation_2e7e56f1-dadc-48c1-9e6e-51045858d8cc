<template>
  <section>
    <a-modal
      width="400px"
      :title="modelTitle"
      :visible="visible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      :maskClosable="false"
      @cancel="handleCancel"
    >
      <a-form
        :form="form"
        ref="ruleForm"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-row>
          <a-col :span="24" v-show="false">
            <a-form-item label="uuid">
              <a-input
                v-decorator="['uuid']"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="配件名称">
              <a-select
                v-decorator="['inventoryUuid', {
                  rules: [{ type: 'array', required: true, message: '配件名称' }],
                  validateTrigger: 'change'
                }]"
                :mode="'multiple'"
                :showArrow="false"
                :open="false"
                class="not-select-search"
                @focus="openMaterialDrawer"
              >
                <a-select-option
                  v-for="(item, index) in materialList"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <MaterialEditModal
            ref="MaterialEditModal"
            :action="action"
            @dataChange="dataChange"
          />
          <!-- <a-col :span="24">
            <a-form-item label="配件型号">
              <a-input
                disabled
                v-decorator="['inventoryModel', {
                  rules: [{ required: true, message: '配件型号' }],
                  validateTrigger: 'change'
                }]"/>
            </a-form-item>
          </a-col> -->
          <!-- <a-col :span="24" v-if="action === 'insert'">
            <a-form-item label="库存数">
              <a-input
                readOnly
                disabled
                type="number"
                v-decorator="['inventoryQty', {
                  rules: [{ required: true, message: '库存数'}, { validator: validatorQty }],
                  validateTrigger: 'change'
                }]"/>
            </a-form-item>
          </a-col> -->
          <!-- <a-col :span="24">
            <a-form-item label="单位">
              <a-select
                :showArrow="false"
                :open="false"
                disabled
                show-search
                optionFilterProp="label"
                v-decorator="['unit', {
                  rules: [{ type: 'string', required: true, message: '请选择单位' }],
                  validateTrigger: 'change'
                }]"
              >
                <a-select-option
                  v-for="(item, index) in units"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col> -->
          <!-- <a-col :span="24">
            <a-form-item label="领用数量">
              <a-input
                type="number"
                v-decorator="['orderQty', {
                  rules: [{ required: true, message: '领用数量'}, { validator: validatorQty }],
                  validateTrigger: 'change'
                }]"/>
            </a-form-item>
          </a-col> -->
          <a-col :span="24">
            <a-form-item label="领用日期">
              <a-date-picker
                disabled
                v-decorator="['receiveDate', {
                  rules: [{ required: true, message: '请选择填写领用日期' }]
                }]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="领用部门">
              <a-select
                disabled
                showSearch
                optionFilterProp="label"
                @change="deptChange"
                v-decorator="['receiveByDept', {
                  rules: [{ type: 'string', required: true, message: '请选择领用部门' }],
                  validateTrigger: 'change'
                }]"
              >
                <a-select-option
                  v-for="(item, index) in depts"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="领用人">
              <a-select
                disabled
                showSearch
                optionFilterProp="label"
                v-decorator="['receiveBy', {
                  rules: [{ type: 'string', required: true, message: '请选择领用人' }],
                  validateTrigger: 'change'
                }]"
              >
                <a-select-option
                  v-for="(item, index) in deptPersons"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <template slot="footer">
        <a-button key="back" @click="handleCancel">
          取消
        </a-button>
        <a-button
          type="primary"
          :loading="confirmLoading"
          @click="handleOk(false)">
          保存
        </a-button>
        <a-button type="primary" :loading="confirmLoading" @click="handleOk(true)" v-show="this.action === 'insert'">
          保存并提交
        </a-button>
      </template>
    </a-modal>
  </section>
</template>

<script>
import {
  // 审批下拉框选项
  APP_ENUM_CODE_NOSTART,
  APP_ENUM_CODE_EDIT_NUM,
  APP_ENUM_CODE_PUBLISH,
  APP_ENUM_CODE_BIANZHI
} from '@/store/variable-types'
import moment from 'moment'
import Vue from 'vue'
import { PERSON_ID, ROLE_NAME, DEPT_ID } from '@/store/mutation-types'
import * as baseApi from '@/api/system/base'
import * as assetXxhReceiveApi from '@/api/device/AssetXxhReceive'
import MaterialEditModal from './MaterialEditModal'
import { requestBuilder } from '@/utils/util'
import { Slider } from 'ant-design-vue'

// 操作人 userId
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)
const USER_DEPT_ID = Vue.ls.get(DEPT_ID)
const USER_ROLE_NAME = Vue.ls.get(ROLE_NAME)
export default {
  name: 'EditModal',
  components: {
    Slider,
    MaterialEditModal
  },
  data () {
    return {
      form: this.$form.createForm(this),
      APP_ENUM_CODE_BIANZHI,
      APP_ENUM_CODE_PUBLISH,
      APP_ENUM_CODE_NOSTART,
      APP_ENUM_CODE_EDIT_NUM,
      visible: false,
      confirmLoading: false,
      inForm: {},
      labelCol: { style: { width: '80px' } },
      wrapperCol: { span: 5 },
      action: 'insert',
      depts: [],
      deptPersons: [],
      operatorStatus: [],
      operatorNatures: [],
      units: [],
      materialList: []
    }
  },
  watch: {
    'form.receiveByDept': {
      deep: true,
      handler () {
        if (this.form.receiveByDept) {
          this.queryPersonByDept(this.form.receiveByDept)
        } else {
          this.deptPersons = []
        }
      }
    }
  },
  created () {
    // 初始化选项获取
    this.initOptions()
  },
  computed: {
    modelTitle () {
      return this.action === 'insert' ? '配件领用新增' : '配件领用修改'
    }
  },
  methods: {
    // 下拉框数据获取
    initOptions () {
      baseApi.getCommboxById({ id: 'dept', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.depts = res.result
        }
      })
      baseApi.getCommboxById({ id: 'unit' }).then(res => {
        if (res.code === '0000') {
          this.units = res.result
        }
      })
      baseApi.getCommboxById({ id: 'applyPerson' }).then(res => {
        if (res.code === '0000') {
          this.deptPersons = res.result
        }
      })
    },
    deptChange (receiveByDept) {
      this.queryPersonByDept(receiveByDept)
      // 清楚表单的用户
      if (this.form) {
        this.form.setFieldsValue({ receiveBy: null })
      }
    },
    handleOk (submit) {
      this.form.validateFields((err, value) => {
        if (!err) {
        } else {
          return setTimeout(() => {
          }, 600)
        }
        if (value) {
          const param = []
          value.inventoryUuid.forEach(item => {
            param.push({
              ...value,
              inventoryUuid: item,
              receiveDate: value.receiveDate ? value.receiveDate.format('YYYY-MM-DD HH:mm:ss') : null,
              inventoryModel: this.materialList.find(info => info.uuid === item).inventoryModel,
              inventoryQty: this.materialList.find(info => info.uuid === item).inventoryQty,
              unit: this.materialList.find(info => info.uuid === item).unit,
              orderQty: this.materialList.find(info => info.uuid === item).orderQty
            })
          })
          this.confirmLoading = true
          assetXxhReceiveApi.modifyAssetXxhReceive(requestBuilder(this.action, param)).then(res => {
            if (res.code !== '0000') {
              this.$notification.error({
                message: '系统消息',
                description: res.message || '系统繁忙！'
              })
              return Promise.reject(res)
            } else {
              this.$notification.success({
                message: '系统消息',
                description: res.message || '操作成功！'
              })
              this.handleCancel()
              this.$emit('doSearch')
              if (submit && res.result) {
                this.$parent.directSubmit(res.result)
              }
            }
          }).finally(res => {
            this.confirmLoading = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleCancel (e) {
      this.materialList = []
      this.visible = false
      this.action = ''
      this.inForm = {}
      this.form.resetFields()
    },
    showModal (action, record = {}) {
      this.visible = true
      this.action = action
      this.inForm = record
      this.$nextTick(() => {
        if (action === 'update') {
          this.form.setFieldsValue({
            ...record,
            receiveDate: record.receiveDate ? moment(record.receiveDate, 'YYYY-MM-DD HH:mm:ss') : null,
            inventoryUuid: [record.inventoryUuid]
          })
          if (record.inventoryUuid) {
            this.materialList.push({
              value: record.inventoryUuid,
              label: record.inventoryName,
              inventoryUuid: record.inventoryUuid,
              inventoryName: record.inventoryName,
              unit: record.unit,
              uuid: record.inventoryUuid,
              inventoryModel: record.inventoryModel,
              inventoryQty: record.inventoryQty,
              orderQty: record.orderQty
            })
          }
          if (this.inForm.receiveByDept) {
            this.queryPersonByDept(this.inForm.receiveByDept)
          }
        } else {
          this.form.setFieldsValue({
            receiveByDept: USER_DEPT_ID,
            receiveBy: USER_PERSON_ID,
            receiveDate: moment(new Date(), 'YYYY-MM-DD HH:mm:ss'),
            inventoryUuid: []
          })
        }
      })
    },
    queryPersonByDept (departmentSysId) {
      baseApi.getCommboxById({ id: 'personByDeptSysId', sqlParams: { departmentSysId } }).then(res => {
        if (res.code === '0000') {
          this.deptPersons = res.result
        }
      })
    },
    allowEditNum (record) {
      if (USER_ROLE_NAME.includes('主管部门调度') || record.receiveBy === USER_PERSON_ID) {
        return false
      }
      if (this.action === 'insert') {
        return false
      }
      return true
    },
    validatorQty (rule, value, callback) {
      const regex = /^[1-9][0-9]*$/
      if (!regex.test(value)) {
        callback(new Error('请输入正确的数量!'))
        return
      }
      callback()
    },
    dataChange (infoList) {
      this.materialList = []
      const inventoryUuids = []
      infoList.forEach(item => {
        this.materialList.push({
          ...item,
          label: item.inventoryName,
          inventoryUuids: item.uuid,
          value: item.uuid
        })
        inventoryUuids.push(item.uuid)
      })
      this.form.setFieldsValue({
        inventoryUuid: inventoryUuids
      })
    },
    openMaterialDrawer () {
      let Array = []
      const list = this.form.getFieldValue('inventoryUuid') || []
      if (list.length > 0) {
        Array = this.materialList.filter(item => list.includes(item.uuid))
      }
      this.$refs.MaterialEditModal.openMaterialDrawer(Array)
    }
  }
}
</script>

<style lang="less" scoped>
  /deep/.ant-form-explain {
    display: none;
  }
</style>
