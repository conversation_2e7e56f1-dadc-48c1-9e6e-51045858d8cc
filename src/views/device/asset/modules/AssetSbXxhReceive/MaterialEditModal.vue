<template>
  <!-- 选择配件列表 -->
  <a-drawer
    :title="materialTitle"
    :visible="materialVisible"
    :maskClosable="false"
    :bodyStyle="{ padding: '120px 20px 10px'}"
    :destroyOnClose="true"
    :width="'850'"
    @close="hiddenMaterialDrawer"
  >
    <div
      class="drawer-body-header"
      :style="{ height: 'auto'}"
    >
      <div
        class="body-header-fixed"
        :style="{ height: 'auto' }"
      >
        <a-form
          layout="inline"
          class="subForm"
        >
          <div style="width: 100%; display: flex;">
            <a-row
              :gutter="materialGrid.gutter"
              style="width: calc(100% - 60px);"
            >
              <a-col
                :xl="materialGrid.xl"
                :md="materialGrid.md"
                :sm="materialGrid.sm"
              >
                <a-form-item label="配件名称">
                  <a-input
                    v-model="materialParam.inventoryName"
                    @pressEnter="doMaterialSearch"
                  />
                </a-form-item>
              </a-col>
              <a-col
                :xl="materialGrid.xl"
                :md="materialGrid.md"
                :sm="materialGrid.sm"
              >
                <a-form-item label="规格型号">
                  <a-input
                    v-model="materialParam.inventoryModel"
                    @pressEnter="doMaterialSearch"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <div
              style="display: flex; align-items: center; margin-left: 10px; width: 50px; border-left: dashed 1px #cfcfcf"
            >
              <a-button
                type="primary"
                style="margin-left: 8px; margin-bottom: 8px"
                @click="doMaterialSearch"
              >查询</a-button>
            </div>
          </div>
        </a-form>
      </div>
    </div>
    <div class="drawer-body-content" style="{padding: 120px}">
      <s-table
        ref="materialTable"
        :data="materialLoadData"
        :columns="materialColumns"
        :scroll="scroll"
        :customRow="materialRowClick"
        :rowSelection="materialRowSelection"
        :clearSelection="materialClearSelection"
        :showPagination="materialShowPagination"
        :pageSizeOptions="materialPageSizeOptions"
        :pageSize="materialDefaultPageSize"
        :rowClassName="materialRowClassName"
        :immediate="materialImmediate"
        :bordered="materialBordered"
        rowKey="uuid"
      >
        <span
          slot="curbal"
          slot-scope="text, record"
        >{{ (text || 0) + takeSelectLabel(units, record.orderUnit) }}</span>
        <span
          slot="usedCoName"
          slot-scope="key"
        >{{ takeSelectLabel(usedCo, key) }}</span>
        <span
          slot="action"
          slot-scope="text, record"
        >
          <template>
            <a
              style="margin: 3px;"
              href="javascript: void(0)"
              @click.stop="() => $refs.appendix.openUploadDrawer(record)"
            >附件</a>
          </template>
        </span>
      </s-table>
      <a-spin :spinning="curbalLoading">
        <div class="selected-material">
          <div class="header">
            <span class="title">已选择的配件</span>
            <a
              class="action"
              href="javascript:void(0)"
              style="float: right; padding-right: 12px; font-size: 12px; color: #f34d4d;"
              @click="doMaterialRemove(cacheMaterials)"
            >全部移除</a>
          </div>
          <s-table
            ref="selectedMaterialTable"
            :data="selectedMaterialLoadData"
            :scroll="scroll"
            :columns="selectedMaterialColumns"
            :customRow="selectedMaterialRowClick"
            :clearSelection="selectedMaterialClearSelection"
            :showPagination="selectedMaterialShowPagination"
            :pageSizeOptions="selectedMaterialPageSizeOptions"
            :pageSize="selectedMaterialDefaultPageSize"
            :rowClassName="selectedMaterialRowClassName"
            :immediate="selectedMaterialImmediate"
            :bordered="selectedMaterialBordered"
            rowKey="uuid"
          >
            <span
              slot="serial"
              slot-scope="text, record, index"
            >{{ index + 1 }}</span>
            <span
              slot="orderQty"
              slot-scope="text, record, index, column"
            >
              <edit-cell-input
                :text="text"
                :status.sync="cellState"
                @change="cellChange(record, column.key, $event)"
                @confirm="cellConfirm(record, column.key)"
              />
            </span>
            <div
              slot="action"
              slot-scope="text, record"
              style="color: #f34d4d; cursor: pointer;"
              @click="doMaterialRemove([record])"
            >移除</div>
          </s-table>
        </div>
      </a-spin>
    </div>
    <div class="drawer-footer">
      <div class="footer-fixed">
        <a-button @click="hiddenMaterialDrawer">取消</a-button>
        <a-button
          type="primary"
          @click="doSelectMaterial"
        >确认</a-button>
      </div>
    </div>
  </a-drawer>
</template>
<script>
import * as assetXxhReceiveApi from '@/api/device/AssetXxhReceive'
import { requestBuilder, deepUpdate } from '@/utils/util'
import { STable, EditCellInput } from '@/components'
export default {
  name: 'MaterialEditModal',
  components: {
    STable,
    EditCellInput
  },
  props: {
    action: {
      type: String,
      default: 'insert'
    }
  },
  data () {
    return {
      // 配件列表 列名
      materialColumns: [
        {
          title: '配件名称',
          dataIndex: 'inventoryName',
          ellipsis: true,
          sorter: true,
          align: 'center',
          width: 100
        },
        {
          title: '规格型号',
          dataIndex: 'inventoryModel',
          ellipsis: true,
          align: 'center',
          width: 100
        },
        {
          title: '库存数',
          dataIndex: 'inventoryQty',
          align: 'center',
          width: 100
        },
        {
          title: '单位',
          dataIndex: 'unitName',
          align: 'center',
          width: 80
        }
      ],
      // 已选择配件 列名
      selectedMaterialColumns: [
        {
          title: '序号',
          dataIndex: 'serial',
          scopedSlots: { customRender: 'serial' },
          align: 'center',
          width: 50
        },
        {
          title: '配件名称',
          dataIndex: 'inventoryName',
          ellipsis: true,
          sorter: true,
          align: 'center',
          width: 120
        },
        {
          title: '规格型号',
          dataIndex: 'inventoryModel',
          ellipsis: true,
          align: 'center',
          width: 120
        },
        {
          title: '库存数',
          dataIndex: 'inventoryQty',
          align: 'center',
          width: 120
        },
        {
          title: '领用数量',
          dataIndex: 'orderQty',
          scopedSlots: { customRender: 'orderQty' },
          align: 'center',
          width: 120
        },
        {
          title: '单位',
          dataIndex: 'unitName',
          align: 'center',
          width: 120
        },
        {
          title: '操作',
          key: 'action',
          scopedSlots: { customRender: 'action' },
          align: 'center',
          fixed: 'right',
          width: 120
        }
      ],
      // 配件选项 - 排版
      materialGrid: {
        xl: 8,
        md: 8,
        sm: 8,
        gutter: 10
      },
      // 配件选项 - 参数
      materialParam: {
        inventoryName: '',
        inventoryModel: ''
      },
      materials: [],
      // 新增模块工程描述列表
      // desTitle: {},
      // 表格配置 - 配件列表
      materialRowSelection: {
        type: 'radio',
        onChange: this.materialSelectChange
      },
      scroll: {
        x: '100%',
        y: '40vh',
        scrollToFirstRowOnChange: false
      },
      materialTitle: '',
      materialDataSource: [],
      materialSelectedRows: [],
      materialSelectedRowKeys: [],
      materialPageSizeOptions: ['3', '15', '20', '25', '30', '50'],
      materialDefaultPageSize: 15,
      materialClearSelection: false,
      materialShowPagination: true,
      materialImmediate: true,
      materialBordered: false,
      curbalLoading: false,
      materialVisible: false,
      materialLoadData: parameter => {
        const materialParam = {
          ...this.materialParam,
          pageTag: true
        }
        const param = requestBuilder(
          '',
          deepUpdate(
            {
              inventoryName: '',
              inventoryModel: '',
              pageTag: true
            },
            materialParam
          ),
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder
        )
        return assetXxhReceiveApi.queryAssetXxhInventory(param).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '配件列表获取失败！'
            })
            return Promise.reject(res)
          }
          this.materialDataSource = res.result.data || []
          this.materialSelectedRows = this.materialDataSource.filter(item =>
            this.cacheMaterials.some(item2 => item2.uuid === item.uuid)
          )
          this.materialSelectedRowKeys = this.materialSelectedRows.map(item => item.uuid)
          this.$refs.materialTable.triggerSelect(this.materialSelectedRowKeys, this.materialSelectedRows)
          return res.result
        })
      },
      materialRowClick: record => ({
        on: {
          click: () => {
            if (this.materialRowSelection.type === 'radio') {
              this.$refs.materialTable.triggerSelect([record.uuid], [record])
            }
            if (this.materialRowSelection.type === 'checkbox') {
              if (!this.materialSelectedRowKeys.includes(record.uuid)) {
                this.materialSelectedRowKeys.push(record.uuid)
                this.materialSelectedRows.push(record)
              } else {
                this.materialSelectedRows = this.materialSelectedRows.filter(
                  item => item.uuid !== record.uuid
                )
                this.materialSelectedRowKeys = this.materialSelectedRowKeys.filter(
                  item => item !== record.uuid
                )
              }
              this.$refs.materialTable.triggerSelect(this.materialSelectedRowKeys, this.materialSelectedRows)
            }
          }
        }
      }),
      materialRowClassName: () => {
        return 'cursor-pointer'
      },
      // 已选择的配件
      selectedMaterialRowSelection: {
        type: 'checkbox'
      },
      selectedMaterialSelectedRows: [],
      selectedMaterialSelectedRowKeys: [],
      selectedMaterialPageSizeOptions: ['10', '15', '20', '25', '30', '50'],
      selectedMaterialDefaultPageSize: 15,
      selectedMaterialClearSelection: false,
      selectedMaterialShowPagination: false,
      selectedMaterialImmediate: false,
      selectedMaterialBordered: false,
      selectedMaterialLoadData: parameter => {
        return Promise.resolve(this.cacheMaterials)
      },
      selectedMaterialRowClick: record => ({
        on: {}
      }),
      selectedMaterialRowClassName: () => {
        return ''
      }
    }
  },
  methods: {
    // materialTable 搜索事件
    doMaterialSearch () {
      this.$refs.materialTable.refresh(true)
    },
    // materialTable 移除已选择
    doMaterialRemove (records) {
      records.forEach(record => {
        this.cacheMaterials = this.cacheMaterials.filter(item => record.uuid !== item.uuid)
        this.materialSelectedRows = this.materialSelectedRows.filter(
          item => record.uuid !== item.uuid
        )
        this.materialSelectedRowKeys = this.materialSelectedRows.map(item => item.uuid)
        this.$refs.materialTable.triggerSelect(this.materialSelectedRowKeys, this.materialSelectedRows)
      })
    },
    // 打开配件弹框
    openMaterialDrawer (infoList) {
      this.materials = infoList
      this.materialVisible = true
      this.materialSelectedRows = []
      this.materialSelectedRowKeys = []
      this.materialRowSelection.type = this.action === 'insert' ? 'radio' : 'radio'
      this.materialTitle = '配件列表'
      this.cacheMaterials = [...this.materials]
    },
    // 关闭配件弹框
    hiddenMaterialDrawer () {
      this.materialParam = {
        inventoryName: '',
        inventoryModel: ''
      }
      this.materialVisible = false
      this.materialSelectedRows = []
      this.materialSelectedRowKeys = []
      this.materialRowSelection.type = 'radio'
      if (this.procureLineType !== '27') {
        this.cacheMaterials = []
      }
      this.materialTitle = ''
    },
    // 选择配件列表
    doSelectMaterial () {
      if (this.cacheMaterials.length === 0) {
        this.$message.error('请选择配件！')
        return
      }
      this.$emit('dataChange', this.cacheMaterials)
      this.hiddenMaterialDrawer()
    },
    // materialTable 勾选更改
    materialSelectChange (selectedRowKeys, selectedRows) {
      this.materialSelectedRowKeys = selectedRowKeys
      this.materialSelectedRows = selectedRows
      for (const item of this.materialDataSource) {
        const selected = this.materialSelectedRowKeys.includes(item.uuid)
        const existed = this.materialDataSource.some(record => record.uuid === item.uuid)
        const index = this.cacheMaterials.findIndex(item2 => item2.uuid === item.uuid)
        if (selected && index === -1) {
          if (this.materialRowSelection.type === 'checkbox') {
            this.cacheMaterials.push({
              inventoryName: item.inventoryName,
              inventoryModel: item.inventoryModel,
              inventoryQty: item.inventoryQty,
              orderQty: 0,
              unit: item.unit,
              unitName: item.unitName,
              uuid: item.uuid,
              inventoryUuid: item.uuid
            })
          }
          if (this.materialRowSelection.type === 'radio') {
            this.cacheMaterials = [
              {
                inventoryName: item.inventoryName,
                inventoryModel: item.inventoryModel,
                inventoryQty: item.inventoryQty,
                orderQty: 0,
                unit: item.unit,
                unitName: item.unitName,
                uuid: item.uuid,
                inventoryUuid: item.uuid
              }
            ]
          }
        }
        if (index > -1) {
          if (!selected && existed) {
            this.cacheMaterials.splice(index, 1)
          }
        }
        if (this.$refs.selectedMaterialTable) {
          this.$refs.selectedMaterialTable.refresh(true)
        }
      }
    },
    cellChange (record, key, value) {
      const event = value
      const regex = /^\d+(\.\d*)?$/
      if (regex.test(event.value)) {
        event.value = +event.value
        value = event.value || 0
      } else if (!event.value.trim()) {
        event.value = ''
        value = 0
      } else {
        value = event.value = record[key] || 0
      }
      this.$set(record, key, value)
    },
    cellConfirm (record, key) {
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep {
  .linkRowStyle {
    background-color: #f7d3d2;
  }
  .ant-table-footer {
  padding: 8px 50px;
  .footer-group {
    display: flex;
    flex-flow: row nowrap;
    justify-content: center;
    & > .footer-item {
      flex: 0 0 auto;
      margin: 0 30px;
      font-size: 12px;
      color: #606266;
    }
  }
  }
  .border-only-bottom {
    .ant-select-selection {
      border: none;
      border-bottom: 1px solid #d9d9d9;
      border-radius: initial;
      box-shadow: none;
      &:focus,
      &:active {
        box-shadow: none;
      }
    }
  }
  .subForm {
    &.ant-form-inline .ant-form-item > .ant-form-item-label {
      width: 65px;
    }
    &.ant-form-inline .ant-form-item > .ant-form-item-control-wrapper {
      width: calc(100% - 65px);
    }
  }
//   .ant-table-body {
//   &::-webkit-scrollbar {//整体样式
//           height:15px; // 此处的important表示优先于element.style
//         }
// }
}
.drawer-body-header {
  width: 100%;
  .body-header-fixed {
    width: calc(100% - 45px);
    padding-top: 10px;
    margin-bottom: 10px;
    position: absolute;
    top: 55px;
    border-bottom: dashed 1px #cfcfcf;
    background-color: #ffffff;
    z-index: 3;
    box-sizing: border-box;
  }
}
.drawer-body-content {
  .selected-material {
    margin-top: 15px;
    .header {
      height: 32px;
      font-size: 14px;
      padding-left: 8px;
      line-height: 32px;
    }
  }
}
.spanTitle {
  font-weight: 700;
}
a.disabled {
    pointer-events: none;
    color: #999;
    -moz-opacity: 0.5;
    opacity: 0.5;
}
.popStyle {
  width: 500px;
}
.ant-divider-horizontal {
  margin: 0;
}
</style>
