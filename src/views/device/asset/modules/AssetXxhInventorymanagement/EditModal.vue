<template>
  <section>
    <a-modal
      width="400px"
      :title="modelTitle"
      :visible="visible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      :maskClosable="false"
      @cancel="handleCancel"
    >
      <a-form
        :form="form"
        ref="ruleForm"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-row>
          <a-col :span="24">
            <a-form-item label="配件名称">
              <a-input
                v-decorator="['inventoryName', {
                  rules: [{ required: true, message: '配件名称' }],
                  validateTrigger: 'change'
                }]"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="规格型号">
              <a-input
                v-decorator="['inventoryModel', {
                  rules: [{ required: true, message: '配件型号' }],
                  validateTrigger: 'change'
                }]"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="库存数">
              <a-input
                type="number"
                v-decorator="['inventoryQty', {
                  rules: [{ required: true, message: '库存数'}, { validator: validatorQty }],
                  validateTrigger: 'change'
                }]"/>
            </a-form-item>
          </a-col>
          <!-- <a-col :span="24">
            <a-form-item label="领用日期">
              <a-date-picker
                v-decorator="['receiveDate', {
                  rules: [{ required: true, message: '请选择填写领用日期' }]
                }]"
              />
            </a-form-item>
          </a-col> -->
          <!-- <a-col :span="24">
            <a-form-item label="领用部门">
              <a-select
                disabled
                showSearch
                optionFilterProp="label"
                @change="deptChange"
                v-decorator="['receiveByDept', {
                  rules: [{ type: 'string', required: true, message: '请选择领用部门' }],
                  validateTrigger: 'change'
                }]"
              >
                <a-select-option
                  v-for="(item, index) in depts"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>-->
          <a-col :span="24">
            <a-form-item label="单位">
              <a-select
                showSearch
                optionFilterProp="label"
                v-decorator="['unit', {
                  rules: [{ type: 'string', required: true, message: '请选择单位' }],
                  validateTrigger: 'change'
                }]"
              >
                <a-select-option
                  v-for="(item, index) in units"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                >{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </section>
</template>

<script>
import {
  // 审批下拉框选项
  APP_ENUM_CODE_NOSTART,
  APP_ENUM_CODE_EDIT_NUM,
  APP_ENUM_CODE_PUBLISH,
  APP_ENUM_CODE_BIANZHI
} from '@/store/variable-types'
// import moment from 'moment'
import Vue from 'vue'
import { PERSON_ID, ROLE_NAME, DEPT_ID } from '@/store/mutation-types'
import * as baseApi from '@/api/system/base'
import * as assetXxhReceiveApi from '@/api/device/AssetXxhReceive'
import { requestBuilder } from '@/utils/util'
import { Slider } from 'ant-design-vue'
// 操作人 userId
const USER_PERSON_ID = Vue.ls.get(PERSON_ID)
const USER_DEPT_ID = Vue.ls.get(DEPT_ID)
const USER_ROLE_NAME = Vue.ls.get(ROLE_NAME)
export default {
  name: 'EditModal',
  components: {
    Slider
  },
  data () {
    return {
      USER_PERSON_ID,
      USER_DEPT_ID,
      USER_ROLE_NAME,
      form: this.$form.createForm(this),
      APP_ENUM_CODE_BIANZHI,
      APP_ENUM_CODE_PUBLISH,
      APP_ENUM_CODE_NOSTART,
      APP_ENUM_CODE_EDIT_NUM,
      visible: false,
      confirmLoading: false,
      inForm: {},
      labelCol: { style: { width: '80px' } },
      wrapperCol: { span: 5 },
      action: 'insert',
      depts: [],
      deptPersons: [],
      operatorStatus: [],
      operatorNatures: [],
      materials: [],
      units: []
    }
  },
  watch: {
    'form.receiveByDept': {
      deep: true,
      handler () {
        if (this.form.receiveByDept) {
          this.queryPersonByDept(this.form.receiveByDept)
        } else {
          this.deptPersons = []
          //  this.queryPersonByDept(this.form.departmentSysId)
        }
      }
    }
  },
  created () {
    // 初始化选项获取
    this.initOptions()
  },
  computed: {
    modelTitle () {
      return this.action === 'insert' ? '配件库存新增' : '配件库存修改'
    }
  },
  methods: {
    // 下拉框数据获取
    initOptions () {
      baseApi.getCommboxById({ id: 'dept', sqlParams: { isAll: '1' } }).then(res => {
        if (res.code === '0000') {
          this.depts = res.result
        }
      })
      baseApi.getCommboxById({ id: 'applyPerson' }).then(res => {
        if (res.code === '0000') {
          this.deptPersons = res.result
        }
      })
      baseApi.getCommboxById({ id: 'unit' }).then(res => {
        if (res.code === '0000') {
          this.units = res.result
        }
      })
    },
    deptChange (receiveByDept) {
      this.queryPersonByDept(receiveByDept)
      // 清楚表单的用户
      if (this.form) {
        this.form.setFieldsValue({ receiveBy: null })
      }
    },
    handleOk (e) {
      e.preventDefault()
      this.form.validateFields((err, value) => {
        if (!err) {
        } else {
          return setTimeout(() => {
          }, 600)
        }
        if (value) {
          const param =
            {
              ...value,
              uuid: this.inForm.uuid
            }
          this.confirmLoading = true
          assetXxhReceiveApi.modifyAssetXxhInventory(requestBuilder(this.action, [param])).then(res => {
            if (res.code !== '0000') {
              this.$notification.error({
                message: '系统消息',
                description: res.message || '系统繁忙！'
              })
              return Promise.reject(res)
            } else {
              this.$notification.success({
                message: '系统消息',
                description: res.message || '操作成功！'
              })
              this.handleCancel()
              this.$emit('doSearch')
            }
          }).finally(() => {
            this.confirmLoading = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleCancel (e) {
      this.visible = false
      this.action = ''
      this.inForm = {}
      this.form.resetFields()
    },
    showModal (action, record = {}) {
      this.visible = true
      this.action = action
      this.inForm = record
      this.$nextTick(() => {
        if (action === 'update') {
          this.form.setFieldsValue({
            ...record
          })
          // if (this.inForm.receiveByDept) {
          //   this.queryPersonByDept(this.inForm.receiveByDept)
          // }
        }
        // else {
        //   this.form.setFieldsValue({
        //     receiveByDept: USER_DEPT_ID,
        //     receiveBy: USER_PERSON_ID,
        //     receiveDate: moment(new Date(), 'YYYY-MM-DD HH:mm:ss')
        //   })
        // }
      })
    },
    queryPersonByDept (departmentSysId) {
      baseApi.getCommboxById({ id: 'personByDeptSysId', sqlParams: { departmentSysId } }).then(res => {
        if (res.code === '0000') {
          this.deptPersons = res.result
        }
      })
    },
    allowEditNum (record) {
      if (USER_ROLE_NAME.includes('主管部门调度') || record.receiveBy === USER_PERSON_ID) {
        return false
      }
      if (this.action === 'insert') {
        return false
      }
      return true
    },
    validatorQty (rule, value, callback) {
      const regex = /^[1-9][0-9]*$/
      if (!regex.test(value)) {
        callback(new Error('请输入正确的数量!'))
        return
      }
      callback()
    }
    // async searchVendor (value, key) {
    //   const obj = {
    //     activity: 'Y'
    //   }
    //   obj[key] = value
    //   const param = requestBuilder('', obj, 1, 15)
    //   const res = await vendorApi.queryVendorInfo(param)
    //   const data = res.result.data.map(item => {
    //     return {
    //       label: item.vendorName,
    //       value: item.vendorSysId
    //     }
    //   })
    //   const arr = this.unique([...data, ...this.vendorList])
    //   this.vendors = arr || []
    //   if (key === 'vendorCode') {
    //     this.vendorList = this.unique([...arr, ...this.vendorList])
    //   }
    // },
    // async searchAgent (value, key) {
    //   const obj = {
    //     activity: 'Y'
    //   }
    //   obj[key] = value
    //   const param = requestBuilder('', obj, 1, 15)
    //   const res = await baseApi.getPersonInfo(param)
    //   const data = res.result.data.map(item => {
    //     return {
    //       label: item.personName,
    //       value: item.personSysId
    //     }
    //   })
    //   const arr = this.unique([...data, ...this.agentList])
    //   this.agents = arr || []
    //   if (key === 'agent') {
    //     this.agentList = this.unique([...arr, ...this.agentList])
    //   }
    // },
  }
}
</script>

<style lang="less" scoped>
  /deep/.ant-form-explain {
    display: none;
  }
</style>
