// helper: validator and extender
import { extender } from '@/components/Form/helper'

// 基本信息
const FORM_BASE = [
  {
    type: 'AGroup',
    slot: 'title1',
    field: 'title1',
    title: '基本信息',
    attrs: {
      style: 'margin-bottom: 6px'
    },
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'createByName',
    field: 'createByName',
    label: '创建人',
    decorator: {},
    attrs: {
      disabled: false
    }
  },
  {
    type: 'AInput',
    slot: 'createDate',
    field: 'createDate',
    label: '创建日期',
    decorator: {},
    attrs: {
      disabled: true
    }
  },
  {
    type: 'AInput',
    slot: 'assetSoftwareNum',
    field: 'assetSoftwareNum',
    label: '系统编码',
    decorator: {},
    attrs: {
      disabled: true
    }
  },
  {
    type: 'AInput',
    slot: 'phone',
    field: 'phone',
    label: '联系电话',
    decorator: {},
    attrs: {
      disabled: true
    }
  }
]

// 详细信息
const FORM_MORE = [
  {
    type: 'AGroup',
    slot: 'title2',
    field: 'title2',
    title: '详细信息',
    attrs: {
      style: 'margin-bottom: 6px'
    },
    grid: {}
  },
  {
    type: 'ASelect',
    slot: 'assetSoftwareDl',
    field: 'assetSoftwareDl',
    label: '软件大类',
    decorator: {
      rules: [{ type: 'string', required: true, message: '请选择软件大类' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'ASelect',
    slot: 'assetSoftwareXl',
    field: 'assetSoftwareXl',
    label: '软件子类',
    decorator: {
      rules: [{ type: 'string', required: true, message: '请选择软件子类' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'assetSoftwareName',
    field: 'assetSoftwareName',
    label: '软件名称',
    decorator: {
      rules: [{ type: 'string', required: true, message: '请输入软件名称' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'ASelect',
    slot: 'sysStatus',
    field: 'sysStatus',
    label: '当前状态',
    decorator: {
      rules: [{ type: 'string', required: true, message: '请输入当前状态' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'ADatePicker',
    slot: 'onlineTine',
    field: 'onlineTine',
    label: '上线时间',
    decorator: {
      rules: [{ required: true, message: '请选择上线时间' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'ASelect',
    slot: 'ssdw',
    field: 'ssdw',
    label: '所属单位',
    decorator: {
      rules: [{ type: 'string', required: true, message: '请选择所属单位' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'useUnits',
    field: 'useUnits',
    label: '使用单位',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'maintenanceUnit',
    field: 'maintenanceUnit',
    label: '维护单位',
    decorator: {
      rules: [{ type: 'string', required: true, message: '请输入维护单位' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'propertyUnit',
    field: 'propertyUnit',
    label: '产权单位',
    decorator: {
      rules: [{ type: 'string', required: true, message: '请输入产权单位' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'personInCharge',
    field: 'personInCharge',
    label: '责任人',
    decorator: {
      rules: [{ type: 'string', required: true, message: '请输入责任人' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'annualMaintenanceFee',
    field: 'annualMaintenanceFee',
    label: '年度运维费',
    decorator: {
      rules: [{ required: true, message: '请输入年度运维费' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'investment',
    field: 'investment',
    label: '投资金额(万元)',
    decorator: {
      rules: [{ required: true, message: '请输入投资金额' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'constructionUnit',
    field: 'constructionUnit',
    label: '建设/合作单位',
    decorator: {
      rules: [{ type: 'string', required: true, message: '请输入建设/合作单位' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'constructionMode',
    field: 'constructionMode',
    label: '建设方式',
    decorator: {
      rules: [{ type: 'string', required: true, message: '请输入建设方式' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'unit',
    field: 'unit',
    label: '计量单位',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'useEnvironment',
    field: 'useEnvironment',
    label: '系统使用环境',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'getWay',
    field: 'getWay',
    label: '获得',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'version',
    field: 'version',
    label: '版本',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'datebaseType',
    field: 'datebaseType',
    label: '数据库类型',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'backupMechanisms',
    field: 'backupMechanisms',
    label: '备份机制',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'wharfManage',
    field: 'wharfManage',
    label: '码头生产管理领域',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'finDepreciationPeriod',
    field: 'finDepreciationPeriod',
    label: '财务折旧年限',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'usedfor',
    field: 'usedfor',
    label: '用途',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'loadmain',
    field: 'loadmain',
    label: '承载主机',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'loadmainIp',
    field: 'loadmainIp',
    label: 'ip',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'loadmainPort',
    field: 'loadmainPort',
    label: '端口',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'ASelect',
    slot: 'isConnectWAN',
    field: 'isConnectWAN',
    label: '是否连接外网',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'ATextarea',
    slot: 'busiDescription',
    field: 'busiDescription',
    label: '业务说明',
    decorator: {},
    attrs: {
      rows: 3
    },
    grid: {
      xs: 12
    }
  },
  {
    type: 'AInput',
    slot: 'databasePortSer',
    field: 'databasePortSer',
    label: '端口及对应服务',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'userGroup',
    field: 'userGroup',
    label: '用户群体',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'userNum',
    field: 'userNum',
    label: '用户数量',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'publicNetwork',
    field: 'publicNetwork',
    label: '公网/内网',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'ATextarea',
    slot: 'remark',
    field: 'remark',
    label: '说明/备注',
    decorator: {},
    attrs: {
      rows: 3
    },
    grid: {
      xs: 8,
      newline: true
    }
  }
]

// 导出配置
export default {
  data () {
    // 处理
    const groups = [
      ...FORM_BASE,
      ...FORM_MORE
    ]

    return {
      // 布局
      grid: {
        gutter: 10,
        xs: 4
      },

      // 属性
      attrs: {
        labelAlign: 'right'
      },

      // 监听
      watch: {
        onValuesChange (props, values) {
          // 创建更改区
          const changed = {}

          // 监听大类
          if (values.assetSoftwareDl) {
            const assetSoftwareDl = values.assetSoftwareDl
            const assetSoftwareXlArr = this.options.assetSoftwareXl.selectOptions
            const assetSoftwareXl = this.form.getFieldValue('assetSoftwareXl')
            if (
              assetSoftwareDl &&
              !assetSoftwareXlArr.some(item => assetSoftwareXl === item.value && assetSoftwareDl === item.valueParent)
            ) {
              this.form.setFieldsValue({ assetSoftwareXl: '' })
            }
          }

          // 是否进行赋值 (一次性赋值，提升性能)
          if (Object.keys(changed).length > 0) {
            this.setFieldsValue(changed)
          }
        }
      },

      // 配置
      groups: [
        ...extender.groups(groups, (group, index, groups) => {
          // 完善 groups
        })
      ],

      // 扩展
      options: {
        ...extender.options(groups, (group, index, groups) => {
          // 默认值
          const option = {}

          // 下拉框处理
          if (group.type === 'ASelect') {
            Object.assign(option, {
              selectOptions: []
            })
          }

          // 当前状态
          if (group.field === 'sysStatus') {
            Object.assign(option, {
              selectOptions: this.queryOptions.sysStatus || []
            })
          }

          // 信息化大类
          if (group.field === 'assetSoftwareDl') {
            Object.assign(option, {
              selectOptions: this.queryOptions.assetSoftwareDl || []
            })
            group.attrs.disabled = () => !this.isAdd
          }
          // 是否连接外网
          if (group.field === 'isConnectWAN') {
            Object.assign(option, {
              selectOptions: [{ 'label': '是', 'value': 'Y' }, { 'label': '否', 'value': 'N' }]
            })
            // group.attrs.disabled = () => !this.isAdd
          }

          // 信息化子类
          if (group.field === 'assetSoftwareXl') {
            Object.assign(option, {
              selectOptionsRender (options, { Utils, form }) {
                return options.filter(opt => {
                  const assetSoftwareDl = form.getFieldValue('assetSoftwareDl')
                  return !assetSoftwareDl || opt.valueParent === assetSoftwareDl
                })
              },
              selectOptions: this.queryOptions.assetSoftwareXl || []
            })
            group.attrs.disabled = () => !this.isAdd
          }

          // 所属单位
          if (group.field === 'ssdw') {
            Object.assign(option, {
              selectOptions: this.queryOptions.ssdws || []
            })
          }

          // 应用软件 soft_dl_01
          // 数据库 soft_xl_03
          // 移动应用 soft_xl_13
          // 微信公众号 soft_xl_14

          // 用途
          if (group.field === 'usedfor') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetSoftwareDl = form.getFieldValue('assetSoftwareDl')
                const assetSoftwareXl = form.getFieldValue('assetSoftwareXl')
                return (
                  ['soft_dl_01'].includes(assetSoftwareDl) ||
                  ['soft_xl_03', 'soft_xl_13', 'soft_xl_14'].includes(assetSoftwareXl)
                )
              }
            })
          }

          // 承载主机
          if (group.field === 'loadmain') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetSoftwareDl = form.getFieldValue('assetSoftwareDl')
                const assetSoftwareXl = form.getFieldValue('assetSoftwareXl')
                return (
                  ['soft_dl_01'].includes(assetSoftwareDl) ||
                  ['soft_xl_03', 'soft_xl_13', 'soft_xl_14'].includes(assetSoftwareXl)
                )
              }
            })
          }

          // 承载主机ip
          if (group.field === 'loadmainIp') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetSoftwareDl = form.getFieldValue('assetSoftwareDl')
                const assetSoftwareXl = form.getFieldValue('assetSoftwareXl')
                return (
                  ['soft_dl_01'].includes(assetSoftwareDl) ||
                  ['soft_xl_03', 'soft_xl_13', 'soft_xl_14'].includes(assetSoftwareXl)
                )
              }
            })
          }
          if (group.field === 'loadmainPort') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetSoftwareDl = form.getFieldValue('assetSoftwareDl')
                const assetSoftwareXl = form.getFieldValue('assetSoftwareXl')
                return (
                  ['soft_dl_01'].includes(assetSoftwareDl) ||
                  ['soft_xl_03', 'soft_xl_13', 'soft_xl_14'].includes(assetSoftwareXl)
                )
              }
            })
          }
          // if (group.field === 'isConnectWAN') {
          //   Object.assign(option, {
          //     handleRender (item, { Utils, form }) {
          //       const assetSoftwareDl = form.getFieldValue('assetSoftwareDl')
          //       const assetSoftwareXl = form.getFieldValue('assetSoftwareXl')
          //       return (
          //         ['soft_dl_01'].includes(assetSoftwareDl) ||
          //         ['soft_xl_03', 'soft_xl_13', 'soft_xl_14'].includes(assetSoftwareXl)
          //       )
          //     }
          //   })
          // }
          if (group.field === 'busiDescription') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetSoftwareDl = form.getFieldValue('assetSoftwareDl')
                const assetSoftwareXl = form.getFieldValue('assetSoftwareXl')
                return (
                  ['soft_dl_01'].includes(assetSoftwareDl) ||
                  ['soft_xl_03', 'soft_xl_13', 'soft_xl_14'].includes(assetSoftwareXl)
                )
              }
            })
          }
          if (group.field === 'loadmainPort') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetSoftwareDl = form.getFieldValue('assetSoftwareDl')
                const assetSoftwareXl = form.getFieldValue('assetSoftwareXl')
                return (
                  ['soft_dl_01'].includes(assetSoftwareDl) ||
                  ['soft_xl_03', 'soft_xl_13', 'soft_xl_14'].includes(assetSoftwareXl)
                )
              }
            })
          }

          // 承载主机系统
          if (group.field === 'loadmainsystem') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetSoftwareXl = form.getFieldValue('assetSoftwareXl')
                return (
                  ['soft_xl_03'].includes(assetSoftwareXl)
                )
              }
            })
          }

          // 端口及对应服务
          if (group.field === 'databasePortSer') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetSoftwareXl = form.getFieldValue('assetSoftwareXl')
                return (
                  ['soft_xl_03'].includes(assetSoftwareXl)
                )
              }
            })
          }

          // 用户群体
          if (group.field === 'userGroup') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetSoftwareXl = form.getFieldValue('assetSoftwareXl')
                return (
                  ['soft_xl_13', 'soft_xl_14'].includes(assetSoftwareXl)
                )
              }
            })
          }

          // 用户数量
          if (group.field === 'userNum') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetSoftwareXl = form.getFieldValue('assetSoftwareXl')
                return (
                  ['soft_xl_13', 'soft_xl_14'].includes(assetSoftwareXl)
                )
              }
            })
          }

          // 公网/内网
          if (group.field === 'publicNetwork') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetSoftwareXl = form.getFieldValue('assetSoftwareXl')
                return (
                  ['soft_xl_13', 'soft_xl_14'].includes(assetSoftwareXl)
                )
              }
            })
          }

          return option
        })
      },

      // 禁用
      disabled: false,

      // 只读
      readonly: false,

      // 查询中
      spinning: false
    }
  }
}
