<template>
  <section>
    <a-spin :spinning="fileListLoading">
      <a-upload
        :multiple="true"
        :data="uploadData"
        :action="uploadAction"
        :headers="uploadHeaders"
        :file-list="uploadFileList"
        :beforeUpload="uploadBeforeUpload"
        :disabled="action === 'insert' || disabled || readonly"
        :remove="uploadRemove"
        @change="uploadChange"
        @preview="uploadPreview"
      >
        整机图片
        <a-button v-if="action !== 'insert' && !disabled && !readonly">
          <a-icon type="upload" />上传
        </a-button>
      </a-upload>
    </a-spin>
  </section>
</template>

<script>
import Vue from 'vue'
import { Base64 } from 'js-base64'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import * as fileApi from '@/api/system/file'

// 上传附件允许类型
const uploadAccept = [
  // Excel
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel',
  // WORD
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
  'application/vnd.ms-word.document.macroEnabled.12',
  'application/vnd.ms-word.template.macroEnabled.12',
  'application/msword',
  // PDF
  'application/pdf',
  // IMG
  'image/png',
  'image/jpeg',
  'image/bmp',
  'image/gif'
]

// 上传接口
const TOKEN = Vue.ls.get(ACCESS_TOKEN)
const BASE_URL = process.env.VUE_APP_API_BASE_URL
const BASE_PREVIEW_URL = process.env.VUE_APP_PREVIEW_BASE_URL
const VUE_APP_UPLOAD_PREVIEW_URL = process.env.VUE_APP_UPLOAD_PREVIEW_URL

export default {
  name: 'InfoUpload',
  props: {
    readonly: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      // 上传参数
      keyId: '',
      action: '',
      businessName: 'infor',
      uploadData: {},
      uploadFileList: [],
      uploadAction: BASE_URL + '/file/fileUpload',
      uploadAccept: uploadAccept.join(','),
      uploadHeaders: { token: TOKEN },
      fileListLoading: false
    }
  },
  methods: {
    queryUploadFiles () {
      if (!this.fileListLoading && this.keyId && this.businessName) {
        // 处理查询参数
        const keyId = this.keyId
        const businessName = this.businessName

        // 查询已上传文件
        fileApi
          .listFiles({ keyId, businessName })
          .then(res => {
            if (res.code !== '0000') {
              this.$notification.error({
                message: '系统消息',
                description: res.message || '获取附件列表失败！'
              })
              return Promise.reject(res)
            }
            for (const item of res.result) {
              this.uploadFileList.push({
                uid: item,
                url: item.replace(/_[^_]+$/, ''),
                name: item.replace(/_[^_]+$/, ''),
                status: 'done'
              })
            }
          })
          .finally(() => {
            this.fileListLoading = false
          })

        // 正在查询中
        this.fileListLoading = true
      }
    },
    uploadBeforeUpload (file) {
      if (!uploadAccept.includes(file.type)) {
        this.$message.error('仅支持上传word、excel、图片等附件')
        return false
      }
      if (file.size / 1024 / 1024 > 10) {
        this.$message.error('上传附件大小不可超过10M')
        return false
      }
      this.uploadFileList.push(file)
    },
    uploadChange (options) {
      if (options.file && ['removed', 'error'].includes(options.file.status)) {
        this.uploadFileList = this.uploadFileList.filter(file => {
          return file.uid !== options.file.uid
        })
      }
      if (options.file && options.file.status === 'done') {
        if (options.file.response && options.file.response.code === '0000') {
          this.uploadFileList = this.uploadFileList.map(file => {
            if (file.uid === options.file.uid) {
              file.url = options.file.response.message
              file.uid = options.file.response.message
              file.status = options.file.status
            }
            return file
          })
        } else {
          this.uploadFileList = this.uploadFileList.filter(file => {
            return file.uid !== options.file.uid
          })
          this.$notification.error({
            message: '系统消息',
            description: '上传附件失败！'
          })
        }
      }
    },
    uploadPreview (file) {
      // 处理参数
      const fileName = file.uid || ''
      const keyId = this.uploadData.params.split('_')[1] || ''
      const businessName = this.uploadData.params.split('_')[0] || ''
      this.$confirm({
        content: '打开方式',
        cancelText: '预览',
        okText: '下载',
        onOk () {
          window.open(`${BASE_URL}/file/fileDownload?keyId=${keyId}&businessName=${businessName}&fileName=${fileName}`)
        },
        onCancel () {
          let previewUrl = ''
          const originUrl = `${BASE_PREVIEW_URL}/file/fileDownload?keyId=${keyId}&businessName=${businessName}&fileName=${fileName}`
          if (/^\/.+/.test(originUrl)) {
            previewUrl = `${window.location.protocol}//${window.location.host}${originUrl}&fullfilename=${fileName}`
          } else {
            previewUrl = `${originUrl}&fullfilename=${fileName}`
          }
          window.open(`${VUE_APP_UPLOAD_PREVIEW_URL}/onlinePreview/onlinePreview?url=${encodeURIComponent(Base64.encode(previewUrl))}`)
        }
      })
    },
    uploadRemove (file) {
      return new Promise((resolve, reject) => {
        this.$confirm({
          title: '确认?',
          content: '是否删除该附件',
          onOk: () => {
            // 处理参数
            const fileName = file.uid || ''
            const keyId = this.uploadData.params.split('_')[1] || ''
            const businessName = this.uploadData.params.split('_')[0] || ''
            // 调用接口
            fileApi.deleteFile({ keyId, businessName, fileName }).then(res => {
              if (res.code !== '0000') {
                this.$notification.error({
                  message: '系统消息',
                  description: '删除附件失败！'
                })
                reject(new Error())
              }
              this.uploadFileList = this.uploadFileList.filter(item => {
                return item.uid !== file.uid
              })
              resolve()
            })
          },
          onCancel () {
            reject(new Error())
          }
        })
      })
    },
    doCreateUpload (action = '', record = {}) {
      this.uploadData = {
        params: this.businessName + '_' + record.uuid
      }
      this.uploadFileList = []
      this.keyId = record.uuid
      this.action = action
      this.queryUploadFiles()
    },
    doDestroyUpload () {
      this.keyId = ''
      this.action = ''
      this.uploadDisabled = false
      this.uploadFileList = []
      this.uploadData = {}
    }
  }
}
</script>
