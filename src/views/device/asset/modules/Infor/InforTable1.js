// helper: validator and extender
import { extender } from '@/components/Form/helper'

// 基本信息
const FORM_BASE = [
  {
    type: 'AGroup',
    slot: 'title1',
    field: 'title1',
    title: '基本信息',
    attrs: {
      style: 'margin-bottom: 6px'
    },
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'createByName',
    field: 'createByName',
    label: '创建人',
    decorator: {},
    attrs: {
      disabled: false
    }
  },
  {
    type: 'AInput',
    slot: 'createDate',
    field: 'createDate',
    label: '创建日期',
    decorator: {},
    attrs: {
      disabled: true
    }
  },
  {
    type: 'AInput',
    slot: 'assetSoftwareNum',
    field: 'assetSoftwareNum',
    label: '系统编码',
    decorator: {},
    attrs: {
      disabled: true
    }
  },
  {
    type: 'AInput',
    slot: 'phone',
    field: 'phone',
    label: '联系电话',
    decorator: {},
    attrs: {
      disabled: true
    }
  }
]

// 详细信息
const FORM_MORE = [
  {
    type: 'AGroup',
    slot: 'title2',
    field: 'title2',
    title: '详细信息',
    attrs: {
      style: 'margin-bottom: 6px'
    },
    grid: {}
  },
  {
    type: 'ASelect',
    slot: 'assetHardwareDl',
    field: 'assetHardwareDl',
    label: '硬件大类',
    decorator: {
      rules: [{ type: 'string', required: false, message: '请选择硬件大类' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'ASelect',
    slot: 'assetHardwareXl',
    field: 'assetHardwareXl',
    label: '硬件子类',
    decorator: {
      rules: [{ type: 'string', required: false, message: '请选择硬件子类' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'assetHardwareNum',
    field: 'assetHardwareNum',
    label: '编号',
    decorator: {
      rules: [{ type: 'string', required: false, message: '请输入编号' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'sbName',
    field: 'sbName',
    label: '设备名称',
    decorator: {
      rules: [{ type: 'string', required: true, message: '请输入设备名称' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'brand',
    field: 'brand',
    label: '品牌',
    decorator: {
      rules: [{ type: 'string', required: false, message: '请输入品牌' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'model',
    field: 'model',
    label: '型号',
    decorator: {
      rules: [{ type: 'string', required: false, message: '请输入型号' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'ASelect',
    slot: 'sysStatus',
    field: 'sysStatus',
    label: '当前状态',
    decorator: {
      rules: [{ type: 'string', required: false, message: '请输入当前状态' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'ADatePicker',
    slot: 'purchaseDate',
    field: 'purchaseDate',
    label: '采购时间',
    decorator: {
      rules: [{ required: false, message: '请选择采购时间' }]
    },
    attrs: {
      showTime: true
    },
    grid: {}
  },
  // {
  //   type: 'AInput',
  //   slot: 'useUnits',
  //   field: 'useUnits',
  //   label: '使用单位',
  //   decorator: {},
  //   attrs: {},
  //   grid: {}
  // },
  // {
  //   type: 'ASelect',
  //   slot: 'ssdw',
  //   field: 'ssdw',
  //   label: '所属单位',
  //   decorator: {
  //     rules: [{ type: 'string', required: false, message: '请选择所属单位' }]
  //   },
  //   attrs: {},
  //   grid: {}
  // },
  {
    type: 'ASelect',
    slot: 'ssbm',
    field: 'ssbm',
    label: '所属部门',
    decorator: {
      rules: [{ type: 'string', required: false, message: '请选择所属部门' }]
    },
    attrs: {
      showSearch: true,
      optionFilterProp: 'label'
    },
    grid: {}
  },
  {
    type: 'ASelect',
    slot: 'sybm',
    field: 'sybm',
    label: '使用部门',
    decorator: {
      rules: [{ type: 'string', required: false, message: '请选择使用部门' }]
    },
    attrs: {
      showSearch: true,
      optionFilterProp: 'label'
    },
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'installationAddress',
    field: 'installationAddress',
    label: '安装地址',
    decorator: {
      rules: [{ type: 'string', required: true, message: '请输入安装地址' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'propertyUnit',
    field: 'propertyUnit',
    label: '产权单位',
    decorator: {
      rules: [{ type: 'string', required: false, message: '请输入产权单位' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'personInCharge',
    field: 'personInCharge',
    label: '责任人',
    decorator: {
      rules: [{ type: 'string', required: false, message: '请输入责任人' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'maintenanceUnit',
    field: 'maintenanceUnit',
    label: '维护单位',
    decorator: {
      rules: [{ type: 'string', required: false, message: '请输入维护单位' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'investment',
    field: 'investment',
    label: '原值(万元)',
    decorator: {
      rules: [{ required: false, message: '请输入原值' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'orderqty',
    field: 'orderqty',
    label: '数量',
    decorator: {
      rules: [{ required: false, message: '请输入数量' }]
    },
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'unit',
    field: 'unit',
    label: '计量单位',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'finDepreciationPeriod',
    field: 'finDepreciationPeriod',
    label: '财务折旧年限',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'usedfor',
    field: 'usedfor',
    label: '用途',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'ipAddress',
    field: 'ipAddress',
    label: 'ip地址',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'operationSysVersion',
    field: 'operationSysVersion',
    label: '操作系统版本',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'version',
    field: 'version',
    label: '数据库版本',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'sysPortSer',
    field: 'sysPortSer',
    label: '端口以及对应服务',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'realLoc',
    field: 'realLoc',
    label: '物理位置',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'virtualIp',
    field: 'virtualIp',
    label: '虚拟ip',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'manageIp',
    field: 'manageIp',
    label: '管理ip地址',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'assetFacModel',
    field: 'assetFacModel',
    label: '设备厂商及型号',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'publicNetworkIp',
    field: 'publicNetworkIp',
    label: '公网ip地址',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'realmName',
    field: 'realmName',
    label: '域名',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'port',
    field: 'port',
    label: '开放端口',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'AInput',
    slot: 'services',
    field: 'services',
    label: '服务',
    decorator: {},
    attrs: {},
    grid: {}
  },
  {
    type: 'ATextarea',
    slot: 'remark',
    field: 'remark',
    label: '说明/备注',
    decorator: {},
    attrs: {
      rows: 3
    },
    grid: {
      xs: 8,
      newline: true
    }
  }
]

// 导出配置
export default {
  data () {
    // 处理
    const groups = [
      ...FORM_BASE,
      ...FORM_MORE
    ]

    return {
      // 布局
      grid: {
        gutter: 10,
        xs: 4
      },

      // 属性
      attrs: {
        labelAlign: 'right'
      },

      // 监听
      watch: {
        onValuesChange (props, values) {
          // 创建更改区
          const changed = {}

          // 监听大类
          if (values.assetHardwareDl) {
            const assetHardwareDl = values.assetHardwareDl
            const assetHardwareXlArr = this.options.assetHardwareXl.selectOptions
            const assetHardwareXl = this.form.getFieldValue('assetHardwareXl')
            if (
              assetHardwareDl &&
              !assetHardwareXlArr.some(item => assetHardwareXl === item.value && assetHardwareDl === item.valueParent)
            ) {
              this.form.setFieldsValue({ assetHardwareXl: '' })
            }
          }

          // 是否进行赋值 (一次性赋值，提升性能)
          if (Object.keys(changed).length > 0) {
            this.setFieldsValue(changed)
          }
        }
      },

      // 配置
      groups: [
        ...extender.groups(groups, (group, index, groups) => {
          // 完善 groups
        })
      ],

      // 扩展
      options: {
        ...extender.options(groups, (group, index, groups, region) => {
          // 默认值
          const option = {}

          // 下拉框处理
          if (group.type === 'ASelect') {
            Object.assign(option, {
              selectOptions: []
            })
          }

          // 信息化大类
          if (group.field === 'assetHardwareDl') {
            Object.assign(option, {
              selectOptions: this.queryOptions.assetHardwareDl || []
            })
            group.attrs.disabled = () => !this.isAdd
          }

          // 信息化子类
          if (group.field === 'assetHardwareXl') {
            Object.assign(option, {
              selectOptionsRender (options, { Utils, form }) {
                return options.filter(opt => {
                  const assetHardwareDl = form.getFieldValue('assetHardwareDl')
                  return !assetHardwareDl || opt.valueParent === assetHardwareDl
                })
              },
              selectOptions: this.queryOptions.assetHardwareXl || []
            })
            group.attrs.disabled = () => !this.isAdd
          }

          // 所属单位
          if (group.field === 'ssdw') {
            Object.assign(option, {
              selectOptions: this.queryOptions.ssdws || []
            })
          }

          if (group.field === 'sybm' || group.field === 'ssbm') {
            Object.assign(option, {
              selectOptions: this.queryOptions.sybms || []
            })
          }

          // 当前状态
          if (group.field === 'sysStatus') {
            Object.assign(option, {
              selectOptions: this.queryOptions.sysStatus || []
            })
          }

          // 服务器 305
          // 网络设备 303
          // 信息安全设备 304
          // 公网服务 311005

          // 用途
          if (group.field === 'usedfor') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetHardwareDl = form.getFieldValue('assetHardwareDl')
                return (
                  ['305'].includes(assetHardwareDl)
                )
              }
            })
          }

          // ip地址
          if (group.field === 'ipAddress') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetHardwareDl = form.getFieldValue('assetHardwareDl')
                return (
                  ['305', '304'].includes(assetHardwareDl)
                )
              }
            })
          }

          // 操作系统版本
          if (group.field === 'operationSysVersion') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetHardwareDl = form.getFieldValue('assetHardwareDl')
                return (
                  ['305'].includes(assetHardwareDl)
                )
              }
            })
          }

          // 数据库版本
          if (group.field === 'version') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetHardwareDl = form.getFieldValue('assetHardwareDl')
                return (
                  ['305'].includes(assetHardwareDl)
                )
              }
            })
          }

          // 端口以及对应服务
          if (group.field === 'sysPortSer') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetHardwareDl = form.getFieldValue('assetHardwareDl')
                return (
                  ['305'].includes(assetHardwareDl)
                )
              }
            })
          }

          // 物理位置
          if (group.field === 'realLoc') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetHardwareDl = form.getFieldValue('assetHardwareDl')
                return (
                  ['305', '303', '304'].includes(assetHardwareDl)
                )
              }
            })
          }

          // 虚拟ip
          if (group.field === 'virtualIp') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetHardwareDl = form.getFieldValue('assetHardwareDl')
                return (
                  ['305'].includes(assetHardwareDl)
                )
              }
            })
          }

          // 管理ip地址
          if (group.field === 'manageIp') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetHardwareDl = form.getFieldValue('assetHardwareDl')
                return (
                  ['303'].includes(assetHardwareDl)
                )
              }
            })
          }

          // 管理ip地址
          if (group.field === 'assetFacModel') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetHardwareDl = form.getFieldValue('assetHardwareDl')
                return (
                  ['304'].includes(assetHardwareDl)
                )
              }
            })
          }

          // 公网ip地址
          if (group.field === 'publicNetworkIp') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetHardwareXl = form.getFieldValue('assetHardwareXl')
                return (
                  ['311005'].includes(assetHardwareXl)
                )
              }
            })
          }

          // 域名
          if (group.field === 'realmName') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetHardwareXl = form.getFieldValue('assetHardwareXl')
                return (
                  ['311005'].includes(assetHardwareXl)
                )
              }
            })
          }

          // 开放端口
          if (group.field === 'port') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetHardwareXl = form.getFieldValue('assetHardwareXl')
                return (
                  ['311005'].includes(assetHardwareXl)
                )
              }
            })
          }

          // 服务
          if (group.field === 'services') {
            Object.assign(option, {
              handleRender (item, { Utils, form }) {
                const assetHardwareXl = form.getFieldValue('assetHardwareXl')
                return (
                  ['311005'].includes(assetHardwareXl)
                )
              }
            })
          }

          return option
        })
      },

      // 禁用
      disabled: false,

      // 只读
      readonly: false,

      // 查询中
      spinning: false
    }
  }
}
