<template>
  <section>
    <s-table
      ref="table"
      :data="loadData"
      :scroll="scroll"
      :columns="columns"
      :customRow="rowClick"
      :rowSelection="rowSelection"
      :clearSelection="clearSelection"
      :showPagination="showPagination"
      :pageSizeOptions="pageSizeOptions"
      :pageSize="defaultPageSize"
      :rowClassName="rowClassName"
      :immediate="immediate"
      :bordered="bordered"
      rowKey="uuid"
    >
      <span
        slot="assetHardwareDl"
        slot-scope="key"
      >{{ takeSelectLabel(queryOptions.assetHardwareDl, key) }}</span>
      <span
        slot="assetHardwareXl"
        slot-scope="key"
      >{{ takeSelectLabel(queryOptions.assetHardwareXl, key) }}</span>
      <span
        slot="sysStatus"
        slot-scope="key"
      >{{ takeSelectLabel(queryOptions.sysStatus, key) }}</span>
      <span
        slot="sybm"
        slot-scope="key"
      >{{ takeSelectLabel(queryOptions.sybms, key) }}</span>
    </s-table>
    <a-drawer
      width="100vw"
      class="no-transform"
      :visible="visible"
      :mask="false"
      :maskClosable="false"
      :getContainer="false"
      @close="doClose()"
    >
      <!-- 关闭图标 -->
      <a-icon
        type="close"
        style="position: absolute; top: 13px; right: 10px; z-index: 25; cursor: pointer;"
        @click="doClose"
      />

      <a-tabs
        v-model="tabsKey"
        tabPosition="top"
      >
        <a-tab-pane
          key="1"
          tab="表单信息"
          :forceRender="true"
        >
          <s-form
            ref="form"
            :grid="grid"
            :watch="watch"
            :attrs="attrs"
            :groups="groups"
            :options="options"
            :disabled="disabled"
            :readonly="readonly"
            :spinning="loading"
          >
            <!-- 年检信息 -->
            <template #after>
              <div class="form-item">
                <div class="form-header">
                  <div style="display: flex; width: 100%; max-width: 1000px; position: relative">
                    <div class="titles">
                      <div style="width: 15%; padding-left: 13px">年检类型</div>
                      <div style="width: 14%; padding-left: 13px">品牌</div>
                      <div style="width: 14%; padding-left: 13px">编号</div>
                      <div style="width: 14%; padding-left: 13px">型号</div>
                      <div style="width: 15%; padding-left: 13px">年检日期</div>
                      <div style="width: 14%; padding-left: 13px">年检周期</div>
                      <div style="width: 14%; padding-left: 13px">提醒期限</div>
                    </div>
                    <div class="buttons">
                      <a-button
                        v-if="!disabled && !readonly"
                        type="link"
                        class="button"
                        @click="doAsEntitiesAdd()"
                      >新增</a-button
                      >
                    </div>
                  </div>
                </div>
                <div
                  v-if="assetSbAsEntities.length > 0"
                  style="width: 100%; max-width: 1000px; margin-bottom: 10px; position: relative"
                  class="form-bodyer"
                >
                  <div v-for="(item, index) of assetSbAsEntities" :key="index" class="form-every">
                    <div class="inputs">
                      <div style="width: 15%; padding: 0 8px">
                        <a-select v-model="item.codeId" :disabled="item.action !== 'insert'" style="width: 100%">
                          <a-select-option v-for="(item2, index2) in queryOptions.annualInspections" :value="item2.value" :key="index2">{{
                            item2.label
                          }}</a-select-option>
                        </a-select>
                      </div>
                      <div style="width: 14%; padding: 0 8px">
                        <a-select v-model="item.brand" :disabled="item.action !== 'insert' || item.codeId !== '0001'" style="width: 100%">
                          <a-select-option v-for="(item2, index2) in queryOptions.cylinderBrands" :value="item2.value" :key="index2">{{
                            item2.label
                          }}</a-select-option>
                        </a-select>
                      </div>
                      <div style="width: 14%; padding: 0 8px">
                        <a-input v-model="item.number" :disabled="item.action !== 'insert'" style="width: 100%" />
                      </div>
                      <div style="width: 14%; padding: 0 8px">
                        <a-input v-model="item.model" :disabled="item.action !== 'insert'" style="width: 100%" />
                      </div>
                      <div style="width: 15%; padding: 0 8px">
                        <a-date-picker
                          v-model="item.annualInspectionDate"
                          :disabled="item.action !== 'insert'"
                          style="width: 100%"
                        />
                      </div>
                      <div style="width: 14%; padding: 0 8px">
                        <a-input v-model="item.period" :disabled="item.action !== 'insert'" style="width: 100%" suffix="天" />
                      </div>
                      <div style="width: 14%; padding: 0 8px">
                        <a-input
                          v-model="item.remindDeadline"
                          :disabled="item.action !== 'insert'"
                          style="width: 100%"
                          suffix="天"
                        />
                      </div>
                    </div>
                    <div class="buttons">
                      <a-button
                        v-if="!disabled && !readonly"
                        type="link"
                        class="button"
                        style="color: #f34d4d"
                        @click="doAsEntitiesDel(item)"
                      >移除</a-button
                      >
                    </div>
                  </div>
                </div>
                <div v-if="!assetSbAsEntities.length" style="margin-bottom: 10px" class="form-bodyer">
                  <div class="form-empty">暂无年检信息...</div>
                </div>
              </div>
            </template>
          </s-form>
          <appendix
            v-show="showUploadFile"
            ref="appendix"
            text="整机图片"
            :directDisplay="true"
          />
        </a-tab-pane>
        <a-tab-pane
          key="2"
          tab="履历册"
          :forceRender="true"
        >
          <resume-table
            :assetUuid="assetXxhUuid"
            assetType="asset_xxh"
          />
        </a-tab-pane>
      </a-tabs>

      <div class="drawer-footer">
        <div class="footer-fixed">
          <a-button @click="doClose()">取消</a-button>
          <a-button
            type="primary"
            :loading="loading"
            @click="doSave"
          >保存</a-button>
        </div>
      </div>
    </a-drawer>
  </section>
</template>

<script>
import { mapGetters } from 'vuex'
import { STable, SForm } from '@/components'
import { requestBuilder, deepUpdate, takeTreeByKey } from '@/utils/util'
import * as inforApi from '@/api/device/infor'
import { queryAssetSbAs } from '@/api/device/assetSb'
import moment from 'moment'
import ResumeTable from '@/views/device/asset/modules/Resume/InfoTable'

// 导入表单配置
import FormConfig from './InforTable1.js'

// 导入上传控件
import InfoUpload from './InfoUpload'
import Appendix from '@/views/system/components/Appendix'

export default {
  name: 'InforTable1',
  components: {
    STable,
    SForm,
    InfoUpload,
    Appendix,
    ResumeTable
  },
  mixins: [FormConfig],
  props: {
    queryOptions: {
      type: Object,
      default: function () {
        return {
          assetHardwareDl: [],
          assetHardwareXl: [],
          ssdws: [],
          sybms: [],
          sysStatus: [],
          annualInspections: [],
          cylinderBrands: []
        }
      }
    },
    selectedNode: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data () {
    return {
      // 表格配置 - 参数
      queryParam: {},
      assetSbAsEntities: [],
      lastAssetSbAsEntities: [],
      assetXxhUuid: '',
      // 表格配置 - 硬件
      columns: [
        {
          title: '使用部门',
          dataIndex: 'sybm',
          scopedSlots: { customRender: 'sybm' },
          sorter: true,
          ellipsis: true,
          width: 100
        },
        {
          title: '责任人',
          dataIndex: 'personInCharge',
          sorter: true,
          ellipsis: true,
          width: 100
        },
        {
          title: '设备名称',
          dataIndex: 'sbName',
          sorter: true,
          ellipsis: true,
          width: 100
        },
        {
          title: '安装地址',
          dataIndex: 'installationAddress',
          sorter: true,
          ellipsis: true,
          width: 110
        },
        {
          title: '品牌',
          dataIndex: 'brand',
          sorter: true,
          ellipsis: true,
          width: 120
        },
        {
          title: '型号',
          dataIndex: 'model',
          sorter: true,
          ellipsis: true,
          width: 120
        },
        {
          title: '当前状态',
          dataIndex: 'sysStatus',
          scopedSlots: { customRender: 'sysStatus' },
          sorter: true,
          ellipsis: true,
          width: 120
        },
        {
          title: '原值(万元)',
          dataIndex: 'investment',
          sorter: true,
          ellipsis: true,
          width: 120
        },
        // {
        //   title: '当前状态',
        //   dataIndex: 'status',
        //   sorter: true,
        //   ellipsis: true,
        //   width: 110
        // },
        {
          title: '产权单位',
          dataIndex: 'propertyUnit',
          sorter: true,
          ellipsis: true,
          width: 110
        }
      ],
      rowSelection: {
        type: 'checkbox',
        onSelect: this.onSelectHandle,
        onChange: this.onSelectChange
      },
      scroll: {
        x: 'max-content',
        scrollToFirstRowOnChange: false
      },
      dataSource: [],
      selectedRows: [],
      selectedRowKeys: [],
      pageSizeOptions: ['10', '15', '20', '25', '30'],
      defaultPageSize: 20,
      clearSelection: true,
      showPagination: true,
      clickTimer: null,
      immediate: false,
      bordered: false,
      loadData: parameter => {
        const queryParam = {
          ...this.queryParam
        }
        const param = requestBuilder(
          '',
          // ？
          deepUpdate(
            {
              activity: 'Y',
              assetHardwareDlList: [],
              assetHardwareXlList: [],
              assetSoftwareDlList: [],
              assetSoftwareXlList: [],
              brand: '',
              model: '',
              sysStatus: '',
              ssdw: '',
              installationAddress: '',
              personInCharge: '',
              roleType: 'device'
            },
            queryParam
          ),
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder
        )
        return inforApi.queryAssetxxh(param).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '查询失败！'
            })
            return Promise.reject(res)
          }
          this.dataSource = [...res.result.data] || []
          return res.result
        })
      },
      rowClick: record => ({
        on: {
          click: () => {
            // 双击时消单击事件
            clearTimeout(this.clickTimer)
            this.selectedRows = [record]
            this.selectedRowKeys = [record.uuid]
            this.$refs.table.triggerSelect(this.selectedRowKeys, this.selectedRows)
            this.doEdit([record])
          },
          oldClick: () => {
            // 限制频繁触发单击事件
            clearTimeout(this.clickTimer)
            this.clickTimer = setTimeout(() => {
              // 在单击效果为单选情况
              if (this.rowSelection.type === 'radio') {
                // 编辑模式下
                if (this.visible) {
                  this.doEdit([record])
                }
              }
              // 在单击效果为多选情况
              if (this.rowSelection.type === 'checkbox') {
                const index = this.selectedRowKeys.indexOf(record.uuid)
                if (index > -1) {
                  this.selectedRowKeys.splice(index, 1)
                  this.selectedRows.splice(index, 1)
                } else {
                  this.selectedRowKeys.push(record.uuid)
                  this.selectedRows.push(record)
                }
                const dataSource = this.dataSource.filter(item => {
                  return this.selectedRowKeys.includes(item.uuid)
                })
                this.selectedRows = dataSource.map(item => item)
                this.selectedRowKeys = dataSource.map(item => item.uuid)
                this.$refs.table.triggerSelect(this.selectedRowKeys, this.selectedRows)
              }
            }, 300)
          }
        }
      }),
      rowClassName: () => {
        return 'cursor-pointer'
      },
      // 标签页
      tabsKey: '1',
      // 抽屉框
      title: '',
      action: '',
      visible: false,
      loading: false,
      isAdd: true,
      showUploadFile: false
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  methods: {
    // 表格手动勾选
    onSelectHandle (record, selected, selectedRows) {
      this.visible && this.doEdit([record])
    },
    // 获取下拉框选项文本
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || key
    },
    // 表格勾选更改
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRows = selectedRows
      this.selectedRowKeys = selectedRowKeys
    },
    // 表格加载中
    doReady (state) {
      this.$refs.table.ready(state)
    },
    // 表格数据清空
    doClear () {
      this.$refs.table.clear()
    },
    // 表格数据刷新
    doSearch (hidden) {
      if (hidden === true) {
        this.doClose()
      }
      this.$refs.table.refresh(true)
    },
    // 信息新增
    doAdd () {
      this.selectedRows = []
      this.selectedRowKeys = []
      this.$refs.table.triggerSelect([], [])
      this.$refs.table.rowSelection.type = 'radio'
      this.assetSbAsEntities = []
      this.lastAssetSbAsEntities = []
      this.showUploadFile = false
      this.doOpen('insert')
    },
    // 信息修改
    doEdit (records) {
      if (records) {
        this.selectedRows = records
        this.selectedRowKeys = records.map(item => item.uuid)
        this.$refs.table.triggerSelect(this.selectedRowKeys, this.selectedRows)
      }
      this.showUploadFile = true
      queryAssetSbAs(requestBuilder('', { assetSbUuid: records[0].uuid, assetObject: 'asset_xxh' }))
        .then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '年检信息查询失败失败！'
            })
            return Promise.reject(res)
          }
          this.assetSbAsEntities = res.result.map(item => {
            return {
              ...item,
              annualInspectionDate: item.annualInspectionDate
                ? moment(item.annualInspectionDate, 'YYYY-MM-DD')
                : null
            }
          })
          this.lastAssetSbAsEntities = res.result.map(item => {
            return {
              ...item,
              annualInspectionDate: item.annualInspectionDate
                ? moment(item.annualInspectionDate, 'YYYY-MM-DD')
                : null
            }
          })
        })
      this.$refs.table.rowSelection.type = 'radio'
      this.doOpen('update')
    },
    // 信息删除
    doDel () {
      if (this.visible) {
        this.$message.error('新增/修改模式下，不可进行删除操作！')
        return
      }
      if (this.selectedRows.length === 0) {
        this.$message.error('请选择所要删除的硬件！')
        return
      }
      this.$confirm({
        title: '系统提示',
        content: '确定删除吗？',
        onOk: () => {
          inforApi.deleteAssetxxh(requestBuilder('delete', [...this.selectedRows])).then(res => {
            if (res.code !== '0000') {
              this.$notification.error({
                message: '系统消息',
                description: res.message || '删除失败！'
              })
              return Promise.reject(res)
            }
            this.$notification.success({
              message: '系统消息',
              description: '删除成功！'
            })
            this.doSearch(true)
          })
        }
      })
    },
    // 打开信息弹框
    doOpen (action) {
      let base = {}
      let assetHardwareDl = ''
      let assetHardwareXl = ''
      const { selectedRows = [] } = this
      const [record = {}] = selectedRows

      switch (action) {
        case 'insert': {
          if (this.selectedNode.level === 3) {
            assetHardwareDl = this.selectedNode.value
          }
          if (this.selectedNode.level === 4) {
            assetHardwareDl = this.selectedNode.parentNode.value
            assetHardwareXl = this.selectedNode.value
          }
          base = {
            // 基本信息
            createByName: this.userInfo.personName,
            createDate: moment().format('YYYY-MM-DD HH:mm:ss'),
            phone: this.userInfo.mobilePhone,
            // 详细信息
            assetHardwareDl: assetHardwareDl,
            assetHardwareXl: assetHardwareXl
          }
          this.isAdd = true
          this.title = '新增'
          this.action = 'insert'
          break
        }
        case 'update': {
          base = {
            // 详细信息
            assetHardwareDl: assetHardwareDl,
            assetHardwareXl: assetHardwareXl
          }
          this.isAdd = false
          this.title = '修改'
          this.action = 'update'
          this.assetXxhUuid = record.uuid
          if (!record.assetSbAsEntities) {
            record.assetSbAsEntities = []
          }
          break
        }
      }
      // 处理表单
      this.$refs.form.resetFields()
      this.$refs.form.setFieldsValue(record, this.isAdd ? base : {})
      // 处理 附件文件
      if (this.showUploadFile) {
        this.$refs.appendix.doCreateUpload('infor', record)
      }

      // 显示弹框
      this.visible = true
    },
    // 关闭信息弹框
    doClose () {
      // 附件,检查是否在上传中
      if (this.showUploadFile && !this.$refs.appendix.hiddenUploadDrawer()) {
        return
      }
      // 表单
      this.$refs.form.resetFields()
      // 表格
      this.$refs.table.triggerSelect([], [])
      this.$refs.table.rowSelection.type = 'checkbox'
      this.lastAssetSbAsEntities = []
      this.assetSbAsEntities = []
      // 抽屉
      this.title = ''
      this.action = ''
      this.assetXxhUuid = ''
      this.isAdd = false
      this.loading = false
      this.visible = false
    },
    // 新增年检信息
    doAsEntitiesAdd () {
      this.assetSbAsEntities.push({
        assetSbUuid: this.selectedRowKeys[0],
        annualInspectionDate: null,
        remindDeadline: '',
        period: '',
        codeId: '',
        brand: '',
        number: '',
        model: '',
        action: 'insert'
      })
    },
    // 删除年检信息
    doAsEntitiesDel (record) {
      this.assetSbAsEntities = this.assetSbAsEntities.filter(item => item !== record)
    },
    // 校验表单数据
    doValidateData (record) {
      return new Promise((resolve, reject) => {
        this.$refs.form.validateFields(errors => {
          if (errors) {
            this.$message.error('请完善表单信息!')
            reject(errors)
            return
          }
          for (const item of this.assetSbAsEntities) {
            if (!item.codeId) {
              this.$message.error('请正确完善年检信息!')
              return
            }
            if (!item.annualInspectionDate) {
              this.$message.error('请正确完善年检信息!')
              return
            }
            if (!/^\d+(\.\d*)?$/.test(item.period)) {
              this.$message.error('请正确填写年检周期!')
              return
            }
            if (!/^\d+(\.\d*)?$/.test(item.remindDeadline)) {
              this.$message.error('请正确填写提醒期限!')
              return
            }
          }
          // 实时的年检信息
          const assetSbAsEntities = []
          for (const item of this.assetSbAsEntities) {
            assetSbAsEntities.push({
              ...item
            })
          }
          // 年检信息是否有删除
          for (const item of this.lastAssetSbAsEntities) {
            if (!this.assetSbAsEntities.some(current => current.uuid === item.uuid)) {
              assetSbAsEntities.push({
                ...item,
                action: 'delete'
              })
            }
          }
          // 信息
          resolve([
            {
              ...record,
              ...this.$refs.form.getFieldsValue(),
              assetSbAsEntities: assetSbAsEntities.map(item => {
                return {
                  ...item,
                  annualInspectionDate: item.annualInspectionDate ? item.annualInspectionDate.format('YYYY-MM-DD') : ''
                }
              })
            }
          ])
        })
      })
    },
    // 更新表单数据
    doSave () {
      const record = this.selectedRows[0]
      console.log('record', record)
      return this.doValidateData(record).then(records => {
        if (!this.loading) {
          // 更新中
          this.loading = true

          // 参数处理
          const notice = {
            error: this.action === 'insert' ? '新增失败！' : '更新失败！',
            success: this.action === 'insert' ? '新增成功！' : '更新成功！'
          }
          records.forEach(item => { item.type = 'hardware' })
          // 调用接口
          return inforApi.modifyAssetxxh(requestBuilder(this.action, records))
            .then(res => {
              if (res.code !== '0000') {
                this.$notification.error({
                  message: '系统消息',
                  description: res.message || notice.error
                })
                return Promise.reject(res)
              }
              this.$notification.success({
                message: '系统消息',
                description: notice.success
              })
              this.doSearch(true)
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
// form 抽屉框
section {
  ::v-deep {
    .ant-drawer {
      .ant-drawer-header-no-title {
        .ant-drawer-close {
          display: none;
        }
        & + .ant-drawer-body {
          padding: 0 20px 10px;
          overflow: visible;
          .ant-tabs {
            margin-top: 10px;
            overflow: visible;
            & > .ant-tabs-bar {
              background-color: #ffffff;
              position: sticky;
              padding-top: 10px;
              z-index: 10;
              top: 0;
            }
            .ant-upload {
              float: left;
              padding-left: 5px;
              margin-bottom: 1px;
            }
            .ant-upload-list {
              width: 100%;
              clear: both;
              border-top: dashed 1px #cfcfcf;
            }
          }
        }
      }
    }
  }
}
.pane-container {
  width: 100%;
  margin-bottom: 30px;
  .pane-header {
    display: flex;
    width: 100%;
    height: 30px;
    padding-left: 5px;
    margin-bottom: 10px;
    line-height: 30px;
    font-size: 13px;
    color: #303133;
    border-bottom: dashed 1px #cfcfcf;
    & > .pane-header-title {
      flex: 1 1 auto;
      padding-left: 8px;
      line-height: 32px;
    }
  }
  .pane-content {
    padding: 0 3px;
    margin-bottom: 10px;
  }
}
::v-deep {
  .s-form-container {
    .ant-form-horizontal,
    .ant-form-vertical,
    .ant-form-inline {
      .ant-form-item {
        & > .ant-form-item-label {
          width: 100px;
        }
      }
    }
  }
}
.form-item {
  width: 100%;
  margin-bottom: 20px;
  .form-header {
    display: flex;
    width: 100%;
    height: 30px;
    padding-left: 5px;
    margin-bottom: 7px;
    line-height: 30px;
    font-size: 13px;
    color: #303133;
    border-bottom: dashed 1px #cfcfcf;
    .titles {
      display: flex;
      width: calc(100% - 50px);
      padding-left: 8px;
      line-height: 32px;
    }
    .buttons {
      width: 50px;
    }
  }
  .form-bodyer {
    padding: 0 3px;
    margin-bottom: 7px;
    overflow: hidden;
    .form-every {
      display: flex;
      width: 100%;
      margin: 3px 0 7px;
      & > .inputs {
        display: flex;
        width: calc(100% - 50px);
      }
      & > .buttons {
        width: 50px;
        & > .button {
          margin-top: 0;
          margin-bottom: 0;
        }
      }
    }
  }
  .form-empty {
    margin-bottom: 20px;
    padding-left: 7px;
    font-size: 13px;
  }
  .form {
    margin-bottom: 60px;
  }
}
</style>
