<template>
  <section>
    <s-table
      ref="table"
      :data="loadData"
      :scroll="scroll"
      :columns="columns"
      :customRow="rowClick"
      :rowSelection="rowSelection"
      :clearSelection="clearSelection"
      :showPagination="showPagination"
      :pageSizeOptions="pageSizeOptions"
      :pageSize="defaultPageSize"
      :rowClassName="rowClassName"
      :immediate="immediate"
      :bordered="bordered"
      rowKey="uuid"
    >
      <span
        slot="assetHardwareDl"
        slot-scope="key"
      >{{ takeSelectLabel(queryOptions.assetHardwareDl, key) }}</span>
      <span
        slot="assetHardwareXl"
        slot-scope="key"
      >{{ takeSelectLabel(queryOptions.assetHardwareXl, key) }}</span>
      <span
        slot="ssdw"
        slot-scope="key"
      >{{ takeSelectLabel(queryOptions.ssdws, key) }}</span>
    </s-table>
    <a-drawer
      width="100vw"
      class="no-transform"
      :visible="visible"
      :mask="false"
      :maskClosable="false"
      :getContainer="false"
      @close="doClose()"
    >
      <!-- 关闭图标 -->
      <a-icon
        type="close"
        style="position: absolute; top: 13px; right: 10px; z-index: 25; cursor: pointer;"
        @click="doClose"
      />

      <a-tabs
        v-model="tabsKey"
        tabPosition="top"
      >
        <a-tab-pane
          key="1"
          tab="表单信息"
          :forceRender="true"
        >
          <s-form
            ref="form"
            :grid="grid"
            :watch="watch"
            :attrs="attrs"
            :groups="groups"
            :options="options"
            :disabled="disabled"
            :readonly="readonly"
            :spinning="loading"
          />
          <appendix
            v-show="showUploadFile"
            ref="appendix"
            text="整机图片"
            :directDisplay="true"
          />
        </a-tab-pane>
        <a-tab-pane
          key="2"
          tab="履历册"
          :forceRender="true"
        />
      </a-tabs>

      <div class="drawer-footer">
        <div class="footer-fixed">
          <a-button @click="doClose()">取消</a-button>
          <a-button
            type="primary"
            :loading="loading"
            @click="doSave"
          >保存</a-button>
        </div>
      </div>
    </a-drawer>
  </section>
</template>

<script>
import { mapGetters } from 'vuex'
import { STable, SForm } from '@/components'
import { requestBuilder, deepUpdate, takeTreeByKey } from '@/utils/util'
import * as inforApi from '@/api/device/infor'
import moment from 'moment'

// 导入表单配置
import FormConfig from './InforTable3.js'

// 导入上传控件
import InfoUpload from './InfoUpload'
import Appendix from '@/views/system/components/Appendix'

export default {
  name: 'InforTable3',
  components: {
    STable,
    SForm,
    InfoUpload,
    Appendix
  },
  mixins: [FormConfig],
  props: {
    queryOptions: {
      type: Object,
      default: function () {
        return {
          assetHardwareDl: [],
          assetHardwareXl: [],
          ssdws: []
        }
      }
    },
    selectedNode: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data () {
    return {
      // 表格配置 - 参数
      queryParam: {},
      // 表格配置 - 硬件
      columns: [
        {
          title: '所属单位',
          dataIndex: 'ssdw',
          scopedSlots: { customRender: 'ssdw' },
          ellipsis: true,
          width: 140
        },
        {
          title: '创建人',
          dataIndex: 'createByName',
          ellipsis: true,
          width: 100
        },
        {
          title: '创建日期',
          dataIndex: 'createDate',
          sorter: true,
          ellipsis: true,
          width: 110
        },
        {
          title: '手机号',
          dataIndex: 'phone',
          ellipsis: true,
          width: 110
        }
      ],
      rowSelection: {
        type: 'checkbox',
        onSelect: this.onSelectHandle,
        onChange: this.onSelectChange
      },
      scroll: {
        x: 'max-content',
        scrollToFirstRowOnChange: false
      },
      dataSource: [],
      selectedRows: [],
      selectedRowKeys: [],
      pageSizeOptions: ['10', '15', '20', '25', '30'],
      defaultPageSize: 20,
      clearSelection: true,
      showPagination: true,
      clickTimer: null,
      immediate: false,
      bordered: false,
      loadData: parameter => {
        const queryParam = {
          ...this.queryParam
        }
        const param = requestBuilder(
          '',
          // ？
          deepUpdate(
            {
              activity: 'Y',
              assetHardwareDlList: [],
              assetHardwareXlList: [],
              assetSoftwareDlList: [],
              assetSoftwareXlList: [],
              ssdw: '',
              roleType: 'device'
            },
            queryParam
          ),
          parameter.pageNo,
          parameter.pageSize,
          parameter.sortField,
          parameter.sortOrder
        )
        return inforApi.queryAssetxxh(param).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || '查询失败！'
            })
            return Promise.reject(res)
          }
          this.dataSource = [...res.result.data] || []
          return res.result
        })
      },
      rowClick: record => ({
        on: {
          click: () => {
            // 双击时消单击事件
            clearTimeout(this.clickTimer)
            this.selectedRows = [record]
            this.selectedRowKeys = [record.uuid]
            this.$refs.table.triggerSelect(this.selectedRowKeys, this.selectedRows)
            this.doEdit([record])
          },
          oldClick: () => {
            // 限制频繁触发单击事件
            clearTimeout(this.clickTimer)
            this.clickTimer = setTimeout(() => {
              // 在单击效果为单选情况
              if (this.rowSelection.type === 'radio') {
                // 编辑模式下
                if (this.visible) {
                  this.doEdit([record])
                }
              }
              // 在单击效果为多选情况
              if (this.rowSelection.type === 'checkbox') {
                const index = this.selectedRowKeys.indexOf(record.uuid)
                if (index > -1) {
                  this.selectedRowKeys.splice(index, 1)
                  this.selectedRows.splice(index, 1)
                } else {
                  this.selectedRowKeys.push(record.uuid)
                  this.selectedRows.push(record)
                }
                const dataSource = this.dataSource.filter(item => {
                  return this.selectedRowKeys.includes(item.uuid)
                })
                this.selectedRows = dataSource.map(item => item)
                this.selectedRowKeys = dataSource.map(item => item.uuid)
                this.$refs.table.triggerSelect(this.selectedRowKeys, this.selectedRows)
              }
            }, 300)
          }
        }
      }),
      rowClassName: () => {
        return 'cursor-pointer'
      },
      // 标签页
      tabsKey: '1',
      // 抽屉框
      title: '',
      action: '',
      visible: false,
      loading: false,
      isAdd: true,
      showUploadFile: false
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  methods: {
    // 表格手动勾选
    onSelectHandle (record, selected, selectedRows) {
      this.visible && this.doEdit([record])
    },
    // 获取下拉框选项文本
    takeSelectLabel (select, key) {
      return (takeTreeByKey(select, key) || {})['label'] || key
    },
    // 表格勾选更改
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRows = selectedRows
      this.selectedRowKeys = selectedRowKeys
    },
    // 表格加载中
    doReady (state) {
      this.$refs.table.ready(state)
    },
    // 表格数据清空
    doClear () {
      this.$refs.table.clear()
    },
    // 表格数据刷新
    doSearch (hidden) {
      if (hidden === true) {
        this.doClose()
      }
      this.$refs.table.refresh(true)
    },
    // 信息新增
    doAdd () {
      this.selectedRows = []
      this.selectedRowKeys = []
      this.$refs.table.triggerSelect([], [])
      this.$refs.table.rowSelection.type = 'radio'
      this.doOpen('insert')
    },
    // 信息修改
    doEdit (records) {
      if (records) {
        this.selectedRows = records
        this.selectedRowKeys = records.map(item => item.uuid)
        this.$refs.table.triggerSelect(this.selectedRowKeys, this.selectedRows)
      }
      this.$refs.table.rowSelection.type = 'radio'
      this.doOpen('update')
    },
    // 信息删除
    doDel () {
      if (this.visible) {
        this.$message.error('新增/修改模式下，不可进行删除操作！')
        return
      }
      if (this.selectedRows.length === 0) {
        this.$message.error('请选择所要删除的硬件！')
        return
      }
      this.$confirm({
        title: '系统提示',
        content: '确定删除吗？',
        onOk: () => {
          inforApi.deleteAssetxxh(requestBuilder('delete', [...this.selectedRows])).then(res => {
            if (res.code !== '0000') {
              this.$notification.error({
                message: '系统消息',
                description: res.message || '删除失败！'
              })
              return Promise.reject(res)
            }
            this.$notification.success({
              message: '系统消息',
              description: '删除成功！'
            })
            this.doSearch(true)
          })
        }
      })
    },
    // 打开信息弹框
    doOpen (action) {
      let base = {}
      const { selectedRows = [] } = this
      const [record = {}] = selectedRows

      switch (action) {
        case 'insert': {
          base = {
            // 基本信息
            createByName: this.userInfo.personName,
            createDate: moment().format('YYYY-MM-DD HH:mm:ss'),
            phone: this.userInfo.mobilePhone
          }
          this.isAdd = true
          this.title = '新增'
          this.action = 'insert'
          this.showUploadFile = false
          break
        }
        case 'update': {
          this.isAdd = false
          this.title = '修改'
          this.action = 'update'
          this.showUploadFile = true
          break
        }
      }
      // 处理表单
      this.$refs.form.resetFields()
      this.$refs.form.setFieldsValue(record, this.isAdd ? base : {})
      // 处理 附件文件
      if (this.showUploadFile) {
        this.$refs.appendix.doCreateUpload('infor', record)
      }
      // 显示弹框
      this.visible = true
    },
    // 关闭信息弹框
    doClose () {
      // 附件是否正上传中
      if (this.showUploadFile && !this.$refs.appendix.hiddenUploadDrawer()) {
        return
      }
      // 表单
      this.$refs.form.resetFields()
      // 表格
      this.$refs.table.triggerSelect([], [])
      this.$refs.table.rowSelection.type = 'checkbox'
      // 抽屉
      this.title = ''
      this.action = ''
      this.isAdd = false
      this.loading = false
      this.visible = false
    },
    // 保存信息
    doSave () {
      const toSubmit = (action, param, notice) => {
        return inforApi.modifyAssetxxh(requestBuilder(action, param)).then(res => {
          if (res.code !== '0000') {
            this.$notification.error({
              message: '系统消息',
              description: res.message || notice.error
            })
            return Promise.reject(res)
          }
          this.$notification.success({
            message: '系统消息',
            description: notice.success
          })
        })
      }
      if (this.action === 'insert') {
        this.$refs.form.validateFields(errors => {
          if (errors) {
            return
          }
          const action = this.action
          const notice = {
            error: '新增失败！',
            success: '新增成功！'
          }

          let assetHardwareDl = ''
          let assetHardwareXl = ''
          const ssdw = this.userInfo.orgId

          if (this.selectedNode.level === 2) {
            assetHardwareDl = this.selectedNode.value
            assetHardwareXl = this.selectedNode.children[0].value
          }
          if (this.selectedNode.level === 3) {
            assetHardwareDl = this.selectedNode.parentNode.value
            assetHardwareXl = this.selectedNode.value
          }

          const records = [
            {
              ...this.$refs.form.getFieldsValue(),
              assetHardwareDl,
              assetHardwareXl,
              ssdw
            }
          ]
          toSubmit(action, records, notice)
            .then(() => {
              this.doSearch(true)
              this.loading = false
            })
            .catch(() => {
              this.loading = false
            })
          this.loading = true
        })
      }
      if (this.action === 'update') {
        this.$refs.form.validateFields(errors => {
          if (errors) {
            return
          }
          const action = this.action
          const records = []
          const notice = {
            error: '修改失败！',
            success: '修改成功！'
          }
          for (const record of this.selectedRows) {
            records.push({
              ...record,
              ...this.$refs.form.getFieldsValue()
            })
          }
          toSubmit(action, records, notice)
            .then(() => {
              this.doSearch(true)
              this.loading = false
            })
            .catch(() => {
              this.loading = false
            })
          this.loading = true
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
// form 抽屉框
section {
  ::v-deep {
    .ant-drawer {
      .ant-drawer-header-no-title {
        .ant-drawer-close {
          display: none;
        }
        & + .ant-drawer-body {
          padding: 0 20px 10px;
          overflow: visible;
          .ant-tabs {
            margin-top: 10px;
            overflow: visible;
            & > .ant-tabs-bar {
              background-color: #ffffff;
              position: sticky;
              padding-top: 10px;
              z-index: 10;
              top: 0;
            }
            .ant-upload {
              float: left;
              padding-left: 5px;
              margin-bottom: 1px;
            }
            .ant-upload-list {
              width: 100%;
              clear: both;
              border-top: dashed 1px #cfcfcf;
            }
          }
        }
      }
    }
  }
}
.pane-container {
  width: 100%;
  margin-bottom: 30px;
  .pane-header {
    display: flex;
    width: 100%;
    height: 30px;
    padding-left: 5px;
    margin-bottom: 10px;
    line-height: 30px;
    font-size: 13px;
    color: #303133;
    border-bottom: dashed 1px #cfcfcf;
    & > .pane-header-title {
      flex: 1 1 auto;
      padding-left: 8px;
      line-height: 32px;
    }
  }
  .pane-content {
    padding: 0 3px;
    margin-bottom: 10px;
  }
}
::v-deep {
  .s-form-container {
    .ant-form-horizontal,
    .ant-form-vertical,
    .ant-form-inline {
      .ant-form-item {
        & > .ant-form-item-label {
          width: 100px;
        }
      }
    }
  }
}
</style>
