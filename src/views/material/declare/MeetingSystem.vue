<template>
  <div class="device-topology-container">
    <!-- 顶部工具栏 -->
    <a-card :bordered="false" class="toolbar-card">
      <div class="toolbar-content">
        <div class="toolbar-left">
          <a-input-search
            v-model="searchKeyword"
            placeholder="搜索设备名称、IP地址..."
            style="width: 300px; margin-right: 16px;"
            @search="handleSearch"
            @change="handleSearchChange"
          />
          <a-select
            v-model="selectedStatus"
            placeholder="设备状态"
            style="width: 120px; margin-right: 16px;"
            allowClear
            @change="handleStatusFilter"
          >
            <a-select-option value="online">在线</a-select-option>
            <a-select-option value="offline">离线</a-select-option>
            <a-select-option value="warning">告警</a-select-option>
            <a-select-option value="error">故障</a-select-option>
          </a-select>
          <a-select
            v-model="selectedType"
            placeholder="设备类型"
            style="width: 120px; margin-right: 16px;"
            allowClear
            @change="handleTypeFilter"
          >
            <a-select-option value="server">服务器</a-select-option>
            <a-select-option value="switch">交换机</a-select-option>
            <a-select-option value="router">路由器</a-select-option>
            <a-select-option value="firewall">防火墙</a-select-option>
            <a-select-option value="storage">存储设备</a-select-option>
          </a-select>
        </div>
        <div class="toolbar-right">
          <a-button-group>
            <a-button
              :type="layoutType === 'force' ? 'primary' : 'default'"
              @click="changeLayout('force')"
            >
              <a-icon type="cluster" />
              力导向图
            </a-button>
            <a-button
              :type="layoutType === 'circular' ? 'primary' : 'default'"
              @click="changeLayout('circular')"
            >
              <a-icon type="radius-setting" />
              环形布局
            </a-button>
            <a-button
              :type="layoutType === 'grid' ? 'primary' : 'default'"
              @click="changeLayout('grid')"
            >
              <a-icon type="appstore" />
              网格布局
            </a-button>
            <a-button
              :type="layoutType === 'hierarchy' ? 'primary' : 'default'"
              @click="changeLayout('hierarchy')"
            >
              <a-icon type="partition" />
              分层布局
            </a-button>
          </a-button-group>
          <a-divider type="vertical" />
          <a-button @click="refreshTopology" :loading="loading">
            <a-icon type="reload" />
            刷新
          </a-button>
          <a-button @click="showLegend = !showLegend">
            <a-icon type="info-circle" />
            图例
          </a-button>
        </div>
      </div>
    </a-card>

    <!-- 主要内容区域 -->
    <a-row :gutter="16" class="main-content">
      <!-- 拓扑图区域 -->
      <a-col :span="showLegend ? 18 : 24">
        <a-card :bordered="false" class="topology-card">
          <div class="topology-header">
            <h3>设备拓扑图</h3>
            <div class="status-summary">
              <a-tag color="green">在线: {{ statusCounts.online }}</a-tag>
              <a-tag color="red">离线: {{ statusCounts.offline }}</a-tag>
              <a-tag color="orange">告警: {{ statusCounts.warning }}</a-tag>
              <a-tag color="volcano">故障: {{ statusCounts.error }}</a-tag>
            </div>
          </div>
          <a-spin :spinning="loading" tip="加载拓扑数据中...">
            <div
              id="topology-chart"
              class="topology-chart"
              :style="{ height: chartHeight + 'px' }"
            />
          </a-spin>
        </a-card>
      </a-col>

      <!-- 图例和信息面板 -->
      <a-col :span="6" v-if="showLegend">
        <a-card :bordered="false" class="legend-card">
          <div class="legend-content">
            <h4>设备类型图例</h4>
            <div class="legend-items">
              <div class="legend-item" v-for="type in deviceTypes" :key="type.value">
                <div class="legend-icon" :style="{ backgroundColor: type.color }"/>
                <span>{{ type.label }}</span>
              </div>
            </div>

            <h4 style="margin-top: 24px;">状态图例</h4>
            <div class="legend-items">
              <div class="legend-item" v-for="status in deviceStatuses" :key="status.value">
                <div class="legend-icon" :style="{ backgroundColor: status.color }"/>
                <span>{{ status.label }}</span>
              </div>
            </div>

            <h4 style="margin-top: 24px;">连接类型</h4>
            <div class="legend-items">
              <div class="legend-item">
                <div class="legend-line" style="border-color: #1890ff;"/>
                <span>网络连接</span>
              </div>
              <div class="legend-item">
                <div class="legend-line" style="border-color: #52c41a; border-style: dashed;"/>
                <span>虚拟连接</span>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 设备详情模态框 -->
    <a-modal
      v-model="deviceDetailVisible"
      title="设备详情"
      width="800px"
      :footer="null"
      @cancel="closeDeviceDetail"
    >
      <div v-if="selectedDevice" class="device-detail">
        <a-row :gutter="16">
          <a-col :span="12">
            <div class="detail-item">
              <label>设备名称:</label>
              <span>{{ selectedDevice.name }}</span>
            </div>
            <div class="detail-item">
              <label>设备类型:</label>
              <span>{{ getDeviceTypeLabel(selectedDevice.type) }}</span>
            </div>
            <div class="detail-item">
              <label>IP地址:</label>
              <span>{{ selectedDevice.ip }}</span>
            </div>
            <div class="detail-item">
              <label>MAC地址:</label>
              <span>{{ selectedDevice.mac || '未知' }}</span>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="detail-item">
              <label>设备状态:</label>
              <a-tag :color="getStatusColor(selectedDevice.status)">
                {{ getStatusLabel(selectedDevice.status) }}
              </a-tag>
            </div>
            <div class="detail-item">
              <label>位置:</label>
              <span>{{ selectedDevice.location || '未知' }}</span>
            </div>
            <div class="detail-item">
              <label>最后更新:</label>
              <span>{{ selectedDevice.lastUpdate }}</span>
            </div>
            <div class="detail-item">
              <label>运行时间:</label>
              <span>{{ selectedDevice.uptime || '未知' }}</span>
            </div>
          </a-col>
        </a-row>

        <a-divider />

        <div class="performance-metrics" v-if="selectedDevice.metrics">
          <h4>性能指标</h4>
          <a-row :gutter="16">
            <a-col :span="8" v-if="selectedDevice.metrics.cpu">
              <div class="metric-item">
                <label>CPU使用率:</label>
                <a-progress
                  :percent="selectedDevice.metrics.cpu"
                  :status="selectedDevice.metrics.cpu > 80 ? 'exception' : 'normal'"
                />
              </div>
            </a-col>
            <a-col :span="8" v-if="selectedDevice.metrics.memory">
              <div class="metric-item">
                <label>内存使用率:</label>
                <a-progress
                  :percent="selectedDevice.metrics.memory"
                  :status="selectedDevice.metrics.memory > 80 ? 'exception' : 'normal'"
                />
              </div>
            </a-col>
            <a-col :span="8" v-if="selectedDevice.metrics.disk">
              <div class="metric-item">
                <label>磁盘使用率:</label>
                <a-progress
                  :percent="selectedDevice.metrics.disk"
                  :status="selectedDevice.metrics.disk > 80 ? 'exception' : 'normal'"
                />
              </div>
            </a-col>
          </a-row>
        </div>

        <div class="connection-info" v-if="selectedDevice.connections">
          <h4>连接信息</h4>
          <a-list
            :dataSource="selectedDevice.connections"
            size="small"
          >
            <a-list-item slot="renderItem" slot-scope="item">
              <span>{{ item.target }} - {{ item.type }}</span>
              <a-tag :color="item.status === 'active' ? 'green' : 'red'" slot="actions">
                {{ item.status === 'active' ? '活跃' : '断开' }}
              </a-tag>
            </a-list-item>
          </a-list>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'DeviceTopology',
  data () {
    return {
      // 基础状态
      loading: false,

      // 搜索和筛选
      searchKeyword: '',
      selectedStatus: undefined,
      selectedType: undefined,

      // 布局和显示
      layoutType: 'force',
      showLegend: true,
      chartHeight: 600,

      // 设备详情模态框
      deviceDetailVisible: false,
      selectedDevice: null,

      // 拓扑图数据
      topologyChart: null,
      devices: [],
      connections: [],
      filteredDevices: [],
      filteredConnections: [],

      // 状态统计
      statusCounts: {
        online: 0,
        offline: 0,
        warning: 0,
        error: 0
      },

      // 设备类型配置
      deviceTypes: [
        { value: 'server', label: '服务器', color: '#1890ff' },
        { value: 'switch', label: '交换机', color: '#52c41a' },
        { value: 'router', label: '路由器', color: '#fa8c16' },
        { value: 'firewall', label: '防火墙', color: '#f5222d' },
        { value: 'storage', label: '存储设备', color: '#722ed1' }
      ],

      // 设备状态配置
      deviceStatuses: [
        { value: 'online', label: '在线', color: '#52c41a' },
        { value: 'offline', label: '离线', color: '#d9d9d9' },
        { value: 'warning', label: '告警', color: '#fa8c16' },
        { value: 'error', label: '故障', color: '#f5222d' }
      ]
    }
  },
  mounted () {
    this.initTopology()
    this.loadDeviceData()
    this.calculateChartHeight()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy () {
    if (this.topologyChart) {
      this.topologyChart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 初始化拓扑图
    initTopology () {
      const chartDom = document.getElementById('topology-chart')
      if (chartDom) {
        this.topologyChart = echarts.init(chartDom)
        this.setupChartEvents()
      }
    },

    // 计算图表高度
    calculateChartHeight () {
      const windowHeight = window.innerHeight
      this.chartHeight = Math.max(600, windowHeight - 300)
    },

    // 窗口大小变化处理
    handleResize () {
      this.calculateChartHeight()
      if (this.topologyChart) {
        this.topologyChart.resize()
      }
    },

    // 设置图表事件
    setupChartEvents () {
      if (this.topologyChart) {
        this.topologyChart.on('click', params => {
          if (params.dataType === 'node') {
            this.showDeviceDetail(params.data)
          }
        })
      }
    },

    // 加载设备数据
    loadDeviceData () {
      this.loading = true
      // 模拟设备数据
      setTimeout(() => {
        this.devices = this.generateMockDevices()
        this.connections = this.generateMockConnections()
        this.updateStatusCounts()
        this.filterAndRenderTopology()
        this.loading = false
      }, 1000)
    },

    // 生成模拟设备数据
    generateMockDevices () {
      const devices = []
      const deviceConfigs = [
        { type: 'server', count: 8, prefix: 'SRV' },
        { type: 'switch', count: 4, prefix: 'SW' },
        { type: 'router', count: 2, prefix: 'RTR' },
        { type: 'firewall', count: 2, prefix: 'FW' },
        { type: 'storage', count: 3, prefix: 'STO' }
      ]

      let id = 1
      deviceConfigs.forEach(config => {
        for (let i = 1; i <= config.count; i++) {
          const status = this.getRandomStatus()
          devices.push({
            id: id++,
            name: `${config.prefix}-${i.toString().padStart(2, '0')}`,
            type: config.type,
            status: status,
            ip: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
            mac: this.generateMacAddress(),
            location: `机房${Math.floor(Math.random() * 3) + 1}-机柜${Math.floor(Math.random() * 20) + 1}`,
            lastUpdate: new Date().toLocaleString(),
            uptime: `${Math.floor(Math.random() * 365)}天${Math.floor(Math.random() * 24)}小时`,
            metrics: status === 'online' ? {
              cpu: Math.floor(Math.random() * 100),
              memory: Math.floor(Math.random() * 100),
              disk: Math.floor(Math.random() * 100)
            } : null,
            connections: []
          })
        }
      })

      return devices
    },

    // 生成模拟连接数据
    generateMockConnections () {
      const connections = []
      const servers = this.devices.filter(d => d.type === 'server')
      const switches = this.devices.filter(d => d.type === 'switch')
      const routers = this.devices.filter(d => d.type === 'router')
      const firewalls = this.devices.filter(d => d.type === 'firewall')

      // 服务器连接到交换机
      servers.forEach(server => {
        const targetSwitch = switches[Math.floor(Math.random() * switches.length)]
        if (targetSwitch) {
          connections.push({
            source: server.id,
            target: targetSwitch.id,
            type: 'network',
            status: 'active'
          })
        }
      })

      // 交换机连接到路由器
      switches.forEach(sw => {
        const targetRouter = routers[Math.floor(Math.random() * routers.length)]
        if (targetRouter) {
          connections.push({
            source: sw.id,
            target: targetRouter.id,
            type: 'network',
            status: 'active'
          })
        }
      })

      // 路由器连接到防火墙
      routers.forEach(router => {
        firewalls.forEach(firewall => {
          connections.push({
            source: router.id,
            target: firewall.id,
            type: 'network',
            status: 'active'
          })
        })
      })

      return connections
    },

    // 获取随机状态
    getRandomStatus () {
      const statuses = ['online', 'offline', 'warning', 'error']
      const weights = [0.7, 0.1, 0.15, 0.05] // 权重：在线70%，离线10%，告警15%，故障5%
      const random = Math.random()
      let sum = 0

      for (let i = 0; i < weights.length; i++) {
        sum += weights[i]
        if (random <= sum) {
          return statuses[i]
        }
      }
      return 'online'
    },

    // 生成MAC地址
    generateMacAddress () {
      const chars = '0123456789ABCDEF'
      let mac = ''
      for (let i = 0; i < 6; i++) {
        if (i > 0) mac += ':'
        mac += chars[Math.floor(Math.random() * 16)]
        mac += chars[Math.floor(Math.random() * 16)]
      }
      return mac
    },

    // 更新状态统计
    updateStatusCounts () {
      this.statusCounts = {
        online: 0,
        offline: 0,
        warning: 0,
        error: 0
      }

      this.devices.forEach(device => {
        this.statusCounts[device.status]++
      })
    },

    // 筛选和渲染拓扑图
    filterAndRenderTopology () {
      // 筛选设备
      this.filteredDevices = this.devices.filter(device => {
        const matchesSearch = !this.searchKeyword ||
          device.name.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
          device.ip.includes(this.searchKeyword)
        const matchesStatus = !this.selectedStatus || device.status === this.selectedStatus
        const matchesType = !this.selectedType || device.type === this.selectedType

        return matchesSearch && matchesStatus && matchesType
      })

      // 筛选连接
      const deviceIds = this.filteredDevices.map(d => d.id)
      this.filteredConnections = this.connections.filter(conn =>
        deviceIds.includes(conn.source) && deviceIds.includes(conn.target)
      )

      this.renderTopology()
    },

    // 渲染拓扑图
    renderTopology () {
      if (!this.topologyChart) return

      let nodes = this.filteredDevices.map(device => ({
        id: device.id,
        name: device.name,
        category: device.type,
        value: device,
        symbolSize: this.getNodeSize(device.type),
        itemStyle: {
          color: this.getNodeColor(device)
        },
        label: {
          show: true,
          position: 'bottom',
          fontSize: 12
        }
      }))

      // 如果是网格布局或分层布局，手动计算节点位置
      if (this.layoutType === 'grid') {
        nodes = this.calculateGridLayout(nodes)
        console.log('Using grid layout, nodes with positions:', nodes.slice(0, 3))
      } else if (this.layoutType === 'hierarchy') {
        nodes = this.calculateHierarchyLayout(nodes)
        console.log('Using hierarchy layout, nodes with positions:', nodes.slice(0, 3))
      }

      const links = this.filteredConnections.map(conn => ({
        source: conn.source,
        target: conn.target,
        lineStyle: {
          color: conn.status === 'active' ? '#1890ff' : '#d9d9d9',
          type: conn.type === 'virtual' ? 'dashed' : 'solid'
        }
      }))

      const categories = this.deviceTypes.map(type => ({
        name: type.value,
        label: type.label
      }))

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: params => {
            if (params.dataType === 'node') {
              const device = params.data.value
              return `
                <div style="padding: 8px;">
                  <div style="font-weight: bold; margin-bottom: 4px;">${device.name}</div>
                  <div>类型: ${this.getDeviceTypeLabel(device.type)}</div>
                  <div>状态: ${this.getStatusLabel(device.status)}</div>
                  <div>IP: ${device.ip}</div>
                  <div style="margin-top: 4px; color: #666; font-size: 12px;">点击查看详情</div>
                </div>
              `
            }
            return ''
          }
        },
        series: [{
          type: 'graph',
          layout: (this.layoutType === 'grid' || this.layoutType === 'hierarchy') ? 'none' : this.layoutType,
          data: nodes,
          links: links,
          categories: categories,
          roam: true,
          focusNodeAdjacency: true,
          force: {
            repulsion: 1000,
            gravity: 0.1,
            edgeLength: 200
          },
          circular: {
            rotateLabel: true
          },
          lineStyle: {
            color: 'source',
            curveness: 0.1
          },
          emphasis: {
            focus: 'adjacency',
            lineStyle: {
              width: 3
            }
          }
        }]
      }

      this.topologyChart.setOption(option, true)
    },

    // 计算网格布局
    calculateGridLayout (nodes) {
      if (nodes.length === 0) return nodes

      // 简单的矩形网格布局
      const cols = Math.ceil(Math.sqrt(nodes.length))
      const rows = Math.ceil(nodes.length / cols)
      const spacing = 120

      // 计算起始位置，使网格居中
      const totalWidth = (cols - 1) * spacing
      const totalHeight = (rows - 1) * spacing
      const startX = -totalWidth / 2
      const startY = -totalHeight / 2

      const result = nodes.map((node, index) => {
        const col = index % cols
        const row = Math.floor(index / cols)

        return {
          ...node,
          x: startX + col * spacing,
          y: startY + row * spacing,
          fixed: true
        }
      })

      console.log('Grid layout calculated:', {
        nodeCount: nodes.length,
        cols,
        rows,
        spacing,
        startX,
        startY,
        samplePositions: result.slice(0, 3).map(n => ({ id: n.id, x: n.x, y: n.y }))
      })

      return result
    },

    // 计算分层布局
    calculateHierarchyLayout (nodes) {
      if (nodes.length === 0) return nodes

      // 按设备类型分层排列
      const typeGroups = {
        firewall: [],
        router: [],
        switch: [],
        server: [],
        storage: []
      }

      // 分组设备
      nodes.forEach(node => {
        const type = node.value.type
        if (typeGroups[type]) {
          typeGroups[type].push(node)
        }
      })

      const result = []
      let currentY = 0
      const layerSpacing = 150
      const nodeSpacing = 100

      // 按层级排列设备（从上到下：防火墙 -> 路由器 -> 交换机 -> 服务器 -> 存储）
      const layerOrder = ['firewall', 'router', 'switch', 'server', 'storage']

      layerOrder.forEach(type => {
        const groupNodes = typeGroups[type]
        if (groupNodes.length === 0) return

        // 计算这一层的布局
        const cols = Math.min(8, groupNodes.length)
        const totalWidth = (cols - 1) * nodeSpacing
        const startX = -totalWidth / 2

        groupNodes.forEach((node, index) => {
          const col = index % cols
          const row = Math.floor(index / cols)

          result.push({
            ...node,
            x: startX + col * nodeSpacing,
            y: currentY + row * 80,
            fixed: true
          })
        })

        // 移动到下一层
        const rows = Math.ceil(groupNodes.length / cols)
        currentY += rows * 80 + layerSpacing
      })

      // 垂直居中
      if (result.length > 0) {
        const minY = Math.min(...result.map(n => n.y))
        const maxY = Math.max(...result.map(n => n.y))
        const centerY = (minY + maxY) / 2

        result.forEach(node => {
          node.y -= centerY
        })
      }

      console.log('Hierarchy layout calculated:', {
        nodeCount: nodes.length,
        layers: layerOrder.map(type => ({ type, count: typeGroups[type].length })),
        samplePositions: result.slice(0, 3).map(n => ({ id: n.id, type: n.value.type, x: n.x, y: n.y }))
      })

      return result
    },

    // 获取节点大小
    getNodeSize (type) {
      const sizes = {
        server: 40,
        switch: 35,
        router: 45,
        firewall: 50,
        storage: 38
      }
      return sizes[type] || 35
    },

    // 获取节点颜色
    getNodeColor (device) {
      const deviceType = this.deviceTypes.find(t => t.value === device.type)
      const typeColor = deviceType ? deviceType.color : '#1890ff'
      const statusColors = {
        online: typeColor,
        offline: '#d9d9d9',
        warning: '#fa8c16',
        error: '#f5222d'
      }
      return statusColors[device.status] || typeColor
    },

    // 搜索处理
    handleSearch (value) {
      this.searchKeyword = value
      this.filterAndRenderTopology()
    },

    // 搜索变化处理
    handleSearchChange (e) {
      this.searchKeyword = e.target.value
      this.filterAndRenderTopology()
    },

    // 状态筛选处理
    handleStatusFilter (value) {
      this.selectedStatus = value
      this.filterAndRenderTopology()
    },

    // 类型筛选处理
    handleTypeFilter (value) {
      this.selectedType = value
      this.filterAndRenderTopology()
    },

    // 改变布局
    changeLayout (layout) {
      this.layoutType = layout
      this.renderTopology()
    },

    // 刷新拓扑图
    refreshTopology () {
      this.loadDeviceData()
    },

    // 显示设备详情
    showDeviceDetail (device) {
      this.selectedDevice = device
      this.deviceDetailVisible = true
    },

    // 关闭设备详情
    closeDeviceDetail () {
      this.deviceDetailVisible = false
      this.selectedDevice = null
    },

    // 获取设备类型标签
    getDeviceTypeLabel (type) {
      const deviceType = this.deviceTypes.find(t => t.value === type)
      return deviceType ? deviceType.label : type
    },

    // 获取状态标签
    getStatusLabel (status) {
      const deviceStatus = this.deviceStatuses.find(s => s.value === status)
      return deviceStatus ? deviceStatus.label : status
    },

    // 获取状态颜色
    getStatusColor (status) {
      const deviceStatus = this.deviceStatuses.find(s => s.value === status)
      return deviceStatus ? deviceStatus.color : '#d9d9d9'
    }
  }
}
</script>

<style scoped lang="less">
.device-topology-container {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;

  .toolbar-card {
    margin-bottom: 16px;

    .toolbar-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .toolbar-left {
        display: flex;
        align-items: center;
      }

      .toolbar-right {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }

  .main-content {
    .topology-card {
      .topology-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
        }

        .status-summary {
          display: flex;
          gap: 8px;
        }
      }

      .topology-chart {
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        background: #fff;
      }
    }

    .legend-card {
      .legend-content {
        h4 {
          margin: 0 0 12px 0;
          font-size: 14px;
          font-weight: 600;
          color: #262626;
        }

        .legend-items {
          .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .legend-icon {
              width: 12px;
              height: 12px;
              border-radius: 50%;
              margin-right: 8px;
            }

            .legend-line {
              width: 20px;
              height: 0;
              border-top: 2px solid;
              margin-right: 8px;
            }

            span {
              font-size: 12px;
              color: #595959;
            }
          }
        }
      }
    }
  }

  .device-detail {
    .detail-item {
      margin-bottom: 12px;

      label {
        font-weight: 600;
        margin-right: 8px;
        color: #262626;
      }

      span {
        color: #595959;
      }
    }

    .performance-metrics {
      h4 {
        margin: 16px 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        color: #262626;
      }

      .metric-item {
        margin-bottom: 16px;

        label {
          display: block;
          margin-bottom: 4px;
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }

    .connection-info {
      h4 {
        margin: 16px 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        color: #262626;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .device-topology-container {
    .main-content {
      .legend-card {
        display: none;
      }
    }
  }
}

@media (max-width: 768px) {
  .device-topology-container {
    padding: 16px;

    .toolbar-card {
      .toolbar-content {
        flex-direction: column;
        gap: 16px;

        .toolbar-left {
          flex-wrap: wrap;
          gap: 8px;
        }

        .toolbar-right {
          flex-wrap: wrap;
        }
      }
    }

    .main-content {
      .topology-card {
        .topology-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;
        }
      }
    }
  }
}
</style>
