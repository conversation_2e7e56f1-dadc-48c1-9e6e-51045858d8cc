// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { axios, qs } from '@/utils/request'

/**
 *  creatPolineByContract: 按合同采购
 *  requestOAService: 发起 OA审批
 *  getOAFlow: 查询 OA 审批结果
 *  doExport:导出报表
 *  rfqToContractHJ:报价生成合同
 */
const api = {
  creatPolineByContract: '/contract/creatPolineByContract',
  requestOAService: '/contract/getOaServiceApi',
  getOAFlow: '/contract/getOaFlow',
  selectContract: '/contract/selectContract',
  selectContractByNum: '/contract/selectContractByNum',
  modifyContract: '/contract/modifyContract',
  selectContractLine: '/contractline/selectContractLine',
  selectContractLineForPrline: '/contractline/selectContractLineForPrline',
  modifyContractLine: '/contractline/modifyContractLine',
  creatPoline: '/contract/creatPoline',
  importContract: '/contract/importContract',
  doExport: '/contract/doExport',
  selectContractWithoutPage: '/contract/selectContractWithoutPage',
  approveContract: '/contract/approveContract',
  enableOrDisable: '/contract/enableOrDisable',
  queryAllPaymentInfo: '/contract/queryAllPaymentInfo',
  modifyPaymentInfo: '/contract/modifyPaymentInfo',
  approveContractPay: '/contract/approveContractPay',
  queryContractPrlineInfo: '/contract/queryContractPrlineInfo',
  queryContractKxPlan: '/contract/queryContractKxPlan',
  modifyContractKxPlan: '/contract/modifyContractKxPlan',
  queryApprovedFwHis: '/contract/queryApprovedFwHis',
  pushContractInfo2Fw: '/jtfw/pushContractInfo2Fw',
  queryContractKxInfo: '/contract/queryContractKxInfo',
  modifyContractKxInfo: '/contract/modifyContractKxInfo',
  pushContractKxPlan2EAS: '/contract/pushContractKxPlan2EAS'
}

export function pushContractKxPlan2EAS (data) {
  return axios({
    url: api.pushContractKxPlan2EAS,
    method: 'post',
    data: data
  })
}

export function modifyContractKxInfo (data) {
  return axios({
    url: api.modifyContractKxInfo,
    method: 'post',
    data: data
  })
}

export function queryContractKxInfo (data) {
  return axios({
    url: api.queryContractKxInfo,
    method: 'post',
    data: data
  })
}

export function pushContractInfo2Fw (data) {
  return axios({
    url: api.pushContractInfo2Fw,
    method: 'post',
    data: data
  })
}

export function queryApprovedFwHis (data) {
  return axios({
    url: api.queryApprovedFwHis,
    method: 'post',
    data: data
  })
}

export function modifyContractKxPlan (data) {
  return axios({
    url: api.modifyContractKxPlan,
    method: 'post',
    data: data
  })
}

export function queryContractKxPlan (data) {
  return axios({
    url: api.queryContractKxPlan,
    method: 'post',
    data: data
  })
}

export function queryContractPrlineInfo (data) {
  return axios({
    url: api.queryContractPrlineInfo,
    method: 'post',
    data: data
  })
}

export function approveContractPay (data) {
  return axios({
    url: api.approveContractPay,
    method: 'post',
    data: data
  })
}

export function modifyPaymentInfo (data) {
  return axios({
    url: api.modifyPaymentInfo,
    method: 'post',
    data: data
  })
}

export function queryAllPaymentInfo (data) {
  return axios({
    url: api.queryAllPaymentInfo,
    method: 'post',
    data: data
  })
}

export function creatPolineByContract (data) {
  return axios({
    url: api.creatPolineByContract,
    method: 'post',
    data: data
  })
}
export function selectContractLineForPrline (data) {
  return axios({
    url: api.selectContractLineForPrline,
    method: 'post',
    data: data
  })
}

export function selectContractWithoutPage (data) {
  return axios({
    url: api.selectContractWithoutPage,
    method: 'post',
    data: data
  })
}

export function requestOAService (data) {
  return axios({
    url: api.requestOAService,
    method: 'post',
    data: data
  })
}

export function getOAFlow (data) {
  return axios({
    url: api.getOAFlow,
    method: 'post',
    data: data
  })
}

export function creatPoline (parameter) {
  return axios({
    url: api.creatPoline,
    method: 'post',
    data: parameter
  })
}

export function modifyContractLine (parameter) {
  return axios({
    url: api.modifyContractLine,
    method: 'post',
    data: parameter
  })
}

export function selectContractLine (parameter) {
  return axios({
    url: api.selectContractLine,
    method: 'post',
    data: parameter
  })
}

export function modifyContract (parameter) {
  return axios({
    url: api.modifyContract,
    method: 'post',
    data: parameter
  })
}

export function selectContract (parameter) {
  return axios({
    url: api.selectContract,
    method: 'post',
    data: parameter
  })
}

export function selectContractByNum (parameter) {
  return axios({
    url: api.selectContract + '/' + parameter.param.id,
    method: 'post',
    data: parameter
  })
}
export function importContract (parameter) {
  return axios({
    url: api.importContract,
    method: 'post',
    data: parameter,
    paramsSerializer: function (parameter) {
      return qs.stringify(parameter, { indices: false })
    }
  })
}
export function approveContract (data) {
  return axios({
    url: api.approveContract,
    method: 'post',
    data: data
  })
}

export function doExport (data) {
  return axios({
    url: api.doExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function enableOrDisable (parameter) {
  return axios({
    url: api.enableOrDisable,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export default api
