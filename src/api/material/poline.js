// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { qs, axios } from '@/utils/request'

/**
 *  getPoLineAll: 获取订单信息
 *  modifyPoline: 修改单条订单记录
 *  modifyPoline2: 批量修改订单记录
 * savePolineList:批量新增订单
 * splitPoline: 拆分订单
 * getApproveRecordByNum：获取订单历史审批记录
 * approvePoline: 提交审批
 * doPolineExports: 订单打印
 * deletePolineList: 批量核销订单
 * getPrintData: 展示订单信息
 */
const api = {
  getPoLineAll: '/poline/queryPolineAll',
  modifyPolineInfo: '/poline/modifyPoline',
  modifyPolineInfo2: '/poline/modifyPoline2',
  savePolineList: '/poline/savePolineList',
  splitPoline: '/poline/splitPoline',
  getApproveRecordByNum: '/poline/queryApproHis',
  approvePoline: '/poline/approPoline',
  doPolineExports: '/poline/doExport',
  doPolineExportSZXD: '/poline/doPolineExportSZXD',
  deletePolineList: 'poline/deletePolineList',
  getPrintData: 'poline/getPrintData',
  approScrapPlan: '/scrap/approScrapPlan',
  getPrintMatu: '/matu/getPrintMatu',
  approScrapSend: '/scrap/approScrapSend',
  queryEntityStatusInfo: '/poline/queryEntityStatusInfo',
  startProcessEntityPoline: '/poline/startProcessEntityPoline',
  queryHJEntityPoline: '/poline/queryHJEntityPoline'
}

export function startProcessEntityPoline (data) {
  return axios({
    url: api.startProcessEntityPoline,
    method: 'post',
    data: data
  })
}

export function queryEntityStatusInfo (data) {
  return axios({
    url: api.queryEntityStatusInfo,
    method: 'post',
    data: data
  })
}

export function getPrintMatu (data) {
  return axios({
    url: api.getPrintMatu,
    method: 'post',
    data: data
  })
}

export function getPoLineAll (data) {
  return axios({
    url: api.getPoLineAll,
    method: 'post',
    data: data
  })
}

export function approScrapSend (data) {
  return axios({
    url: api.approScrapSend,
    method: 'post',
    data: data
  })
}

export function modifyPolineInfo (data) {
  return axios({
    url: api.modifyPolineInfo,
    method: 'post',
    data: data
  })
}

export function modifyPolineInfo2 (data) {
  return axios({
    url: api.modifyPolineInfo2,
    method: 'post',
    data: data
  })
}

export function savePolineList (data) {
  return axios({
    url: api.savePolineList,
    method: 'post',
    data: data
  })
}

export function splitPoline (data) {
  return axios({
    url: api.splitPoline,
    method: 'post',
    data: data
  })
}

export function approvePoline (data) {
  return axios({
    url: api.approvePoline,
    method: 'post',
    data: data
  })
}

export function getApproveRecordByNum (parameter) {
  return axios({
    url: api.getApproveRecordByNum,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function doPolineExports (data) {
  return axios({
    url: api.doPolineExports,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doPolineExportSZXD (data) {
  return axios({
    url: api.doPolineExportSZXD,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function deletePolineList (data) {
  return axios({
    url: api.deletePolineList,
    method: 'post',
    data: data
  })
}
export function getPrintData (data) {
  return axios({
    url: api.getPrintData,
    method: 'post',
    data: data
  })
}
// 报废计划审批
export function approScrapPlan (data) {
  return axios({
    url: api.approScrapPlan,
    method: 'post',
    data: data
  })
}

export function queryHJEntityPoline (data) {
  return axios({
    url: api.queryHJEntityPoline,
    method: 'post',
    data: data
  })
}

export default api
