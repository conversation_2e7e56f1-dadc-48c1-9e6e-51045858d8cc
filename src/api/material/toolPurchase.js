
// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 *  queryLabourRecord: 查询
 *  doPurchase: 采购
 *  queryLabour:通用报表查询劳保工具
 *  doExportLabourRecord：导出
 */
const api = {
  queryLabourRecord: '/labourRecord/queryLabourRecord',
  doPurchase: '/labourRecord/doPurchase',
  queryLabour: '/labourRecord/queryLabour',
  doExportLabourRecord: '/labourRecord/doExport'
}

export function queryLabourRecord (data) {
  return axios({
    url: api.queryLabourRecord,
    method: 'post',
    data: data
  })
}

export function doPurchase (data) {
  return axios({
    url: api.doPurchase,
    method: 'post',
    data: data
  })
}
export function queryLabour (data) {
  return axios({
    url: api.queryLabour,
    method: 'post',
    data: data
  })
}

export function doExportLabourRecord (data) {
  return axios({
    url: api.doExportLabourRecord,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export default api
