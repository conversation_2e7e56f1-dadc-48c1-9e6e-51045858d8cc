import { axios } from '@/utils/request'

const api = {
  getEnergyConsumptionBaseByPages: '/wzport/energy/getEnergyConsumptionBaseByPages',
  modifyEnergyConsumptionBaseInfo: '/wzport/energy/modifyEnergyConsumptionBaseInfo',
  getEnergyConsumptionBaseApprovalByPages: '/wzport/energy/getEnergyConsumptionBaseApprovalByPages',
  getEnergyConsumptionDetails: '/wzport/energy/getEnergyConsumptionDetails',
  getEnergyConsumptionReportForZHNHB: '/wzport/energyreport/getEnergyConsumptionReportForZHNHB',
  doExportZHNHB: '/wzport/energyreport/doExportZHNHB',
  getEnergyConsumptionReportForNYXHTZ: '/wzport/energyreport/getEnergyConsumptionReportForNYXHTZ',
  doExportNYXHTZ: '/wzport/energyreport/doExportNYXHTZ',
  getEnergyConsumptionReportForSCNYDH: '/wzport/energyreport/getEnergyConsumptionReportForSCNYDH',
  doExportSCNYDH: '/wzport/energyreport/doExportSCNYDH',
  getEnergyConsumptionReportForSHZX: '/wzport/energyreport/getEnergyConsumptionReportForSHZX',
  doExportSHZX: '/wzport/energyreport/doExportSHZX',
  getEnergyConsumptionReportForDHZZX: '/wzport/energyreport/getEnergyConsumptionReportForDHZZX',
  doExportDHZZX: '/wzport/energyreport/doExportDHZZX',
  getEnergyConsumptionReportForNYXHYB: '/wzport/energyreport/getEnergyConsumptionReportForNYXHYB',
  doExportNYXHYB: '/wzport/energyreport/doExportNYXHYB'
}

export function getEnergyConsumptionBaseByPages (parameter) {
  return axios({
    url: api.getEnergyConsumptionBaseByPages,
    method: 'post',
    data: parameter
  })
}

export function getEnergyConsumptionDetails (parameter) {
  return axios({
    url: api.getEnergyConsumptionDetails,
    method: 'post',
    data: parameter
  })
}

export function modifyEnergyConsumptionBaseInfo (parameter) {
  return axios({
    url: api.modifyEnergyConsumptionBaseInfo,
    method: 'post',
    data: parameter
  })
}

export function getEnergyConsumptionBaseApprovalByPages (parameter) {
  return axios({
    url: api.getEnergyConsumptionBaseApprovalByPages,
    method: 'post',
    data: parameter
  })
}

export function getEnergyConsumptionReportForZHNHB (parameter) {
  return axios({
    url: api.getEnergyConsumptionReportForZHNHB,
    method: 'post',
    data: parameter
  })
}
export function doExportZHNHB (parameter) {
  return axios({
    url: api.doExportZHNHB,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}

export function getEnergyConsumptionReportForNYXHTZ (parameter) {
  return axios({
    url: api.getEnergyConsumptionReportForNYXHTZ,
    method: 'post',
    data: parameter
  })
}

export function doExportNYXHTZ (parameter) {
  return axios({
    url: api.doExportNYXHTZ,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}

export function getEnergyConsumptionReportForSCNYDH (parameter) {
  return axios({
    url: api.getEnergyConsumptionReportForSCNYDH,
    method: 'post',
    data: parameter
  })
}

export function doExportSCNYDH (parameter) {
  return axios({
    url: api.doExportSCNYDH,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}
export function getEnergyConsumptionReportForSHZX (parameter) {
  return axios({
    url: api.getEnergyConsumptionReportForSHZX,
    method: 'post',
    data: parameter
  })
}
export function doExportSHZX (parameter) {
  return axios({
    url: api.doExportSHZX,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}

export function getEnergyConsumptionReportForDHZZX (parameter) {
  return axios({
    url: api.getEnergyConsumptionReportForDHZZX,
    method: 'post',
    data: parameter
  })
}
export function doExportDHZZX (parameter) {
  return axios({
    url: api.doExportDHZZX,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}

export function getEnergyConsumptionReportForNYXHYB (parameter) {
  return axios({
    url: api.getEnergyConsumptionReportForNYXHYB,
    method: 'post',
    data: parameter
  })
}
export function doExportNYXHYB (parameter) {
  return axios({
    url: api.doExportNYXHYB,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}
