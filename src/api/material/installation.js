import { axios } from '@/utils/request'

const api = {
  getInstallationCompanyByPages: '/wzport/installation/getInstallationCompanyByPages',
  modifyInstallationCompanyInfo: '/wzport/installation/modifyInstallationCompanyInfo',
  getInstallationCompanys: '/wzport/installation/getInstallationCompanys',
  getInstallationMaintenanceByPages: '/wzport/installation/getInstallationMaintenanceByPages',
  modifyInstallationMaintenanceInfo: '/wzport/installation/modifyInstallationMaintenanceInfo',
  getInstallationServiceByPages: '/wzport/installation/getInstallationServiceByPages',
  modifyInstallationServiceInfo: '/wzport/installation/modifyInstallationServiceInfo'
}

export function getInstallationCompanyByPages (parameter) {
  return axios({
    url: api.getInstallationCompanyByPages,
    method: 'post',
    data: parameter
  })
}

export function modifyInstallationCompanyInfo (parameter) {
  return axios({
    url: api.modifyInstallationCompanyInfo,
    method: 'post',
    data: parameter
  })
}
export function getInstallationCompanys (parameter) {
  return axios({
    url: api.getInstallationCompanys,
    method: 'post',
    data: parameter
  })
}

export function getInstallationMaintenanceByPages (parameter) {
  return axios({
    url: api.getInstallationMaintenanceByPages,
    method: 'post',
    data: parameter
  })
}

export function modifyInstallationMaintenanceInfo (parameter) {
  return axios({
    url: api.modifyInstallationMaintenanceInfo,
    method: 'post',
    data: parameter
  })
}

export function getInstallationServiceByPages (parameter) {
  return axios({
    url: api.getInstallationServiceByPages,
    method: 'post',
    data: parameter
  })
}

export function modifyInstallationServiceInfo (parameter) {
  return axios({
    url: api.modifyInstallationServiceInfo,
    method: 'post',
    data: parameter
  })
}
