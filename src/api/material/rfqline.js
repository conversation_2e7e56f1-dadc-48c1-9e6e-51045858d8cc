// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { qs, axios } from '@/utils/request'
// import { axios } from '@/utils/request'

/**
 *  queryRfqLineInfo: 查询询价单
 *  modifyRfqLineInfo: 修改询价单
 *  cancelRfqlineInfo: 取消询价单
 *  splitProcurePlan: 采购单拆分
 *  queryApproHis: 获取审批记录
 *  approRfqline: 审批操作
 *  biddingPlanSend: '发送给招标采购平台'
 *  getTenderInfo: '查询招标信息'
 *  updateRfqTenderMethodDetail: '更新非招简单询价方式'
 */
const api = {
  queryRfqLineInfo: '/rfqline/queryRfqLineInfo',
  modifyRfqLineInfo: '/rfqline/modifyRfqLineInfo',
  cancelRfqlineInfo: '/rfqline/cancelRfqline',
  splitProcurePlan: '/rfqline/splitRfqline',
  addRfqLineVendor: '/rfqline/addVendor',
  queryApproHis: '/rfqline/queryApproHis',
  approRfqline: '/rfqline/approRfqline',
  doExportRfq: '/rfqline/doExportRfq',
  biddingPlanSend: '/rfqline/biddingPlanSend',
  doExport: '/rfqline/doExport',
  getTenderInfo: '/rfqline/getTenderInfo',
  uploadJtoaTender: '/rfqline/uploadJtoaTender',
  uploadHjJtoaTender: '/rfqline/uploadHjJtoaTender',
  updateRfqTenderMethodDetail: '/rfqline/updateRfqTenderMethodDetail',
  getAllCompany: '/company/getAllCompany',
  approveTenderRfq: '/rfqline/approveTenderRfq',
  getHjContractDetail: '/rfqline/getHjContractDetail',
  findJtoaCockpit: '/rfqline/findJtoaCockpit',
  findMatrCockpit: '/matr/findMatrCockpit',
  findContractCockpit: '/contract/findContractCockpit'
}

export function findContractCockpit (data) {
  return axios({
    url: api.findContractCockpit,
    method: 'post',
    data: data
  })
}

export function findMatrCockpit (data) {
  return axios({
    url: api.findMatrCockpit,
    method: 'post',
    data: data
  })
}

export function findJtoaCockpit (data) {
  return axios({
    url: api.findJtoaCockpit,
    method: 'post',
    data: data
  })
}

export function getHjContractDetail (data) {
  return axios({
    url: api.getHjContractDetail,
    method: 'post',
    data: data
  })
}

export function getAllCompany (data) {
  return axios({
    url: api.getAllCompany,
    method: 'post',
    data: data
  })
}

export function uploadHjJtoaTender (data) {
  return axios({
    url: api.uploadHjJtoaTender,
    method: 'post',
    data: data
  })
}

export function queryRfqLineInfo (data) {
  return axios({
    url: api.queryRfqLineInfo,
    method: 'post',
    data: data
  })
}

export function uploadJtoaTender (data) {
  return axios({
    url: api.uploadJtoaTender,
    method: 'post',
    data: data
  })
}

export function modifyRfqLineInfo (data) {
  return axios({
    url: api.modifyRfqLineInfo,
    method: 'post',
    data: data
  })
}

export function cancelRfqlineInfo (data) {
  return axios({
    url: api.cancelRfqlineInfo,
    method: 'post',
    data: data
  })
}

export function splitProcurePlan (data) {
  return axios({
    url: api.splitProcurePlan,
    method: 'post',
    data: data
  })
}

export function addRfqLineVendor (data) {
  return axios({
    url: api.addRfqLineVendor,
    method: 'post',
    data: data
  })
}

export function queryApproHis (parameter) {
  return axios({
    url: api.queryApproHis,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function approRfqline (data) {
  return axios({
    url: api.approRfqline,
    method: 'post',
    data: data
  })
}

export function doExportRfq (parameter) {
  return axios({
    url: api.doExportRfq,
    method: 'post',
    params: parameter,
    responseType: 'blob',
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function biddingPlanSend (data) {
  return axios({
    url: api.biddingPlanSend,
    method: 'post',
    data: data
  })
}

export function doExport (data) {
  return axios({
    url: api.doExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function getTenderInfo (data) {
  return axios({
    url: api.getTenderInfo,
    method: 'post',
    data: data
  })
}

export function updateRfqTenderMethodDetail (data) {
  return axios({
    url: api.updateRfqTenderMethodDetail,
    method: 'post',
    data: data
  })
}

export function approveTenderRfq (data) {
  return axios({
    url: api.approveTenderRfq,
    method: 'post',
    data: data
  })
}

export default api
