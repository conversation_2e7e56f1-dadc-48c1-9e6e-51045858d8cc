// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { qs, axios } from '@/utils/request'

/**
 *  queryVendorInfo: 获取供应商信息
 *  modifyVendorInfo: 增删改供应商信息
 *  queryVendorContact: 获取供应商联系人信息
 *  modifyVendorContact: 修改供应商联系人
 *  queryVendorScore: 查询供应商评分
 *  modifyVendorScore: 修改供应商评分
 *  approVendor: 供应商审批
 *  getApproVendor: 根据操作人返回需要审批的供应商
 *  queryApproHis: 根据vendorSysId查审批历史
 *  reportOfVendor: '向ETMS上报供应商代码'
 *  trueDeleteVendor: 真实删除供应商（慎用）
 */
const api = {
  queryVendorInfo: '/vendor/queryVendorInfo',
  modifyVendorInfo: '/vendor/modifyVendorInfo',
  queryVendorContact: '/vendor/queryVendorContact',
  modifyVendorContact: '/vendor/modifyVendorContact',
  queryVendorScore: '/vendor/queryVendorScore',
  modifyVendorScore: '/vendor/modifyVendorScore',
  approVendor: '/vendor/approVendor',
  getApproVendor: '/vendor/getApproVendor',
  queryApproHis: '/vendor/queryApproHis',
  trueDeleteVendor: '/vendor/trueDeleteVendor',
  reportOfVendor: '/vendor/reportOfVendor',
  queryMachineSelectVendor: '/vendor/queryMachineSelectVendor'
}

export function queryMachineSelectVendor (parameter) {
  return axios({
    url: api.queryMachineSelectVendor,
    method: 'post',
    data: parameter
  })
}

export function queryVendorInfo (parameter) {
  return axios({
    url: api.queryVendorInfo,
    method: 'post',
    data: parameter
  })
}

export function modifyVendorInfo (parameter) {
  return axios({
    url: api.modifyVendorInfo,
    method: 'post',
    data: parameter
  })
}

export function queryVendorContact (parameter) {
  return axios({
    url: api.queryVendorContact,
    method: 'post',
    data: parameter
  })
}

export function modifyVendorContact (parameter) {
  return axios({
    url: api.modifyVendorContact,
    method: 'post',
    data: parameter
  })
}

export function queryVendorScore (parameter) {
  return axios({
    url: api.queryVendorScore,
    method: 'post',
    data: parameter
  })
}

export function modifyVendorScore (parameter) {
  return axios({
    url: api.modifyVendorScore,
    method: 'post',
    data: parameter
  })
}

//  判断供应商评分是否已存在
export function isVendorScoreExist (parameter) {
  return axios({
    url: 'vendor/isVendorScoreExist',
    method: 'post',
    data: parameter
  })
}

export function approVendor (parameter) {
  return axios({
    url: api.approVendor,
    method: 'post',
    data: parameter
  })
}

export function getApproVendor (parameter) {
  return axios({
    url: api.getApproVendor,
    method: 'get',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function queryApproHis (parameter) {
  return axios({
    url: api.queryApproHis,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function reportOfVendor (parameter) {
  return axios({
    url: api.reportOfVendor,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function trueDeleteVendor (parameter) {
  return axios({
    url: api.trueDeleteVendor,
    method: 'post',
    data: parameter
  })
}
