
// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { axios } from '@/utils/request'

/**
 *  queryMatrDetail: 查询入库明细
 *  queryMatuDetail: 查询出库明细
 *  doMatrExports: 入库明细打印
 *  doMatuExports: 出库明细打印
 * doExportZLlyqdmx 导出清单明细
 * doExportZLlyqd 导出清单
 * doExportZLlymx 导出明细
 * doExportZLlytj 导出领用统计
 * doExportZLkh  导出考核出库
 */
const api = {
  queryMatrDetail: '/matr/queryMatrDetail',
  queryMatuDetail: '/matu/queryMatuDetail',
  doMatrExports: '/matr/doMatrDetailExports',
  doMatrExportsWZG: '/matr/doMatrDetailExportsWZG',
  doMatuExports: '/matu/doMatuDetailExports',
  doExportNBCTMatu: '/matu/doExportNBCTMatu',
  doExportZLlyqdmx: '/matu/doExportZLlyqdmx',
  doExportZLlyqd: '/matu/doExportZLlyqd',
  doExportZLlymx: '/matu/doExportZLlymx',
  doExportZLlytj: '/matu/doExportZLlytj',
  doExportZLkh: '/matu/doExportZLkh',
  doExportWgDeptMatu: '/matu/doExportWgDeptMatu'
}

export function queryMatrDetail (data) {
  return axios({
    url: api.queryMatrDetail,
    method: 'post',
    data: data
  })
}

export function queryMatuDetail (data) {
  return axios({
    url: api.queryMatuDetail,
    method: 'post',
    data: data
  })
}

export function doMatrExports (data) {
  return axios({
    url: api.doMatrExports,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doMatrExportsWZG (data) {
  return axios({
    url: api.doMatrExportsWZG,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function doMatuExports (data) {
  return axios({
    url: api.doMatuExports,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function doExportNBCTMatu (data) {
  return axios({
    url: api.doExportNBCTMatu,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function doExportZLlyqdmx (data) {
  return axios({
    url: api.doExportZLlyqdmx,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportZLlyqd (data) {
  return axios({
    url: api.doExportZLlyqd,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportZLlymx (data) {
  return axios({
    url: api.doExportZLlymx,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportZLlytj (data) {
  return axios({
    url: api.doExportZLlytj,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportZLkh (data) {
  return axios({
    url: api.doExportZLkh,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function doExportWgDeptMatu (data) {
  return axios({
    url: api.doExportWgDeptMatu,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export default api
