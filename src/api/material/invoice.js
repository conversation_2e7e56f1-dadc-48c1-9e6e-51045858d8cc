// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
import { axios } from '@/utils/request'

/**
 *  getInvoiceWithPages: 获取发票主体信息
 *  getInvoiceDetailWithPages: 获取发票详细信息
 *  modifyInvoice：发票主体信息增、删、改
 *  modifyInvoiceDetail：发票详细信息增、删、改
 */
const api = {
  getInvoiceWithPages: '/invoice/queryInvoiceAll',
  getInvoiceDetailWithPages: '/invoice/queryInvoiceDetail',
  modifyInvoice: '/invoice/modifyInvoice',
  modifyInvoiceDetail: '/invoice/modifyInvoiceDetail'
}

export function getInvoiceWithPages (parameter) {
  return axios({
    url: api.getInvoiceWithPages,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function getInvoiceDetailWithPages (parameter) {
  return axios({
    url: api.getInvoiceDetailWithPages,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function modifyInvoice (parameter) {
  return axios({
    url: api.modifyInvoice,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function modifyInvoiceDetail (parameter) {
  return axios({
    url: api.modifyInvoiceDetail,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export default api
