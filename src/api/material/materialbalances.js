// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 *  inventoryProfit: 盘盈
 *  inventoryLoss: 盘亏
 *  queryInventoryDetail：查询库存明细
 *  doExportInventoryDetail：打印库存明细信息
 *  getBanlanceByDate: 根据年月获取库存总数量和金额
 * exportMaterialBalances:  对账单明细
 */
const api = {
  inventoryProfit: '/materialbalances/inventoryProfit',
  inventoryLoss: '/materialbalances/inventoryLoss',
  queryInventoryDetail: '/materialbalances/queryInventoryDetail',
  doExportInventoryDetail: '/materialbalances/doExport',
  getBanlanceByDate: '/materialbalances/getBanlanceByDate',
  exportMaterialBalances: '/materialbalances/exportMaterialBalances',
  queryProfitAndLoss: '/profitAndLoss/queryProfitAndLoss',
  modifyProfitAndLoss: '/profitAndLoss/modifyProfitAndLoss'
}

export function inventoryProfit (data) {
  return axios({
    url: api.inventoryProfit,
    method: 'post',
    data: data
  })
}

export function getBanlanceByDate (data) {
  return axios({
    url: api.getBanlanceByDate,
    method: 'post',
    data: data
  })
}

export function inventoryLoss (data) {
  return axios({
    url: api.inventoryLoss,
    method: 'post',
    data: data
  })
}

export function queryInventoryDetail (data) {
  return axios({
    url: api.queryInventoryDetail,
    method: 'post',
    data: data
  })
}

export function queryProfitAndLoss (data) {
  return axios({
    url: api.queryProfitAndLoss,
    method: 'post',
    data: data
  })
}

export function modifyProfitAndLoss (data) {
  return axios({
    url: api.modifyProfitAndLoss,
    method: 'post',
    data: data
  })
}

export function doExportInventoryDetail (data) {
  return axios({
    url: api.doExportInventoryDetail,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function exportMaterialBalances (data) {
  return axios({
    url: api.exportMaterialBalances,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export default api
