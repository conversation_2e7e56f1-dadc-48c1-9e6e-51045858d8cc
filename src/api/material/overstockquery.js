// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
import { axios } from '@/utils/request'

/**
 *  getOverstockInfo: 获取各单据审批状态详细信息
 *  exportOverStock: 下载积压库存表格
 */
const api = {
  getOverstockInfo: '/overstock/queryOverstockInfo',
  exportOverStock: '/overstock/doExport',
  doAgingMaterialsExport: '/overstock/doAgingMaterialsExport'
}

export function getOverstockInfo (parameter) {
  return axios({
    url: api.getOverstockInfo,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function exportOverStock (data) {
  return axios({
    url: api.exportOverStock,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doAgingMaterialsExport (data) {
  return axios({
    url: api.doAgingMaterialsExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export default api
