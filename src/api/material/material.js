// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
import { axios, qs } from '@/utils/request'

/**
 *  getMaterialInfoWithCurbal: 获取物资代码详细信息(含余量)
 *  getMaterialInfo: 获取物资代码详细信息
 *  getMaterialForm: 获取物资代码详细信息和余量信息
 *  modifyMaterialInfo: 物资代码信息增、删、改
 *  getMaterialQrCode: 获取物资二维码
 *  querySecondStoreHouseMaterialForm: 获取二级库物资
 *  getMaterialNumList: '获取物资编码'
 *  reportOfUniOrder: '向ETMS上报统购物资'
 *  deleteAbsenceMaterial: '删除被弃用的缺位物资编码'
 *  getDisabledMaterial: '获取禁用的物资编码'
 *  getQrCodeList: '获得物资对应的二维码'
 *  getOfficeSuppliesMaterial: '获取办公用品物资列表'
 *  enableOrDisable: '启用或禁用物资代码'
 *  getMaterialFormHJ: 获取物资代码详细信息和余量信息(过滤库存为0的物资)
 */
const api = {
  queryMaterialBalances: '/materialbalances/queryMaterialBalances',
  getMaterialInfo: '/material/queryMaterialInfo',
  getMaterialForm: '/material/queryMaterialForm',
  modifyMaterialInfo: '/material/modifyMaterialInfo',
  getMaterialQrCode: '/material/getQrCode',
  approveMaterial: '/material/approMaterial',
  doExportByTypeDetail: '/materialbalances/doExportByTypeDetail',
  querySecondStoreHouseMaterialForm: '/material/querySecondStoreHouseMaterialForm',
  queryMaterialByNum: '/material/queryMaterialByNum',
  reportOfUniOrder: '/material/reportOfUniOrder',
  deleteAbsenceMaterial: '/material/deleteAbsenceMaterial',
  getDisabledMaterial: '/material/getDisabledMaterial',
  doExportMaterialInfo: '/material/doExportMaterialInfo',
  getQrCodeList: '/material/getQrCodeList',
  queryExistence: '/material/queryExistence',
  enableOrDisable: '/material/enableOrDisable',
  getOfficeSuppliesMaterial: '/material/getOfficeSuppliesMaterial',
  exportTemplate: '/file/modelDownload?fileName=物资代码导入模板',
  selectContractLine: '/contractline/selectContractLine',
  findHJJgSelectedMaterial: '/prline/findHJJgSelectedMaterial',
  existingProcess: '/material/existingProcess',
  queryMaterialInfo: '/material/queryMaterialInfo',
  getMaterialFormHJ: '/material/queryMaterialFormHJ'
}

export function queryMaterialInfo (data) {
  return axios({
    url: api.queryMaterialInfo,
    method: 'post',
    data: data
  })
}

export function existingProcess (parameter) {
  return axios({
    url: api.existingProcess,
    method: 'get',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function findHJJgSelectedMaterial (data) {
  return axios({
    url: api.findHJJgSelectedMaterial,
    method: 'post',
    data: data
  })
}

export function doExportByTypeDetail (parameter) {
  return axios({
    url: api.doExportByTypeDetail,
    method: 'post',
    data: parameter,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function queryMaterialBalances (parameter) {
  return axios({
    url: api.queryMaterialBalances,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function getMaterialInfo (parameter) {
  return axios({
    url: api.getMaterialInfo,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function getMaterialForm (parameter) {
  return axios({
    url: api.getMaterialForm,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function modifyMaterialInfo (parameter) {
  return axios({
    url: api.modifyMaterialInfo,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function reportOfUniOrder (parameter) {
  return axios({
    url: api.reportOfUniOrder,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function approveMaterial (data) {
  return axios({
    url: api.approveMaterial,
    method: 'post',
    data: data
  })
}

export function getMaterialQrCode (data) {
  return axios({
    url: api.getMaterialQrCode,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}

export function queryExistence (parameter) {
  return axios({
    url: api.queryExistence,
    method: 'get',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function enableOrDisable (parameter) {
  return axios({
    url: api.enableOrDisable,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function querySecondStoreHouseMaterialForm (parameter) {
  return axios({
    url: api.querySecondStoreHouseMaterialForm,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function queryMaterialByNum (data) {
  return axios({
    url: api.queryMaterialByNum,
    method: 'post',
    data: data
  })
}

export function deleteAbsenceMaterial (data) {
  return axios({
    url: api.deleteAbsenceMaterial,
    method: 'post',
    data: data
  })
}

export function getDisabledMaterial (data) {
  return axios({
    url: api.getDisabledMaterial,
    method: 'post',
    data: data
  })
}

export function doExportMaterialInfo (parameter) {
  return axios({
    url: api.doExportMaterialInfo,
    method: 'post',
    data: parameter,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function getQrCodeList (parameter) {
  return axios({
    url: api.getQrCodeList,
    method: 'post',
    data: parameter,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function getOfficeSuppliesMaterial (parameter) {
  return axios({
    url: api.getOfficeSuppliesMaterial,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function exportTemplate (data) {
  return axios({
    url: api.exportTemplate,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export function selectContractLine (data) {
  return axios({
    url: api.selectContractLine,
    method: 'post',
    data: data
  })
}

export function getMaterialFormHJ (parameter) {
  return axios({
    url: api.getMaterialFormHJ,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export default api
