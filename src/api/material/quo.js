import { axios, qs } from '@/utils/request'

/**
 *  queryRfqNum: 返回所有询价单
 *  queryQuoInfo: 筛选框供应商查询
 *  queryQuoAll: 筛选框报价信息查询
 *  queryVenQuoInfo: 供应商联动报价信息查询
 *  modifyQuoInfo: 报价单行修改、授予
 *  modifyQuoGrantAll: 报价全部授予/全部取消
 *  rfqToPoline: 生成订单
 *  doExport: 下载报价单
 *  writeOff: 报价单核销
 *  approQuo: 审批报价单
 *  queryApproHis: 查询流程历史
 *  queryApproRfqNum: 查询审批待办
 *  modifyTotalPrice: 修改报价合计，即供应商报价
 *  rfqToContract: 生产合同
 *  rfqToContractHJ: 生成合同(海建)
 *  doExportOfScrapSend: 采购评估表
 */
const api = {
  queryRfqNum: '/quo/queryRfqNum',
  queryQuoInfo: '/quo/queryQuoInfo',
  queryQuoAll: '/quo/queryQuoAll',
  queryVenQuoInfo: '/quo/queryVenQuoInfo',
  modifyQuoGrantAll: '/quo/grantAll',
  modifyQuoInfo: '/quo/modifyQuoInfo',
  rfqToPoline: '/quo/rfqToPoline',
  doExport: '/quo/doExport',
  doQuoExport: '/quo/doExportJYS',
  writeOff: '/quo/writeOff',
  approQuo: '/quo/approQuo',
  queryApproHis: '/quo/queryApproHis',
  queryApproRfqNum: '/quo/queryApproRfqNum',
  rfqToContract: '/quo/rfqToContract',
  rfqToContractHJ: '/quo/rfqToContractHJ',
  doExportOfAppraisalForm: '/quo/doExportOfAppraisalForm',
  getAppraisalFormContent: '/quo/getAppraisalFormContent'
}

export function getAppraisalFormContent (parameter) {
  return axios({
    url: api.getAppraisalFormContent,
    method: 'get',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function queryRfqNum (parameter) {
  return axios({
    url: api.queryRfqNum,
    method: 'post',
    data: parameter
  })
}

export function queryQuoInfo (parameter) {
  return axios({
    url: api.queryQuoInfo,
    method: 'post',
    data: parameter
  })
}

export function queryQuoAll (parameter) {
  return axios({
    url: api.queryQuoAll,
    method: 'post',
    data: parameter
  })
}

export function queryVenQuoInfo (parameter) {
  return axios({
    url: api.queryVenQuoInfo,
    method: 'post',
    data: parameter
  })
}

export function modifyQuoGrantAll (parameter) {
  return axios({
    url: api.modifyQuoGrantAll,
    method: 'post',
    data: parameter
  })
}

export function modifyQuoInfo (parameter) {
  return axios({
    url: api.modifyQuoInfo,
    method: 'post',
    data: parameter
  })
}

export function rfqToPoline (parameter) {
  return axios({
    url: api.rfqToPoline,
    method: 'post',
    data: parameter
  })
}

export function doExport (parameter) {
  return axios({
    url: api.doExport,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}

export function doQuoExport (parameter) {
  return axios({
    url: api.doQuoExport,
    responseType: 'blob',
    method: 'post',
    data: parameter
  })
}

export function writeOff (parameter) {
  return axios({
    url: api.writeOff,
    method: 'post',
    data: parameter
  })
}

export function approQuo (parameter) {
  return axios({
    url: api.approQuo,
    method: 'post',
    data: parameter
  })
}

export function queryApproHis (parameter) {
  return axios({
    url: api.queryApproHis,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function queryApproRfqNum (parameter) {
  return axios({
    url: api.queryApproRfqNum,
    method: 'get',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function rfqToContract (parameter) {
  return axios({
    url: api.rfqToContract,
    method: 'post',
    data: parameter
  })
}

export function doExportOfAppraisalForm (parameter) {
  return axios({
    url: api.doExportOfAppraisalForm,
    method: 'post',
    responseType: 'blob',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function rfqToContractHJ (data) {
  return axios({
    url: api.rfqToContractHJ,
    method: 'post',
    data: data
  })
}
