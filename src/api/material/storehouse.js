// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 *  queryStorehouse: 仓库查询
 *  querySecondStorehouse: 二级仓查询
 *  queryStorage: 收发存查询
 *  querySRLG: 仑港收发存查询,
 *  querySRTMG: 头门港收发存查询,
 *  doExportLG: 仑港收发存导出
 *  querySRZL: 众联收发存查询
 *  doExportZL: 众联收发存导出
 *  doExportTMG: 头门港收发存导出
 *  querySR26: 温州港26大类收发存
 * doExportDepartment:导出嘉兴部门出库金额汇总
 */
const api = {
  queryStorehouse: '/storehouse/queryStorehouse',
  queryStorage: '/SendReceive/querySRInfo',
  querySRSum: '/SendReceive/querySRSum',
  doExport: '/SendReceive/doExport',
  querySRLG: '/SendReceive/querySRLG',
  doExportLG: '/SendReceive/doExportLG',
  querySecondStorehouse: '/storehouse/querySecondStorehouse',
  querySRZL: '/SendReceive/querySRZL',
  querySRTMG: '/SendReceive/querySRTMG',
  querySR26: '/SendReceive/querySR26',
  queryWGSYBM: '/SendReceive/queryWGSYBM',
  doExportZL: '/SendReceive/doExportZL',
  doExportTMG: '/SendReceive/doExportTMG',
  doExportDZ: '/SendReceive/doExportDZ',
  queryStorageJx: '/SendReceive/querySRInfoJx',
  querySRSumJx: '/SendReceive/querySRSumJx',
  doExportJX: '/SendReceive/doExportJX',
  doExportMD: '/SendReceive/doExportMD',
  doExportTS: '/SendReceive/doExportTS',
  doExportTSSC: '/SendReceive/doExportTSSC',
  doExportTSRL: '/SendReceive/doExportTSRL',
  doExportDepartment: '/SendReceive/selectDepartmentJx',
  doExportNBCTSFC: '/SendReceive/doExportNBCTSFC',
  doExportType: '/SendReceive/doExportType'
}

export function doExport (data) {
  return axios({
    url: api.doExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportJX (data) {
  return axios({
    url: api.doExportJX,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportMD (data) {
  return axios({
    url: api.doExportMD,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportTS (data) {
  return axios({
    url: api.doExportTS,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function doExportTSSC (data) {
  return axios({
    url: api.doExportTSSC,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function doExportTSRL (data) {
  return axios({
    url: api.doExportTSRL,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function queryStorehouse (data) {
  return axios({
    url: api.queryStorehouse,
    method: 'post',
    data: data
  })
}

export function queryStorage (data) {
  return axios({
    url: api.queryStorage,
    method: 'post',
    data: data
  })
}

export function querySRSum (data) {
  return axios({
    url: api.querySRSum,
    method: 'post',
    data: data
  })
}

export function querySRLG (data) {
  return axios({
    url: api.querySRLG,
    method: 'post',
    data: data
  })
}

export function doExportLG (data) {
  return axios({
    url: api.doExportLG,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function querySecondStorehouse (data) {
  return axios({
    url: api.querySecondStorehouse,
    method: 'post',
    data: data
  })
}

export function querySRZL (data) {
  return axios({
    url: api.querySRZL,
    method: 'post',
    data: data
  })
}

export function querySRTMG (data) {
  return axios({
    url: api.querySRTMG,
    method: 'post',
    data: data
  })
}

export function querySR26 (data) {
  return axios({
    url: api.querySR26,
    method: 'post',
    data: data
  })
}

export function queryWGSYBM (data) {
  return axios({
    url: api.queryWGSYBM,
    method: 'post',
    data: data
  })
}

export function doExportDepartment (data) {
  return axios({
    url: api.doExportDepartment,
    method: 'post',
    data: data
  })
}
export function doExportZL (data) {
  return axios({
    url: api.doExportZL,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportTMG (data) {
  return axios({
    url: api.doExportTMG,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function doExportDZ (data) {
  return axios({
    url: api.doExportDZ,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryStorageJx (data) {
  return axios({
    url: api.queryStorageJx,
    method: 'post',
    data: data
  })
}

export function querySRSumJx (data) {
  return axios({
    url: api.querySRSumJx,
    method: 'post',
    data: data
  })
}

export function doExportNBCTSFC (data) {
  return axios({
    url: api.doExportNBCTSFC,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportType (data) {
  return axios({
    url: api.doExportType,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export default api
