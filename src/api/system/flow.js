// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios, qs } from '@/utils/request'

/**
 *  findTodoCount: 获取各类待办统计
 *  findDoneCount: 获取各类已办统计
 *  findTodoByOrigin: 获取各类待办
 *  findDoneByOrigin: 获取各类已办
 */
const api = {
  findTodoCount: '/todo/findToDoTasksSummary',
  findDoneCount: '/done/findTasksSummary',
  findTodoByOrigin: '/todo',
  findDoneByOrigin: '/done',
  toBeCcTotal: 'toBeCcTotal',
  toBeCcDetail: 'toBeCcDetail',
  endCc: 'endCc'
}

export function findTodoCount (data) {
  return axios({
    url: api.findTodoCount,
    method: 'post',
    data: data
  })
}

export function findDoneCount (data) {
  return axios({
    url: api.findDoneCount,
    method: 'post',
    data: data
  })
}

export function findTodoByOrigin (url, data) {
  return axios({
    url: api.findTodoByOrigin + '/' + url,
    method: 'post',
    data: data
  })
}

export function findDoneByOrigin (url, data) {
  return axios({
    url: api.findDoneByOrigin + '/' + url,
    method: 'post',
    data: data
  })
}

export function toBeCcTotal (parameter) {
  return axios({
    url: api.toBeCcTotal,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function toBeCcDetail (data) {
  return axios({
    url: api.toBeCcDetail,
    method: 'post',
    data: data
  })
}

export function endCc (parameter) {
  return axios({
    url: api.endCc,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}
export default api
