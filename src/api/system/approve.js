import { qs, axios } from '@/utils/request'

/**
 *  queryAssigee: 下一个审批人
 *  queryApproHis: 查询审批历史
 */
const api = {
  queryAssigee: '/appro/queryPrlineAssigee',
  queryApproHis: '/appro/queryApproHis'
}

export function queryAssigee (data) {
  return axios({
    url: api.queryAssigee,
    method: 'post',
    data: data
  })
}

export function queryApproHis (data) {
  return axios({
    url: api.queryApproHis,
    method: 'post',
    params: data,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export default api
