// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { axios } from '@/utils/request'

/**
 *  getAuditTableList: 获取审计表列表
 *  modifyAuditTable: 修改审计表信息
 *  getAuditTableByPages: 分页获取审计表
 *  getAuditFieldList: 获取审计字段列表
 *  modifyAuditField: 修改审计字段信息
 */
const api = {
  queryAuditTable: '/audit/queryAuditTable',
  modifyAuditTable: '/audit/modifyAuditTable',
  queryAuditTableDetail: '/audit/queryAuditTableDetail',
  modifyAuditTableDetail: '/audit/modifyAuditTableDetail'
}
export function queryAuditTable (data) {
  return axios({
    url: api.queryAuditTable,
    method: 'post',
    data: data
  })
}

export function modifyAuditTable (parameter) {
  return axios({
    url: api.modifyAuditTable,
    method: 'post',
    data: parameter
  })
}

export function queryAuditTableDetail (parameter) {
  return axios({
    url: api.queryAuditTableDetail,
    method: 'post',
    data: parameter
  })
}

export function modifyAuditTableDetail (parameter) {
  return axios({
    url: api.modifyAuditTableDetail,
    method: 'post',
    data: parameter
  })
}

export default api
