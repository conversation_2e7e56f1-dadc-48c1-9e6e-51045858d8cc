// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { qs, axios } from '@/utils/request'

/**
 * deleteFile: 删除附件
 * listFiles: 查看附件
 */
const api = {
  deleteFile: '/file/deleteFile',
  listFiles: '/file/listFiles',
  fileUpload: '/file/fileUploadMinio',
  getPicMinio: '/file/getPicMinio',
  deleteFileMinio: '/file/deleteFileMinio',
  queryUploadFile: '/file/queryUploadFile'
}

export function queryUploadFile (data) {
  return axios({
    url: api.queryUploadFile,
    method: 'post',
    data: data
  })
}

export function deleteFile (parameter) {
  return axios({
    url: api.deleteFile,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function listFiles (parameter) {
  return axios({
    url: api.listFiles,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function fileUpload (data) {
  return axios({
    url: api.fileUpload,
    method: 'post',
    data
  })
}

export function getPicMinio (data) {
  return axios({
    url: api.getPicMinio,
    method: 'post',
    data: data
  })
}

export function deleteFileMinio (params) {
  return axios({
    url: api.deleteFileMinio,
    method: 'delete',
    params
  })
}
export default api
