// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { axios } from '@/utils/request'
import { ORG_ID } from '@/store/mutation-types'
import Vue from 'vue'

/**
 *  getTreeById: 通过id查询公共模块对应tree型数据
 *  getCommboxById: 通过id查询公共模块对应下拉框数据
 *  getOrgInfo: 获取组织信息
 *  getOrgTreeInfo: 获取组织节点信息
 *  modifyOrgDeptInfo: 修改组织部门信息
 *  deleteOrgDeptInfo: 删除组织部门信息
 *  getDeptInfo: 获取部门信息
 *  getPersonInfo: 获取员工信息
 *  modifyPersonInfo: 修改员工信息
 *  getPersonInfoAll: 查询员工信息（不分页）
 */
const api = {
  getTreeById: '/base/queryTreeNodes',
  getCommboxById: '/base/queryCommboxNodes',
  getOrgInfo: 'org/queryOrgsInfo',
  getOrgTreeInfo: 'org/queryOrgsTreeInfo',
  modifyOrgDeptInfo: 'org/modifyOrgDeptInfo',
  deleteOrgDeptInfo: 'org/deleteOrgDeptInfo',
  getDeptInfo: 'dept/queryDeptsInfo',
  getPersonInfo: 'person/queryPersonInfo',
  modifyPersonInfo: 'person/modifyPersonInfo',
  getPersonInfoAll: 'person/getPersonInfoAll',
  queryDefectCode: '/defectCode/queryDefectCode'
}

export function queryDefectCode (data) {
  return axios({
    url: api.queryDefectCode,
    method: 'post',
    data: data
  })
}

export function getTreeById (data) {
  return axios({
    url: api.getTreeById,
    method: 'post',
    data: {
      ...data,
      sqlParams: {
        orgId: Vue.ls.get(ORG_ID),
        ...data.sqlParams
      }
    }
  })
}

export function getCommboxById (data) {
  return axios({
    url: api.getCommboxById,
    method: 'post',
    data: {
      ...data,
      sqlParams: {
        orgId: Vue.ls.get(ORG_ID),
        ...data.sqlParams
      }
    }
  })
}

export function getOrgInfo (parameter) {
  return axios({
    url: api.getOrgInfo,
    method: 'post',
    data: parameter
  })
}

export function getOrgTreeInfo (parameter) {
  return axios({
    url: api.getOrgTreeInfo,
    method: 'post',
    data: parameter
  })
}

export function modifyOrgDeptInfo (parameter) {
  return axios({
    url: api.modifyOrgDeptInfo,
    method: 'post',
    data: parameter
  })
}

export function deleteOrgDeptInfo (parameter) {
  return axios({
    url: api.deleteOrgDeptInfo,
    method: 'post',
    data: parameter
  })
}

export function getDeptInfo (parameter) {
  return axios({
    url: api.getDeptInfo,
    method: 'post',
    data: parameter
  })
}

export function getPersonInfo (parameter) {
  return axios({
    url: api.getPersonInfo,
    method: 'post',
    data: parameter
  })
}

export function modifyPersonInfo (parameter) {
  return axios({
    url: api.modifyPersonInfo,
    method: 'post',
    data: parameter
  })
}

export function getPersonInfoAll (parameter) {
  return axios({
    url: api.getPersonInfoAll,
    method: 'post',
    data: parameter
  })
}

export default api
