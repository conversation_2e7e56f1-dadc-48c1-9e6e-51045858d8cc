import { axios } from '@/utils/request'

const api = {
  selectrepairCosts: '/repairCosts/queryFloor',
  doExportProcurePlan: '/repairCosts/queryFloo'
}

export function selectrepairCosts (data) {
  return axios({
    url: api.selectrepairCosts,
    method: 'post',
    data: data
  })
}

export function doExportProcurePlan (data) {
  return axios({
    url: api.doExportProcurePlan,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
