import { axios } from '@/utils/request'

/**
 *  queryProject: 特种作业查询
 *  modifyProject: 特种作业增删改
 */

const api = {
  querySpecialOperation: '/specialOperation/querySpecialOperation',
  modifySpecialOperation: '/specialOperation/modifySpecialOperation',
  uploadSpecialOperation: '/specialOperation/uploadSpecialOperation',
  exportTemplateSpecialOperation: '/file/modelDownload?fileName=特种作业导入模板'
}

export function exportTemplateSpecialOperation (data) {
  return axios({
    url: api.exportTemplateSpecialOperation,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export function uploadSpecialOperation (data) {
  return axios({
    url: api.uploadSpecialOperation,
    responseType: 'json',
    method: 'post',
    data: data
  })
}

export function querySpecialOperation (data) {
  return axios({
    url: api.querySpecialOperation,
    method: 'post',
    data: data
  })
}

export function modifySpecialOperation (data) {
  return axios({
    url: api.modifySpecialOperation,
    method: 'post',
    data: data
  })
}
export default api
