// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 *  queryAssetOtherObjectArea: 获取点检区域
 *  modifyAssetOtherObjectArea: 修改点检区域
 *  deleteAssetOtherObjectArea: 删除点检区域
 *
 *  queryAssetOtherObjectEquipment: 获取点检设备
 *  modifyAssetOtherObjectEquipment: 修改点检设备
 *  deleteAssetOtherObjectEquipment: 删除点检设备
 *
 *  queryAssetOtherObjectPart: 获取点检部件
 *  modifyAssetOtherObjectPart: 修改点检部件
 *  deleteAssetOtherObjectPart: 删除点检部件
 *
 *  queryAssetOtherObjectItem: 获取点检项目
 *  modifyAssetOtherObjectItem: 修改点检项目
 *  deleteAssetOtherObjectItem: 删除点检项目
 */
const api = {
  queryEquipment: '/assetOtherObject/queryEquipment',
  queryAssetXxhPart: '/assetOtherObject/queryParts',
  queryAssetOtherObjectArea: '/assetOtherObject/queryAssetOtherObjectArea',
  modifyAssetOtherObjectArea: '/assetOtherObject/modifyAssetOtherObjectArea',
  deleteAssetOtherObjectArea: '/assetOtherObject/modifyAssetOtherObjectArea',

  queryAssetOtherObjectEquipment: '/assetOtherObject/queryAssetOtherObjectEquipment',
  modifyAssetOtherObjectEquipment: '/assetOtherObject/modifyAssetOtherObjectEquipment',
  deleteAssetOtherObjectEquipment: '/assetOtherObject/modifyAssetOtherObjectEquipment',

  queryAssetOtherObjectPart: '/assetOtherObject/queryAssetOtherObjectPart',
  modifyAssetOtherObjectPart: '/assetOtherObject/modifyAssetOtherObjectPart',
  deleteAssetOtherObjectPart: '/assetOtherObject/modifyAssetOtherObjectPart',

  queryAssetOtherObjectItem: '/assetOtherObject/queryAssetOtherObjectItem',
  modifyAssetOtherObjectItem: '/assetOtherObject/modifyAssetOtherObjectItem',
  deleteAssetOtherObjectItem: '/assetOtherObject/modifyAssetOtherObjectItem'

  // queryAssetOtherObjectModule: '/assetOtherObjectModule/queryAssetOtherObjectModule', // 查询
  // modifyAssetOtherObjectModule: '/assetOtherObjectModule/modifyAssetOtherObjectModule', // 新增修改
  // modifyAssetOtherObjectModuleDe: '/assetOtherObjectModule/modifyAssetOtherObjectModule' // 删除
}

export function queryAssetXxhPart (data) {
  return axios({
    url: api.queryAssetXxhPart,
    method: 'post',
    data: data
  })
}

export function queryEquipment (data) {
  return axios({
    url: api.queryEquipment,
    method: 'post',
    data: data
  })
}

// export function modifyAssetOtherObjectModuleDe (data) {
//   return axios({
//     url: api.modifyAssetOtherObjectModuleDe,
//     method: 'post',
//     data: data
//   })
// }

// export function modifyAssetOtherObjectModule (data) {
//   return axios({
//     url: api.modifyAssetOtherObjectModule,
//     method: 'post',
//     data: data
//   })
// }

// export function queryAssetOtherObjectModule (data) {
//   return axios({
//     url: api.queryAssetOtherObjectModule,
//     method: 'post',
//     data: data
//   })
// }

export function queryAssetOtherObjectArea (data) {
  return axios({
    url: api.queryAssetOtherObjectArea,
    method: 'post',
    data: data
  })
}

export function modifyAssetOtherObjectArea (data) {
  return axios({
    url: api.modifyAssetOtherObjectArea,
    method: 'post',
    data: data
  })
}

export function deleteAssetOtherObjectArea (data) {
  return axios({
    url: api.deleteAssetOtherObjectArea,
    method: 'post',
    data: data
  })
}

export function queryAssetOtherObjectEquipment (data) {
  return axios({
    url: api.queryAssetOtherObjectEquipment,
    method: 'post',
    data: data
  })
}

export function modifyAssetOtherObjectEquipment (data) {
  return axios({
    url: api.modifyAssetOtherObjectEquipment,
    method: 'post',
    data: data
  })
}

export function deleteAssetOtherObjectEquipment (data) {
  return axios({
    url: api.deleteAssetOtherObjectEquipment,
    method: 'post',
    data: data
  })
}

export function queryAssetOtherObjectPart (data) {
  return axios({
    url: api.queryAssetOtherObjectPart,
    method: 'post',
    data: data
  })
}

export function modifyAssetOtherObjectPart (data) {
  return axios({
    url: api.modifyAssetOtherObjectPart,
    method: 'post',
    data: data
  })
}

export function deleteAssetOtherObjectPart (data) {
  return axios({
    url: api.deleteAssetOtherObjectPart,
    method: 'post',
    data: data
  })
}

export function queryAssetOtherObjectItem (data) {
  return axios({
    url: api.queryAssetOtherObjectItem,
    method: 'post',
    data: data
  })
}

export function modifyAssetOtherObjectItem (data) {
  return axios({
    url: api.modifyAssetOtherObjectItem,
    method: 'post',
    data: data
  })
}

export function deleteAssetOtherObjectItem (data) {
  return axios({
    url: api.deleteAssetOtherObjectItem,
    method: 'post',
    data: data
  })
}

export default api
