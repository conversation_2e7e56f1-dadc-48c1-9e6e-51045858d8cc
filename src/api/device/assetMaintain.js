// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'

import { axios, qs } from '@/utils/request'

/**
 *  queryMaintainArea: 获取点检区域
 *  modifyMaintainArea: 修改点检区域
 *  deleteMaintainArea: 删除点检区域
 *
 *  queryMaintainEquipment: 获取点检设备
 *  modifyMaintainEquipment: 修改点检设备
 *  deleteMaintainEquipment: 删除点检设备
 *
 *  querymaintainPart: 获取点检部件
 *  modifyMaintainPart: 修改点检部件
 *  deleteMaintainPart: 删除点检部件
 *
 *  queryMaintainItem: 获取点检项目
 *  modifyMaintainItem: 修改点检项目
 *  deleteMaintainItem: 删除点检项目
 *
 *  uploadTemplate: 模板上传
 */
const api = {
  queryMaintainArea: '/maintain/queryMaintainArea',
  modifyMaintainArea: '/Maintain/modifyMaintainArea',
  deleteMaintainArea: '/maintain/modifyMaintainArea',

  queryMaintainEquipment: '/maintain/queryMaintainEquipment',
  modifyMaintainEquipment: '/maintain/modifyMaintainEquipment',
  deleteMaintainEquipment: '/maintain/modifyMaintainEquipment',

  queryMaintainPart: '/maintain/queryMaintainPart',
  modifyMaintainPart: '/maintain/modifyMaintainPart',
  deleteMaintainPart: '/maintain/modifyMaintainPart',

  queryMaintainItem: '/maintain/queryMaintainItem',
  modifyMaintainItem: '/maintain/modifyMaintainItem',
  deleteMaintainItem: '/maintain/modifyMaintainItem',

  queryMaintainModule: '/MaintainObjectModule/queryMaintainModule', // 查询
  modifyMaintainModule: '/MaintainObjectModule/modifyMaintainModule', // 新增修改
  modifyMaintainModuleDe: '/MaintainObjectModule/modifyMaintainModule', // 删除
  modifyRepairTrace: '/regularRepair/modifyRepairTrace',

  uploadTemplate: '/maintain/uploadTemplate',
  queryMaintainDetail: '/maintain/queryMaintainDetail',
  modifyMaintainDetail: '/maintain/modifyMaintainDetail',
  queryMaintainTemplate: '/maintain/queryMaintainTemplate',
  modifyMaintainTemplate: '/maintain/modifyMaintainTemplate',
  queryMaintainTechnician: '/maintain/queryMaintainTechnician',
  queryMaintainDetailByItem: '/maintain/queryMaintainDetailByItem',
  modifyMaintainDetailByItem: '/maintain/modifyMaintainDetailByItem',
  approveMaintainFeedback: '/maintain/approveMaintainFeedback'
}
export function approveMaintainFeedback (data) {
  return axios({
    url: api.approveMaintainFeedback,
    method: 'post',
    data: data
  })
}

export function modifyMaintainDetailByItem (data) {
  return axios({
    url: api.modifyMaintainDetailByItem,
    method: 'post',
    data: data
  })
}

export function queryMaintainDetailByItem (data) {
  return axios({
    url: api.queryMaintainDetailByItem,
    method: 'post',
    data: data
  })
}

export function queryMaintainTechnician (data) {
  return axios({
    url: api.queryMaintainTechnician,
    method: 'post',
    data: data
  })
}
export function modifyMaintainTemplate (data) {
  return axios({
    url: api.modifyMaintainTemplate,
    method: 'post',
    data: data
  })
}

export function queryMaintainTemplate (data) {
  return axios({
    url: api.queryMaintainTemplate,
    method: 'post',
    data: data
  })
}

export function modifyMaintainDetail (data) {
  return axios({
    url: api.modifyMaintainDetail,
    method: 'post',
    data: data
  })
}

export function queryMaintainDetail (data) {
  return axios({
    url: api.queryMaintainDetail,
    method: 'post',
    data: data
  })
}

export function uploadTemplate (parameter) {
  return axios({
    url: api.uploadTemplate,
    method: 'post',
    data: parameter,
    paramsSerializer: function (parameter) {
      return qs.stringify(parameter, { indices: false })
    }
  })
}

export function modifyRepairTrace (data) {
  return axios({
    url: api.modifyRepairTrace,
    method: 'post',
    data: data
  })
}

export function modifyMaintainModuleDe (data) {
  return axios({
    url: api.modifyMaintainModuleDe,
    method: 'post',
    data: data
  })
}

export function modifyMaintainModule (data) {
  return axios({
    url: api.modifyMaintainModule,
    method: 'post',
    data: data
  })
}

export function queryMaintainModule (data) {
  return axios({
    url: api.queryMaintainModule,
    method: 'post',
    data: data
  })
}

export function queryMaintainArea (data) {
  return axios({
    url: api.queryMaintainArea,
    method: 'post',
    data: data
  })
}

export function modifyMaintainArea (data) {
  return axios({
    url: api.modifyMaintainArea,
    method: 'post',
    data: data
  })
}

export function deleteMaintainArea (data) {
  return axios({
    url: api.deleteMaintainArea,
    method: 'post',
    data: data
  })
}

export function queryMaintainEquipment (data) {
  return axios({
    url: api.queryMaintainEquipment,
    method: 'post',
    data: data
  })
}

export function modifyMaintainEquipment (data) {
  return axios({
    url: api.modifyMaintainEquipment,
    method: 'post',
    data: data
  })
}

export function deleteMaintainEquipment (data) {
  return axios({
    url: api.deleteMaintainEquipment,
    method: 'post',
    data: data
  })
}

export function queryMaintainPart (data) {
  return axios({
    url: api.queryMaintainPart,
    method: 'post',
    data: data
  })
}

export function modifyMaintainPart (data) {
  return axios({
    url: api.modifyMaintainPart,
    method: 'post',
    data: data
  })
}

export function deleteMaintainPart (data) {
  return axios({
    url: api.deleteMaintainPart,
    method: 'post',
    data: data
  })
}

export function queryMaintainItem (data) {
  return axios({
    url: api.queryMaintainItem,
    method: 'post',
    data: data
  })
}

export function modifyMaintainItem (data) {
  return axios({
    url: api.modifyMaintainItem,
    method: 'post',
    data: data
  })
}

export function deleteMaintainItem (data) {
  return axios({
    url: api.deleteMaintainItem,
    method: 'post',
    data: data
  })
}

export default api
