// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 *  queryResume: 获取履历信息
 *  modifyResume: 修改履历信息
 *  deleteResume: 删除履历信息
 */
const api = {
  queryResume: '/resume/queryResume',
  modifyResume: '/resume/modifyResume',
  deleteResume: '/resume/modifyResume'
}

export function queryResume (data) {
  return axios({
    url: api.queryResume,
    method: 'post',
    data: data
  })
}

export function modifyResume (data) {
  return axios({
    url: api.modifyResume,
    method: 'post',
    data: data
  })
}

export function deleteResume (data) {
  return axios({
    url: api.deleteResume,
    method: 'post',
    data: data
  })
}

export default api
