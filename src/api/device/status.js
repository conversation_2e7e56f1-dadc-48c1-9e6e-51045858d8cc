import { axios } from '@/utils/request'

/**
 *  queryAssetStatus: 设备状态查询
 *  modifyAssetStatus: 设备状态新增
 *  deleteAssetStatus: 设备状态删除
 *  approveStatus：设备状态审批
 */
const api = {
  queryAssetStatus: '/assetStatus/queryAssetStatusInfo',
  modifyAssetStatus: '/assetStatus/modifyAssetStatus',
  deleteAssetStatus: '/assetStatus/modifyAssetStatus',
  approveStatus: '/assetStatus/appro'
}

export function queryAssetStatus (data) {
  return axios({
    url: api.queryAssetStatus,
    method: 'post',
    data: data
  })
}

export function modifyAssetStatus (data) {
  return axios({
    url: api.modifyAssetStatus,
    method: 'post',
    data: data
  })
}

export function deleteAssetStatus (data) {
  return axios({
    url: api.deleteAssetStatus,
    method: 'post',
    data: data
  })
}

export function approveStatus (data) {
  return axios({
    url: api.approveStatus,
    method: 'post',
    data: data
  })
}

export default api
