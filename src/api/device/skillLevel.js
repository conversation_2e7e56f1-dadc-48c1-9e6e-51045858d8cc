import { axios } from '@/utils/request'

/**
 *  queryProject: 特种作业查询
 *  modifyProject: 特种作业增删改
 */

const api = {
  querySkillLevel: '/skillLevel/querySkillLevel',
  modifySkillLevel: '/skillLevel/modifySkillLevel',
  uploadSkillLevel: '/skillLevel/uploadSkillLevel',
  exportTemplateSkillLevel: '/file/modelDownload?fileName=专业技术技能等级人员导入模板',
  exportSkillLevel: '/skillLevel/doExport'

}

export function exportSkillLevel (data) {
  return axios({
    url: api.exportSkillLevel,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}

export function exportTemplateSkillLevel (data) {
  return axios({
    url: api.exportTemplateSkillLevel,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export function uploadSkillLevel (data) {
  return axios({
    url: api.uploadSkillLevel,
    responseType: 'json',
    method: 'post',
    data: data
  })
}

export function querySkillLevel (data) {
  return axios({
    url: api.querySkillLevel,
    method: 'post',
    data: data
  })
}

export function modifySkillLevel (data) {
  return axios({
    url: api.modifySkillLevel,
    method: 'post',
    data: data
  })
}
export default api
