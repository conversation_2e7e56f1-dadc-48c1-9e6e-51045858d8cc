// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
import { axios, qs } from '@/utils/request'

/**
 *  queryWorkorderDetail: 查询工单详情
 *
 *  queryWorkorder: 获取工单信息
 *  modifyWorkorder: 修改工单信息
 *  deleteWorkorder: 删除工单信息
 *  exportWorkorder:导出选中的工单
 *
 *  queryMaterial: 获取物资信息
 *  modifyMaterial: 修改物资信息
 *  deleteMaterial: 删除物资信息
 *
 *  queryPerson: 获取维修人员
 *  modifyPerson: 修改维修人员
 *  deletePerson: 删除维修人员
 *
 *  approveWorkorder: 审批工单
 *  finalConfir:确认工单
 *  deConfirmation:反确认工单
 *  queryMaterialToDo: 查询待处理物资
 *  doMaterialOutOfStock:出库
 *  generatePlan:生成计划
 * *  getAssetInfo:查询设备信息
 */
const api = {
  queryWorkorderDetail: '/workorder/queryWorkorderDetail',

  // 工单信息
  queryWorkorder: '/workorder/queryWorkorder',
  modifyWorkorder: '/workorder/modifyWorkorder',
  deleteWorkorder: '/workorder/modifyWorkorder',
  exportWorkorder: '/workorder/exportWorkorder',
  doExportDetailed: '/workorder/doExportDetailed',
  exportOutsource: '/workorder/exportOutsource',

  // 物资信息
  queryMaterial: '/workorder/listWorkorderMaterial',
  modifyMaterial: '/workorder/moidfyWorkorderMaterial',
  deleteMaterial: '/workorder/moidfyWorkorderMaterial',

  // 维修人员
  queryPerson: '/workorder/listWorkorderPerson',
  modifyPerson: '/workorder/moidfyWorkorderPerson',
  deletePerson: '/workorder/moidfyWorkorderPerson',

  // 审批相关
  approveWorkorder: '/workorder/approWorkorder',

  // 工单物资相关
  finalConfir: '/workorder/finalConfir',
  deConfirmation: '/workorder/deConfirmation',
  doMaterialOutOfStock: '/workorder/doMaterialOutOfStock',
  queryMaterialToDo: '/workorder/queryMaterialToDo',
  generatePlan: '/workorder/generatePlan',
  // 工单设备相关 一对多
  getAssetInfo: '/workorder/getAssetInfo',
  modifyAssetInfo: '/workorder/modifyAssetInfo'
}

export function queryWorkorderDetail (data) {
  return axios({
    url: api.queryWorkorderDetail,
    method: 'post',
    data: data
  })
}

export function queryWorkorder (data) {
  return axios({
    url: api.queryWorkorder,
    method: 'post',
    data: data
  })
}

export function modifyWorkorder (data) {
  return axios({
    url: api.modifyWorkorder,
    method: 'post',
    data: data
  })
}

export function deleteWorkorder (data) {
  return axios({
    url: api.deleteWorkorder,
    method: 'post',
    data: data
  })
}

export function exportWorkorder (data) {
  return axios({
    url: api.exportWorkorder,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doExportDetailed (data) {
  return axios({
    url: api.doExportDetailed,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function exportOutsource (data) {
  return axios({
    url: api.exportOutsource,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryMaterial (data) {
  return axios({
    url: api.queryMaterial,
    method: 'post',
    data: data
  })
}

export function modifyMaterial (data) {
  return axios({
    url: api.modifyMaterial,
    method: 'post',
    data: data
  })
}

export function deleteMaterial (data) {
  return axios({
    url: api.deleteMaterial,
    method: 'post',
    data: data
  })
}

export function queryPerson (data) {
  return axios({
    url: api.queryPerson,
    method: 'post',
    data: data
  })
}

export function modifyPerson (data) {
  return axios({
    url: api.modifyPerson,
    method: 'post',
    data: data
  })
}

export function deletePerson (data) {
  return axios({
    url: api.deletePerson,
    method: 'post',
    data: data
  })
}

export function approveWorkorder (data) {
  return axios({
    url: api.approveWorkorder,
    method: 'post',
    data: data
  })
}

export function finalConfir (data) {
  return axios({
    url: api.finalConfir,
    method: 'post',
    data: data
  })
}

export function deConfirmation (parameter) {
  return axios({
    url: api.deConfirmation,
    method: 'post',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function doMaterialOutOfStock (data) {
  return axios({
    url: api.doMaterialOutOfStock,
    method: 'post',
    data: data
  })
}

export function queryMaterialToDo (data) {
  return axios({
    url: api.queryMaterialToDo,
    method: 'post',
    data: data
  })
}

export function generatePlan (data) {
  return axios({
    url: api.generatePlan,
    method: 'post',
    data: data
  })
}

export function getAssetInfo (parameter) {
  return axios({
    url: api.getAssetInfo,
    method: 'get',
    params: parameter,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function modifyAssetInfo (data) {
  return axios({
    url: api.modifyAssetInfo,
    method: 'post',
    data: data
  })
}
export default api
