// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'

import { axios } from '@/utils/request'

/**
 *  queryAssetSbTemplate: 获取点检模板
 *  modifyAssetSbTemplate: 修改点检模板
 *  queryAssetSbPart: 查询部件库
 *  queryAssetSbItem: 查询构件库
 *  modifyAssetSbItem: 修改构件库
 *  modifyAssetSbPart：修改部件库
 *  queryAssetSbTemplatePart: 获取模板关联部件
 *  queryAssetSbTemplatePart: 获取模板关联部件
 *  modifyAssetSbTemplatePart: 修改模板关联部件
 *  modifyAssetSbTemplateItem: 修改模板关联构件
 */
const api = {
  queryAssetSbTemplate: '/assetSbTemplate/queryAssetSbTemplate',
  modifyAssetSbTemplate: '/assetSbTemplate/modifyAssetSbTemplate',
  queryAssetSbPart: '/assetSb/queryAssetSbPart',
  queryAssetSbItem: '/assetSb/queryAssetSbItem',
  modifyAssetSbPart: '/assetSb/modifyAssetSbPart',
  modifyAssetSbItem: '/assetSb/modifyAssetSbItem',
  queryAssetSbTemplatePart: '/assetSbTemplate/queryAssetSbTemplatePart',
  queryAssetSbTemplateItem: '/assetSbTemplate/queryAssetSbTemplateItem',
  modifyAssetSbTemplatePart: '/assetSbTemplate/modifyAssetSbTemplatePart',
  modifyAssetSbTemplateItem: '/assetSbTemplate/modifyAssetSbTemplateItem',
  modifyAssetCheckTemplate: '/assetCheckTemplate/modifyAssetCheckTemplate',
  queryAssetCheckDetail: '/assetCheckObject/queryAssetCheckDetail',
  modifyAssetCheckDetail: '/assetCheckObject/modifyAssetCheckDetail',
  modifyAssetCheckItem: '/assetCheckObject/modifyAssetCheckItem'

}

export function modifyAssetCheckItem (data) {
  return axios({
    url: api.modifyAssetCheckItem,
    method: 'post',
    data: data
  })
}

export function modifyAssetCheckDetail (data) {
  return axios({
    url: api.modifyAssetCheckDetail,
    method: 'post',
    data: data
  })
}

export function queryAssetCheckDetail (data) {
  return axios({
    url: api.queryAssetCheckDetail,
    method: 'post',
    data: data
  })
}

export function modifyAssetCheckTemplate (data) {
  return axios({
    url: api.modifyAssetCheckTemplate,
    method: 'post',
    data: data
  })
}

export function modifyAssetSbTemplatePart (data) {
  return axios({
    url: api.modifyAssetSbTemplatePart,
    method: 'post',
    data: data
  })
}

export function modifyAssetSbTemplateItem (data) {
  return axios({
    url: api.modifyAssetSbTemplateItem,
    method: 'post',
    data: data
  })
}

export function queryAssetSbTemplateItem (data) {
  return axios({
    url: api.queryAssetSbTemplateItem,
    method: 'post',
    data: data
  })
}

export function queryAssetSbTemplatePart (data) {
  return axios({
    url: api.queryAssetSbTemplatePart,
    method: 'post',
    data: data
  })
}

export function modifyAssetSbPart (data) {
  return axios({
    url: api.modifyAssetSbPart,
    method: 'post',
    data: data
  })
}

export function modifyAssetSbItem (data) {
  return axios({
    url: api.modifyAssetSbItem,
    method: 'post',
    data: data
  })
}

export function queryAssetSbItem (data) {
  return axios({
    url: api.queryAssetSbItem,
    method: 'post',
    data: data
  })
}

export function queryAssetSbPart (data) {
  return axios({
    url: api.queryAssetSbPart,
    method: 'post',
    data: data
  })
}

export function queryAssetSbTemplate (data) {
  return axios({
    url: api.queryAssetSbTemplate,
    method: 'post',
    data: data
  })
}

export function modifyAssetSbTemplate (data) {
  return axios({
    url: api.modifyAssetSbTemplate,
    method: 'post',
    data: data
  })
}

export default api
