import { axios } from '@/utils/request'

/**
 *  queryRegularRepairInfo: 点检查询
 *  insertRegularRepairInfo: 点检新增
 *  updateRegularRepairInfo: 点检修改
 *  deleteRegularRepairInfo: 点检删除
 *  queryRegularRepairDailyInfo: 点检日表查询
 *  insertRegularRepairDailyInfo: 点检日表新增
 *  updateRegularRepairDailyInfo: 点检日表修改
 *  deleteRegularRepairDailyInfo: 点检日表删除
 *  queryRegularRepairWeekInfo: 点检周表查询
 *  insertRegularRepairWeekInfo: 点检周表新增
 *  updateRegularRepairWeekInfo: 点检周表修改
 *  deleteRegularRepairWeekInfo: 点检周表删除
 *  queryRegularRepairMonthInfo: 点检月表查询
 *  insertRegularRepairMonthInfo: 点检月表新增
 *  updateRegularRepairMonthInfo: 点检月表修改
 *  updateRegularRepairMonthCompletionInfo: 点检月表完成状态修改
 *  deleteRegularRepairMonthInfo: 点检月表删除
 *  carryOver: 结转计划
 *  queryRepairChangeRecord: 查询转换情况
 *  queryRepairMonthDailyRecord: 查询月计划已列日计划内容
 *  getNewPlanMonth:获取当前最大月
 *  uploadRegularRepairYear: 年计划导入
 *  queryRepairYearMonthRecord
 *  queryRelateRepairYearInfo
 *  returnApproveMonth: 月计划撤回(发布 => 编制)
 *  changeMonthReturn: 结转退回(编制 => 发布状态(计划月和编码恢复))
 *  exporWeekRecord: 周计划导出
 *  doApproFinishRepairMonth: 月计划完成
 *  queryRegularRepairPlanStatisticsDetail: 驾驶舱计划统计细目
 *  queryRepairYearAmount: 查询年计划维修费用管理
 *  returnApproveYear: 年计划结转(发布 => 编制)
 *  queryRepairYearAmountStatistic: 获取年计划项目类别已使用的金额
 *  judgeRegularRepairYearQuota： 维修年计划查询是否超额
 *  getRepairYearMaxAmountYear: 维修年计划最大月
 *  queryRegularRepairFinishRate: 查询日、周、月总计划数
 */

const api = {
  queryRegularRepairInfo: '/regularRepair/queryRegularRepairInfo',
  insertRegularRepairInfo: '/regularRepair/modifyRegularRepair',
  updateRegularRepairInfo: '/regularRepair/modifyRegularRepair',
  deleteRegularRepairInfo: '/regularRepair/modifyRegularRepair',
  queryRegularRepairDailyInfo: '/regularRepairDaily/queryRegularRepairDailyInfo',
  insertRegularRepairDailyInfo: '/regularRepairDaily/modifyRegularRepairDaily',
  updateRegularRepairDailyInfo: '/regularRepairDaily/modifyRegularRepairDaily',
  deleteRegularRepairDailyInfo: '/regularRepairDaily/modifyRegularRepairDaily',
  queryRegularRepairWeekInfo: '/regularRepairWeek/queryRegularRepairWeekInfo',
  insertRegularRepairWeekInfo: '/regularRepairWeek/modifyRegularRepairWeek',
  updateRegularRepairWeekInfo: '/regularRepairWeek/modifyRegularRepairWeek',
  deleteRegularRepairWeekInfo: '/regularRepairWeek/modifyRegularRepairWeek',
  terminateRegularRepairWeekInfo: '/regularRepairWeek/modifyRegularRepairWeek',
  queryRegularRepairMonthInfo: '/regularRepairMonth/queryRegularRepairMonthInfo',
  insertRegularRepairMonthInfo: '/regularRepairMonth/modifyRegularRepairMonth',
  updateRegularRepairMonthInfo: '/regularRepairMonth/modifyRegularRepairMonth',
  deleteRegularRepairMonthInfo: '/regularRepairMonth/modifyRegularRepairMonth',
  terminateRegularRepairMonthInfo: '/regularRepairMonth/modifyRegularRepairMonth',
  uploadRegularRepairMonth: 'regularRepairMonth/uploadRegularRepairMonth',
  doRepairExport: '/regularRepair/doRepairExport',
  doNightShiftExport: '/regularRepair/doNightShiftExport',
  approveRepairWeek: '/regularRepairWeek/approveRepairWeek',
  approveRepairDaily: '/regularRepairDaily/approveRepairDaily',
  approveRepairMonth: '/regularRepairMonth/approveRepairMonth',
  weekCarryOver: '/regularRepairWeek/carryOver',
  trans2DailyPlan: '/regularRepairWeek/trans2DailyPlan',
  trans2DailyPlanMonth: '/regularRepairMonth/trans2DailyPlan',
  queryRepairChangeRecord: '/regularRepair/queryRepairChangeRecord',
  queryRepairHandOverRecord: '/regularRepair/queryRepairHandOverRecord',
  dailyCarryOver: '/regularRepairDaily/carryOver',
  MonthCarryOver: '/regularRepairMonth/carryOver',
  yearCarryOver: '/regularRepairYear/carryOver',
  queryRepairMonthDailyRecord: '/regularRepairMonth/queryRepairMonthDailyRecord',
  queryRepairWeekDailyRecord: '/regularRepairWeek/queryRepairWeekDailyRecord',
  queryRepairDailyRelate: '/regularRepairDaily/queryRepairDailyRelate',
  repairWeekHandOver: '/regularRepairWeek/repairWeekHandOver',
  repairMonthHandOver: '/regularRepairMonth/repairMonthHandOver',
  getNewPlanMonth: '/regularRepairMonth/getNewPlanMonth',
  queryRegularRepairYearInfo: '/regularRepairYear/queryRegularRepairYearInfo',
  modifyRegularRepairYearInfo: '/regularRepairYear/modifyRegularRepairYearInfo',
  uploadRegularRepairYear: 'regularRepairYear/uploadRegularRepairYear',
  exportTemplateRepairYear: '/file/modelDownload?fileName=年计划导入模板',
  queryRepairYearMonthRecord: '/regularRepairYear/queryRepairYearMonthRecord',
  queryRelateRepairYearInfo: '/regularRepairYear/queryRelateRepairYearInfo',
  exportMonthRecord: '/regularRepairMonth/exportMonthRecord',
  returnApproveMonth: '/regularRepairMonth/returnApproveMonth',
  returnApproveWeek: '/regularRepairWeek/returnApproveWeek',
  changeMonthReturn: '/regularRepairMonth/changeMonthReturn',
  exportWeekRecord: '/regularRepairWeek/exportWeekRecord',
  queryRegularRepairPlanStatisticsTotal: '/regularRepair/queryRegularRepairPlanStatisticsTotal',
  doApproFinishRepairMonth: '/regularRepairMonth/doApproFinishRepairMonth',
  queryRegularRepairPlanStatisticsDetail: '/regularRepair/queryRegularRepairPlanStatisticsDetail',
  exportYearRecord: '/regularRepairYear/exportYearRecord',
  queryRepairYearAmount: '/regularRepairYear/queryRepairYearAmount',
  modifyRepairYearAmount: '/regularRepairYear/modifyRepairYearAmount',
  approveRepairYear: '/regularRepairYear/approveRepairYear',
  returnApproveYear: '/regularRepairYear/returnApproveYear',
  queryRepairYearAmountStatistic: '/regularRepairYear/queryRepairYearAmountStatistic',
  queryRepairYearOtherAmount: '/regularRepairYear/queryRepairYearOtherAmount',
  oneClickPublishRepairYear: '/regularRepairYear/oneClickPublishRepairYear',
  oneClickPublishRepairMonth: '/regularRepairMonth/oneClickPublishRepairMonth',
  judgeRegularRepairYearQuota: '/regularRepairYear/judgeRegularRepairYearQuota',
  getRepairYearMaxAmountYear: '/regularRepairYear/getRepairYearMaxAmountYear',
  queryRegularRepairFinishRate: '/regularRepair/queryRegularRepairFinishRate'
}

export function queryRegularRepairFinishRate (data) {
  return axios({
    url: api.queryRegularRepairFinishRate,
    method: 'post',
    data: data
  })
}

export function queryRepairYearOtherAmount (data) {
  return axios({
    url: api.queryRepairYearOtherAmount,
    method: 'post',
    data: data
  })
}
export function getRepairYearMaxAmountYear (data) {
  return axios({
    url: api.getRepairYearMaxAmountYear,
    method: 'post',
    data: data
  })
}

export function judgeRegularRepairYearQuota (data) {
  return axios({
    url: api.judgeRegularRepairYearQuota,
    method: 'post',
    data: data
  })
}
export function oneClickPublishRepairMonth (data) {
  return axios({
    url: api.oneClickPublishRepairMonth,
    method: 'post',
    data: data
  })
}

export function oneClickPublishRepairYear (data) {
  return axios({
    url: api.oneClickPublishRepairYear,
    method: 'post',
    data: data
  })
}

export function queryRepairYearAmountStatistic (data) {
  return axios({
    url: api.queryRepairYearAmountStatistic,
    method: 'post',
    data: data
  })
}

export function returnApproveYear (data) {
  return axios({
    url: api.returnApproveYear,
    method: 'post',
    data: data
  })
}

export function approveRepairYear (data) {
  return axios({
    url: api.approveRepairYear,
    method: 'post',
    data: data
  })
}

export function modifyRepairYearAmount (data) {
  return axios({
    url: api.modifyRepairYearAmount,
    method: 'post',
    data: data
  })
}
export function queryRepairYearAmount (data) {
  return axios({
    url: api.queryRepairYearAmount,
    method: 'post',
    data: data
  })
}

export function exportYearRecord (data) {
  return axios({
    url: api.exportYearRecord,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function yearCarryOver (data) {
  return axios({
    url: api.yearCarryOver,
    method: 'post',
    data: data
  })
}
export function queryRegularRepairPlanStatisticsDetail (data) {
  return axios({
    url: api.queryRegularRepairPlanStatisticsDetail,
    method: 'post',
    data: data
  })
}
export function doApproFinishRepairMonth (data) {
  return axios({
    url: api.doApproFinishRepairMonth,
    method: 'post',
    data: data
  })
}

export function queryRegularRepairPlanStatisticsTotal (data) {
  return axios({
    url: api.queryRegularRepairPlanStatisticsTotal,
    method: 'post',
    data: data
  })
}

export function changeMonthReturn (data) {
  return axios({
    url: api.changeMonthReturn,
    method: 'post',
    data: data
  })
}
export function returnApproveMonth (data) {
  return axios({
    url: api.returnApproveMonth,
    method: 'post',
    data: data
  })
}

export function returnApproveWeek (data) {
  return axios({
    url: api.returnApproveWeek,
    method: 'post',
    data: data
  })
}

export function exportWeekRecord (data) {
  return axios({
    url: api.exportWeekRecord,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function exportMonthRecord (data) {
  return axios({
    url: api.exportMonthRecord,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryRelateRepairYearInfo (data) {
  return axios({
    url: api.queryRelateRepairYearInfo,
    method: 'post',
    data: data
  })
}

export function queryRepairYearMonthRecord (data) {
  return axios({
    url: api.queryRepairYearMonthRecord,
    method: 'post',
    data: data
  })
}

export function exportTemplateRepairYear (data) {
  return axios({
    url: api.exportTemplateRepairYear,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}
export function uploadRegularRepairYear (data) {
  return axios({
    url: api.uploadRegularRepairYear,
    responseType: 'json',
    method: 'post',
    data: data
  })
}

export function uploadRegularRepairMonth (data) {
  return axios({
    url: api.uploadRegularRepairMonth,
    responseType: 'json',
    method: 'post',
    data: data
  })
}

export function queryRegularRepairYearInfo (data) {
  return axios({
    url: api.queryRegularRepairYearInfo,
    method: 'post',
    data: data
  })
}
export function modifyRegularRepairYearInfo (data) {
  return axios({
    url: api.modifyRegularRepairYearInfo,
    method: 'post',
    data: data
  })
}
export function updateRegularRepairYearInfo (data) {
  return axios({
    url: api.updateRegularRepairYearInfo,
    method: 'post',
    data: data
  })
}

export function approveRepairDaily (data) {
  return axios({
    url: api.approveRepairDaily,
    method: 'post',
    data: data
  })
}

export function getNewPlanMonth (data) {
  return axios({
    url: api.getNewPlanMonth,
    method: 'post',
    data: data
  })
}

export function repairWeekHandOver (data) {
  return axios({
    url: api.repairWeekHandOver,
    method: 'post',
    data: data
  })
}

export function repairMonthHandOver (data) {
  return axios({
    url: api.repairMonthHandOver,
    method: 'post',
    data: data
  })
}

export function queryRepairDailyRelate (data) {
  return axios({
    url: api.queryRepairDailyRelate,
    method: 'post',
    data: data
  })
}

export function queryRepairMonthDailyRecord (data) {
  return axios({
    url: api.queryRepairMonthDailyRecord,
    method: 'post',
    data: data
  })
}

export function queryRepairWeekDailyRecord (data) {
  return axios({
    url: api.queryRepairWeekDailyRecord,
    method: 'post',
    data: data
  })
}

export function trans2DailyPlanMonth (data) {
  return axios({
    url: api.trans2DailyPlanMonth,
    method: 'post',
    data: data
  })
}

export function MonthCarryOver (data) {
  return axios({
    url: api.MonthCarryOver,
    method: 'post',
    data: data
  })
}

export function approveRepairMonth (data) {
  return axios({
    url: api.approveRepairMonth,
    method: 'post',
    data: data
  })
}

export function dailyCarryOver (data) {
  return axios({
    url: api.dailyCarryOver,
    method: 'post',
    data: data
  })
}

export function trans2DailyPlan (data) {
  return axios({
    url: api.trans2DailyPlan,
    method: 'post',
    data: data
  })
}

export function queryRepairChangeRecord (data) {
  return axios({
    url: api.queryRepairChangeRecord,
    method: 'post',
    data: data
  })
}
export function queryRepairHandOverRecord (data) {
  return axios({
    url: api.queryRepairHandOverRecord,
    method: 'post',
    data: data
  })
}

export function weekCarryOver (data) {
  return axios({
    url: api.weekCarryOver,
    method: 'post',
    data: data
  })
}
export function approveRepairWeek (data) {
  return axios({
    url: api.approveRepairWeek,
    method: 'post',
    data: data
  })
}

export function doNightShiftExport (data) {
  return axios({
    url: api.doNightShiftExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function doRepairExport (data) {
  return axios({
    url: api.doRepairExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryRegularRepairInfo (data) {
  return axios({
    url: api.queryRegularRepairInfo,
    method: 'post',
    data: data
  })
}

export function insertRegularRepairInfo (data) {
  return axios({
    url: api.insertRegularRepairInfo,
    method: 'post',
    data: data
  })
}

export function updateRegularRepairInfo (data) {
  return axios({
    url: api.updateRegularRepairInfo,
    method: 'post',
    data: data
  })
}

export function deleteRegularRepairInfo (data) {
  return axios({
    url: api.deleteRegularRepairInfo,
    method: 'post',
    data: data
  })
}

export function queryRegularRepairDailyInfo (data) {
  return axios({
    url: api.queryRegularRepairDailyInfo,
    method: 'post',
    data: data
  })
}

export function insertRegularRepairDailyInfo (data) {
  return axios({
    url: api.insertRegularRepairDailyInfo,
    method: 'post',
    data: data
  })
}

export function updateRegularRepairDailyInfo (data) {
  return axios({
    url: api.updateRegularRepairDailyInfo,
    method: 'post',
    data: data
  })
}

export function deleteRegularRepairDailyInfo (data) {
  return axios({
    url: api.deleteRegularRepairDailyInfo,
    method: 'post',
    data: data
  })
}

export function queryRegularRepairWeekInfo (data) {
  return axios({
    url: api.queryRegularRepairWeekInfo,
    method: 'post',
    data: data
  })
}

export function insertRegularRepairWeekInfo (data) {
  return axios({
    url: api.insertRegularRepairWeekInfo,
    method: 'post',
    data: data
  })
}

export function updateRegularRepairWeekInfo (data) {
  return axios({
    url: api.updateRegularRepairWeekInfo,
    method: 'post',
    data: data
  })
}

export function deleteRegularRepairWeekInfo (data) {
  return axios({
    url: api.deleteRegularRepairWeekInfo,
    method: 'post',
    data: data
  })
}

export function queryRegularRepairMonthInfo (data) {
  return axios({
    url: api.queryRegularRepairMonthInfo,
    method: 'post',
    data: data
  })
}

export function insertRegularRepairMonthInfo (data) {
  return axios({
    url: api.insertRegularRepairMonthInfo,
    method: 'post',
    data: data
  })
}

export function updateRegularRepairMonthInfo (data) {
  return axios({
    url: api.updateRegularRepairMonthInfo,
    method: 'post',
    data: data
  })
}

export function updateRegularRepairMonthCompletionInfo (data) {
  return axios({
    url: api.updateRegularRepairMonthCompletionInfo,
    method: 'post',
    data: data
  })
}

export function deleteRegularRepairMonthInfo (data) {
  return axios({
    url: api.deleteRegularRepairMonthInfo,
    method: 'post',
    data: data
  })
}

export function terminateRegularRepairMonthInfo (data) {
  return axios({
    url: api.terminateRegularRepairMonthInfo,
    method: 'post',
    data: data
  })
}

export function terminateRegularRepairWeekInfo (data) {
  return axios({
    url: api.terminateRegularRepairWeekInfo,
    method: 'post',
    data: data
  })
}

export default api
