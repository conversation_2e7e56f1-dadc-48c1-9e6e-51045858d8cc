import { axios } from '@/utils/request'

const api = {
  modifyKnowledgeBase: '/knowledgeBase/modifyKnowledgeBase',
  queryKnowledgeBase: '/knowledgeBase/queryKnowledgeBase'
}

export function modifyKnowledgeBase (data) {
  return axios({
    url: api.modifyKnowledgeBase,
    method: 'post',
    data: data
  })
}
export function queryKnowledgeBase (data) {
  return axios({
    url: api.queryKnowledgeBase,
    method: 'post',
    data: data
  })
}
