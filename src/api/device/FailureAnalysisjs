import { axios } from '@/utils/request'

const api = {
  selectfailureAnalysis: '/failureAnalysis/queryFloor',
  doExportProcurePlan: '/failureAnalysis/exportPrline'
}

export function selectfailureAnalysis (data) {
  return axios({
    url: api.selectfailureAnalysis,
    method: 'post',
    data: data
  })
}

export function doExportProcurePlan (data) {
  return axios({
    url: api.doExportProcurePlan,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
