import { axios } from '@/utils/request'

/**
 *  queryMachineMain: 查询装卸机械运行报表
 */
const api = {
  queryMachineCostTypeOne: '/machineCost/queryMachineCostTypeOne',
  modifyMachineCostTypeOne: '/machineCost/modifyMachineCostTypeOne',
  queryMachineCostTypeTwo: '/machineCost/queryMachineCostTypeTwo',
  modifyMachineCostTypeTwo: '/machineCost/modifyMachineCostTypeTwo',
  modifyMachineCostAssetSb: '/machineCost/modifyMachineCostAssetSb',
  queryMachineCostAssetSb: '/machineCost/queryMachineCostAssetSb',
  modifyMachineCostAssetSbPart: '/machineCost/modifyMachineCostAssetSbPart',
  queryMachineCostAssetSbPart: '/machineCost/queryMachineCostAssetSbPart',
  queryMachineCost: '/machineCost/queryMachineCost',
  modifyMachineCost: '/machineCost/modifyMachineCost',
  insertNewMachineCostRecord: '/machineCost/insertNewMachineCostRecord'
}

export function insertNewMachineCostRecord (data) {
  return axios({
    url: api.insertNewMachineCostRecord,
    method: 'post',
    data: data
  })
}

export function modifyMachineCost (data) {
  return axios({
    url: api.modifyMachineCost,
    method: 'post',
    data: data
  })
}

export function queryMachineCost (data) {
  return axios({
    url: api.queryMachineCost,
    method: 'post',
    data: data
  })
}

export function modifyMachineCostTypeOne (data) {
  return axios({
    url: api.modifyMachineCostTypeOne,
    method: 'post',
    data: data
  })
}

export function queryMachineCostTypeTwo (data) {
  return axios({
    url: api.queryMachineCostTypeTwo,
    method: 'post',
    data: data
  })
}

export function modifyMachineCostTypeTwo (data) {
  return axios({
    url: api.modifyMachineCostTypeTwo,
    method: 'post',
    data: data
  })
}

export function modifyMachineCostAssetSb (data) {
  return axios({
    url: api.modifyMachineCostAssetSb,
    method: 'post',
    data: data
  })
}

export function queryMachineCostAssetSb (data) {
  return axios({
    url: api.queryMachineCostAssetSb,
    method: 'post',
    data: data
  })
}

export function modifyMachineCostAssetSbPart (data) {
  return axios({
    url: api.modifyMachineCostAssetSbPart,
    method: 'post',
    data: data
  })
}

export function queryMachineCostAssetSbPart (data) {
  return axios({
    url: api.queryMachineCostAssetSbPart,
    method: 'post',
    data: data
  })
}

export function queryMachineCostTypeOne (data) {
  return axios({
    url: api.queryMachineCostTypeOne,
    method: 'post',
    data: data
  })
}

export default api
