import { axios } from '@/utils/request'

/**
 *  queryProject: 特种作业查询
 *  modifyProject: 特种作业增删改
 */

const api = {
  queryAssetXxhCheckParent: '/assetXxhCheck/queryAssetXxhCheckParent',
  queryAssetXxhCheckChildGroup: '/assetXxhCheck/queryAssetXxhCheckChildGroup',
  queryAssetXxhCheckChildDetail: '/assetXxhCheck/queryAssetXxhCheckChildDetail',
  queryAssetXxhCheckResult: '/assetXxhCheck/queryAssetXxhCheckResult',
  modifyAssetXxhCheckResult: '/assetXxhCheck/modifyAssetXxhCheckResult',
  oneClickAssetXxhCheckResult: '/assetXxhCheck/oneClickAssetXxhCheckResult',
  queryAssetXxhCheckRemainProb: '/assetXxhCheck/queryAssetXxhCheckRemainProb'
}

export function oneClickAssetXxhCheckResult (data) {
  return axios({
    url: api.oneClickAssetXxhCheckResult,
    method: 'post',
    data: data
  })
}

export function queryAssetXxhCheckRemainProb (data) {
  return axios({
    url: api.queryAssetXxhCheckRemainProb,
    method: 'post',
    data: data
  })
}

export function modifyAssetXxhCheckResult (data) {
  return axios({
    url: api.modifyAssetXxhCheckResult,
    method: 'post',
    data: data
  })
}

export function queryAssetXxhCheckChildGroup (data) {
  return axios({
    url: api.queryAssetXxhCheckChildGroup,
    method: 'post',
    data: data
  })
}

export function queryAssetXxhCheckChildDetail (data) {
  return axios({
    url: api.queryAssetXxhCheckChildDetail,
    method: 'post',
    data: data
  })
}

export function queryAssetXxhCheckResult (data) {
  return axios({
    url: api.queryAssetXxhCheckResult,
    method: 'post',
    data: data
  })
}

export function queryAssetXxhCheckParent (data) {
  return axios({
    url: api.queryAssetXxhCheckParent,
    method: 'post',
    data: data
  })
}

export default api
