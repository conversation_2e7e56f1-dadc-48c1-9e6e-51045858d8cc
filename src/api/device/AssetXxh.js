import { axios } from '@/utils/request'

/**
 *  queryProject: 特种作业查询
 *  modifyProject: 特种作业增删改
 */

const api = {
  queryAssetXxhCheckTemplate: '/assetXxh/queryAssetXxhCheckTemplate',
  modifyAssetXxhCheckTemplate: '/assetXxh/modifyAssetXxhCheckTemplate',
  queryAssetXxhCheck: '/assetXxhCheck/queryAssetXxhCheck',
  modifyAssetXxhCheck: '/assetXxhCheck/modifyAssetXxhCheck',
  modifyAssetXxhCheckPerson: '/assetXxh/modifyAssetXxhCheckPerson',
  queryAssetXxhCheckPerson: '/assetXxh/queryAssetXxhCheckPerson',
  queryAssetXxh: '/assetXxh/queryAssetXxh',
  modifyAssetXxh: '/assetXxh/modifyAssetXxh',
  queryXxhDigitalVideoByCamera: '/assetXxh/queryXxhDigitalVideoByCamera',
  entrustAssetXxhCheckResult: '/assetXxhCheck/entrustAssetXxhCheckResult',
  exportXxhMonthRecord: '/assetXxhCheck/exportXxhMonthRecord'
}

export function exportXxhMonthRecord (data) {
  return axios({
    url: api.exportXxhMonthRecord,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function entrustAssetXxhCheckResult (data) {
  return axios({
    url: api.entrustAssetXxhCheckResult,
    method: 'post',
    data: data
  })
}
export function queryXxhDigitalVideoByCamera (data) {
  return axios({
    url: api.queryXxhDigitalVideoByCamera,
    method: 'post',
    data: data
  })
}

export function modifyAssetXxh (data) {
  return axios({
    url: api.modifyAssetXxh,
    method: 'post',
    data: data
  })
}

export function queryAssetXxh (data) {
  return axios({
    url: api.queryAssetXxh,
    method: 'post',
    data: data
  })
}

export function queryAssetXxhCheckPerson (data) {
  return axios({
    url: api.queryAssetXxhCheckPerson,
    method: 'post',
    data: data
  })
}

export function modifyAssetXxhCheckPerson (data) {
  return axios({
    url: api.modifyAssetXxhCheckPerson,
    method: 'post',
    data: data
  })
}

export function modifyAssetXxhCheck (data) {
  return axios({
    url: api.modifyAssetXxhCheck,
    method: 'post',
    data: data
  })
}

export function queryAssetXxhCheck (data) {
  return axios({
    url: api.queryAssetXxhCheck,
    method: 'post',
    data: data
  })
}

export function modifyAssetXxhCheckTemplate (data) {
  return axios({
    url: api.modifyAssetXxhCheckTemplate,
    method: 'post',
    data: data
  })
}

export function queryAssetXxhCheckTemplate (data) {
  return axios({
    url: api.queryAssetXxhCheckTemplate,
    method: 'post',
    data: data
  })
}
export default api
