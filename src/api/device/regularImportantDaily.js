import { axios } from '@/utils/request'
/**
 */

const api = {
  queryRegularRepairPlanAll: '/regularImportantDaily/queryRegularRepairPlanAll',
  queryRegularImportantDaily: '/regularImportantDaily/queryRegularImportantDaily',
  modifyRegularImportantDaily: '/regularImportantDaily/modifyRegularImportantDaily',
  queryRegularImportantDailyStatistics: '/regularImportantDaily/queryRegularImportantDailyStatistics',
  queryRegularImportantHandle: '/regularImportantHandle/queryRegularImportantHandle',
  modifyRegularImportantHandle: '/regularImportantHandle/modifyRegularImportantHandle',
  doBuildDailyTask: '/regularImportantDaily/doBuildDailyTask',
  doExport: '/regularImportantDaily/doExport',
  queryRegularImportantPerson: '/regularImportantPerson/queryRegularImportantPerson',
  modifyRegularImportantPerson: '/regularImportantPerson/modifyRegularImportantPerson'
}

export function queryRegularImportantPerson (data) {
  return axios({
    url: api.queryRegularImportantPerson,
    method: 'post',
    data: data
  })
}

export function modifyRegularImportantPerson (data) {
  return axios({
    url: api.modifyRegularImportantPerson,
    method: 'post',
    data: data
  })
}
export function doExport (data) {
  return axios({
    url: api.doExport,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}

export function queryRegularImportantDailyStatistics (data) {
  return axios({
    url: api.queryRegularImportantDailyStatistics,
    method: 'post',
    data: data
  })
}

export function doBuildDailyTask (data) {
  return axios({
    url: api.doBuildDailyTask,
    method: 'post',
    data: data
  })
}

export function queryRegularImportantHandle (data) {
  return axios({
    url: api.queryRegularImportantHandle,
    method: 'post',
    data: data
  })
}

export function modifyRegularImportantHandle (data) {
  return axios({
    url: api.modifyRegularImportantHandle,
    method: 'post',
    data: data
  })
}

export function queryRegularImportantDaily (data) {
  return axios({
    url: api.queryRegularImportantDaily,
    method: 'post',
    data: data
  })
}

export function modifyRegularImportantDaily (data) {
  return axios({
    url: api.modifyRegularImportantDaily,
    method: 'post',
    data: data
  })
}

export function queryRegularRepairPlanAll (data) {
  return axios({
    url: api.queryRegularRepairPlanAll,
    method: 'post',
    data: data
  })
}

export default api
