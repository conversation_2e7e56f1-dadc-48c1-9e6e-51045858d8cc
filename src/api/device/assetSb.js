// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 *  queryAssetSbItem: 获取设备项目
 *  modifyAssetSbItem: 修改设备项目
 *  deleteAssetSbItem: 删除设备项目
 *  uploadAssetSbItem: 导入设备项目
 *
 *  queryEquipment: 获取设备台账信息（分页）
 *  queryLocationEquipment: 获取设备台账信息（设备树）
 *  queryAllEquipment: 获取设备台账信息（查询所有）
 *  queryUnBindEquipment: 获取设备台账信息（未绑定）
 *  queryAssetSbAs: 获取设备台账年检信息
 *  modifyEquipment: 修改设备台账信息
 *  deleteEquipment: 删除设备台账信息
 *
 *  queryOperationRecord: 获取运行周报
 *  deleteOperationRecord: 删除运行周报
 *  queryOperationRecordMonth: 获取运行月报
 *  deleteOperationRecordMonth: 删除运行月报
 *
 *  exportImportTemplete: 下载导入模版
 *
 *  getAssetQrCode:生成设备信息二维码
 *  doExportAssetSb:导出
 *  transfer2ETMS: 将数据同步到etms系统
 * queryMaintainObject: 查询维修对象
 * queryAssetOutsourceItem：查询外包信息
 *
 */
const api = {
  // 设备台账
  queryAssetSbItem: '/assetSb/queryAssetSbItem',
  modifyAssetSbItem: '/assetSb/modifyAssetSbItem',
  deleteAssetSbItem: '/assetSb/modifyAssetSbItem',
  uploadAssetSbItem: '/assetSb/uploadAssetSb',

  queryEquipment: '/assetSb/queryAssetSb',
  queryMaintainObject: '/assetSb/queryMaintainObject',
  queryAssetOutsourceItem: '/assetSb/queryAssetOutsourceItem',
  modifyAssetOutsourceItem: '/assetSb/modifyAssetOutsourceItem',
  queryLocationEquipment: '/assetSb/queryAssetSbByLocation',
  queryAllEquipment: '/assetSb/queryAssetSbNotPage',
  queryUnBindEquipment: '/assetSb/queryAssetSbLocationNull',
  queryAssetSbAs: '/assetSb/queryAssetSbAs',
  modifyEquipment: '/assetSb/modifyAssetSb',
  deleteEquipment: '/assetSb/modifyAssetSb',

  connectMaintainPlan: '/assetSbOperationRecord/connectMaintainPlan',
  queryOperationRecord: '/assetSbOperationRecord/queryAssetSbOperationRecord',
  deleteOperationRecord: '/assetSbOperationRecord/deleteAssetSbOperationRecord',
  queryOperationRecordMonth: '/assetSbOperationRecord/queryAssetSbOperationRecordMonth',
  deleteOperationRecordMonth: '/assetSbOperationRecord/deleteAssetSbOperationRecord',

  exportImportTemplete: '/file/modelDownload?fileName=assetsbimportmodel',
  exportImportTempleteWeekGn: '/file/modelDownload?fileName=operateRecordweekgn',
  exportImportTempleteWeekGw: '/file/modelDownload?fileName=operateRecordweekgw',
  exportImportTempleteMonthGn: '/file/modelDownload?fileName=operateRecordmonthgn',
  exportImportTempleteMonthGw: '/file/modelDownload?fileName=operateRecordmonthgw',

  getAssetQrCode: '/assetSb/getAssetQrCode',
  getAssetPartQrCode: '/assetSb/getAssetPartQrCode',
  queryResume: '/assetSb/queryResume',
  doExportAssetSb: '/assetSb/doExportAssetSb',
  // 项目配件查询及增删改
  queryAssetSbPart: '/assetSb/queryAssetSbPart',
  modifyAssetSbPart: '/assetSb/modifyAssetSbPart',

  // 抢修记录查询及增删改
  queryEmergencyRepairRecord: '/assetSb/queryEmergencyRepairRecord',
  modifyEmergencyRepairRecord: '/assetSb/modifyEmergencyRepairRecord',
  exportEmergencyRepairRecord: '/assetSb/exportEmergencyRepairRecord',
  transfer2ETMS: '/assetSb/transfer2ETMS'
}

export function queryAssetSbItem (data) {
  return axios({
    url: api.queryAssetSbItem,
    method: 'post',
    data: data
  })
}

export function modifyAssetSbItem (data) {
  return axios({
    url: api.modifyAssetSbItem,
    method: 'post',
    data: data
  })
}

export function deleteAssetSbItem (data) {
  return axios({
    url: api.deleteAssetSbItem,
    method: 'post',
    data: data
  })
}

export function uploadAssetSbItem (data, fn) {
  return axios({
    url: api.uploadAssetSbItem,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data;charset=UTF-8'
    },
    transformRequest: [
      function (data) {
        return data
      }
    ],
    onUploadProgress: fn,
    data: data
  })
}

export function queryEquipment (data) {
  return axios({
    url: api.queryEquipment,
    method: 'post',
    data: data
  })
}

export function queryMaintainObject (data) {
  return axios({
    url: api.queryMaintainObject,
    method: 'post',
    data: data
  })
}

export function queryAssetOutsourceItem (data) {
  return axios({
    url: api.queryAssetOutsourceItem,
    method: 'post',
    data: data
  })
}

export function modifyAssetOutsourceItem (data) {
  return axios({
    url: api.modifyAssetOutsourceItem,
    method: 'post',
    data: data
  })
}

export function queryLocationEquipment (data) {
  return axios({
    url: api.queryLocationEquipment,
    method: 'post',
    data: data
  })
}

export function queryAllEquipment (data) {
  return axios({
    url: api.queryAllEquipment,
    method: 'post',
    data: data
  })
}

export function queryUnBindEquipment (data) {
  return axios({
    url: api.queryUnBindEquipment,
    method: 'post',
    data: data
  })
}

export function queryAssetSbAs (data) {
  return axios({
    url: api.queryAssetSbAs,
    method: 'post',
    data: data
  })
}

export function modifyEquipment (data) {
  return axios({
    url: api.modifyEquipment,
    method: 'post',
    data: data
  })
}

export function deleteEquipment (data) {
  return axios({
    url: api.deleteEquipment,
    method: 'post',
    data: data
  })
}

export function connectMaintainPlan (data) {
  return axios({
    url: api.connectMaintainPlan,
    method: 'post',
    data: data
  })
}

export function queryOperationRecord (data) {
  return axios({
    url: api.queryOperationRecord,
    method: 'post',
    data: data
  })
}

export function deleteOperationRecord (data) {
  return axios({
    url: api.deleteOperationRecord,
    method: 'post',
    data: data
  })
}

export function queryOperationRecordMonth (data) {
  return axios({
    url: api.queryOperationRecordMonth,
    method: 'post',
    data: data
  })
}

export function deleteOperationRecordMonth (data) {
  return axios({
    url: api.deleteOperationRecordMonth,
    method: 'post',
    data: data
  })
}

export function exportImportTemplete (data) {
  return axios({
    url: api.exportImportTemplete,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export function exportImportTempleteWeekGn (data) {
  return axios({
    url: api.exportImportTempleteWeekGn,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export function exportImportTempleteWeekGw (data) {
  return axios({
    url: api.exportImportTempleteWeekGw,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export function exportImportTempleteMonthGn (data) {
  return axios({
    url: api.exportImportTempleteMonthGn,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export function exportImportTempleteMonthGw (data) {
  return axios({
    url: api.exportImportTempleteMonthGw,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export function getAssetQrCode (data) {
  return axios({
    url: api.getAssetQrCode,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}

export function getAssetPartQrCode (data) {
  return axios({
    url: api.getAssetPartQrCode,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}

export function queryResume (data) {
  return axios({
    url: api.queryResume,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}

export function doExportAssetSb (data) {
  return axios({
    url: api.doExportAssetSb,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}

export function queryAssetSbPart (data) {
  return axios({
    url: api.queryAssetSbPart,
    method: 'post',
    data: data
  })
}

export function modifyAssetSbPart (data) {
  return axios({
    url: api.modifyAssetSbPart,
    method: 'post',
    data: data
  })
}

export function queryEmergencyRepairRecord (data) {
  return axios({
    url: api.queryEmergencyRepairRecord,
    method: 'post',
    data: data
  })
}

export function modifyEmergencyRepairRecord (data) {
  return axios({
    url: api.modifyEmergencyRepairRecord,
    method: 'post',
    data: data
  })
}

export function exportEmergencyRepairRecord (data) {
  return axios({
    url: api.exportEmergencyRepairRecord,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}

export function transfer2ETMS (data) {
  return axios({
    url: api.transfer2ETMS,
    method: 'post',
    data: data
  })
}
export default api
