import { axios } from '@/utils/request'

/**
 *  queryProject: 项目查询
 *  modifyProject: 项目增删改
 *  urgeProjectFollow: 项目进度催办
 *  projectHandOver: 项目移交
 */

const api = {
  queryProject: '/project/queryProject',
  modifyProject: '/project/modifyProject',
  queryRelateProject: '/project/queryRelateProject',
  // 项目对应的每月计划的增删改查
  queryProjectFollow: '/project/queryProjectFollow',
  modifyProjectFollow: '/project/modifyProjectFollow',
  queryProjectFollowYear: '/project/queryProjectFollowYear',
  urgeProjectFollow: '/project/urgeProjectFollow',
  approveProject: '/project/approveProject',
  queryProjectSummary: '/project/queryProjectSummary',
  exportProjectSummary: '/project/exportProjectSummary',
  queryProjectStatisticsTotal: '/project/queryProjectStatisticsTotal',
  queryProjectStatistics: '/project/queryProjectStatistics',
  projectHandOver: '/project/projectHandOver',
  queryProjectHandOverRecord: '/project/queryProjectHandOverRecord',
  getProjectYearFinsihAmount: '/project/getProjectYearFinsihAmount',
  queryProjectFollowWeek: '/project/queryProjectFollowWeek',
  addWeekInfo: '/project/addWeekInfo'
}

export function addWeekInfo (data) {
  return axios({
    url: api.addWeekInfo,
    method: 'post',
    data: data
  })
}

export function queryProjectFollowWeek (data) {
  return axios({
    url: api.queryProjectFollowWeek,
    method: 'post',
    data: data
  })
}

export function getProjectYearFinsihAmount (data) {
  return axios({
    url: api.getProjectYearFinsihAmount,
    method: 'post',
    data: data
  })
}
export function terminateProject (data) {
  return axios({
    url: api.terminateProject,
    method: 'post',
    data: data
  })
}

export function queryProjectHandOverRecord (data) {
  return axios({
    url: api.queryProjectHandOverRecord,
    method: 'post',
    data: data
  })
}

export function projectHandOver (data) {
  return axios({
    url: api.projectHandOver,
    method: 'post',
    data: data
  })
}

export function queryProjectStatisticsTotal (data) {
  return axios({
    url: api.queryProjectStatisticsTotal,
    method: 'post',
    data: data
  })
}

export function queryProjectStatistics (data) {
  return axios({
    url: api.queryProjectStatistics,
    method: 'post',
    data: data
  })
}

export function exportProjectSummary (data) {
  return axios({
    url: api.exportProjectSummary,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function approveProject (data) {
  return axios({
    url: api.approveProject,
    method: 'post',
    data: data
  })
}

export function urgeProjectFollow (data) {
  return axios({
    url: api.urgeProjectFollow,
    method: 'post',
    data: data
  })
}

export function queryProjectFollowYear (data) {
  return axios({
    url: api.queryProjectFollowYear,
    method: 'post',
    data: data
  })
}

export function queryProjectSummary (data) {
  return axios({
    url: api.queryProjectSummary,
    method: 'post',
    data: data
  })
}

export function queryProject (data) {
  return axios({
    url: api.queryProject,
    method: 'post',
    data: data
  })
}

export function queryRelateProject (data) {
  return axios({
    url: api.queryRelateProject,
    method: 'post',
    data: data
  })
}

export function modifyProject (data) {
  return axios({
    url: api.modifyProject,
    method: 'post',
    data: data
  })
}

export function queryProjectFollow (data) {
  return axios({
    url: api.queryProjectFollow,
    method: 'post',
    data: data
  })
}

export function modifyProjectFollow (data) {
  return axios({
    url: api.modifyProjectFollow,
    method: 'post',
    data: data
  })
}
export default api
