import { axios } from '@/utils/request'

/**
 *  queryProject: 特种作业查询
 *  modifyProject: 特种作业增删改
 */

const api = {
  queryAssetXxhInventory: '/assetXxhInventory/queryAssetXxhInventory',
  modifyAssetXxhInventory: '/assetXxhInventory/modifyAssetXxhInventory',
  queryAssetXxhReceive: '/assetXxhReceive/queryAssetXxhReceive',
  modifyAssetXxhReceive: '/assetXxhReceive/modifyAssetXxhReceive',
  exportAssetXxhReceive: '/assetXxhReceive/doExport',
  approveAssetXxhReceive: '/assetXxhReceive/approveAssetXxhReceive'
}

export function queryAssetXxhInventory (data) {
  return axios({
    url: api.queryAssetXxhInventory,
    method: 'post',
    data: data
  })
}

export function modifyAssetXxhInventory (data) {
  return axios({
    url: api.modifyAssetXxhInventory,
    method: 'post',
    data: data
  })
}

export function approveAssetXxhReceive (data) {
  return axios({
    url: api.approveAssetXxhReceive,
    method: 'post',
    data: data
  })
}

export function exportAssetXxhReceive (data) {
  return axios({
    url: api.exportAssetXxhReceive,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}

export function queryAssetXxhReceive (data) {
  return axios({
    url: api.queryAssetXxhReceive,
    method: 'post',
    data: data
  })
}

export function modifyAssetXxhReceive (data) {
  return axios({
    url: api.modifyAssetXxhReceive,
    method: 'post',
    data: data
  })
}
export default api
