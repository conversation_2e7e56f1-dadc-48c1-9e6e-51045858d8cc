import { axios } from '@/utils/request'

/**
 *  queryProject: 特种作业查询
 *  modifyProject: 特种作业增删改
 */

const api = {
  querySpecialOperator: '/specialOperator/querySpecialOperator',
  modifySpecialOperator: '/specialOperator/modifySpecialOperator',
  uploadSpecialOperator: '/specialOperator/uploadSpecialOperator',
  exportTemplateSpecialOperator: '/file/modelDownload?fileName=特作证导入模板',
  exportSpecialOperator: '/specialOperator/doExport'
}

export function exportSpecialOperator (data) {
  return axios({
    url: api.exportSpecialOperator,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}

export function exportTemplateSpecialOperator (data) {
  return axios({
    url: api.exportTemplateSpecialOperator,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export function uploadSpecialOperator (data) {
  return axios({
    url: api.uploadSpecialOperator,
    responseType: 'json',
    method: 'post',
    data: data
  })
}

export function querySpecialOperator (data) {
  return axios({
    url: api.querySpecialOperator,
    method: 'post',
    data: data
  })
}

export function modifySpecialOperator (data) {
  return axios({
    url: api.modifySpecialOperator,
    method: 'post',
    data: data
  })
}
export default api
