import { axios } from '@/utils/request'

/**
 *  queryAssetMainPlan: 获取保养计划
 *  modifyAssetMainPlan: 修改保养计划
 *  deleteAssetMainPlan: 删除保养计划
 *
 *  modifyAssetMainArea: 修改保养计划区域
 *  deleteAssetMainArea: 删除保养计划区域
 *
 *  queryAssetMainItem: 查询保养计划项目
 *  modifyAssetMainItem: 修改保养计划项目
 *  deleteAssetMainItem: 删除保养计划项目
 *
 *  queryAssetMainTask: 获取保养计划
 *  exportMainFeekback:导出保养反馈报表
 *  queryAssetMainFeedback: 查询保养计划反馈
 *  modifyAssetMainFeedback: 修改保养计划反馈
 *  saveWorkorderByMaintain: 保养反馈选择设备生成工单
 *  modifyFeedback: 修改保养计划反馈
 *  queryMaintainTask: 查询保养
 *
 */
const api = {
  queryAssetMainPlan: '/maintainPlan/queryMaintainPlan',
  modifyAssetMainPlan: '/maintainPlan/modifyMaintainPlan',
  deleteAssetMainPlan: '/maintainPlan/modifyMaintainPlan',

  modifyAssetMainArea: '/maintainPlan/updateMaintainPlanAreaOnly',
  deleteAssetMainArea: '/maintainPlan/removeMaintainPlanAreaOnly',

  queryAssetMainItem: '/maintainPlan/queryItem',
  modifyAssetMainItem: '/maintainPlan/modifyItem',
  deleteAssetMainItem: '/maintainPlan/modifyItem',

  queryAssetMainTask: '/maintainPlan/queryFeedback',
  exportMainFeekback: '/maintainPlan/exportFeedback',
  queryAssetMainFeedback: '/maintainPlan/queryFeedbackItem',
  modifyAssetMainFeedback: '/maintainPlan/modifyFeedback',
  saveWorkorderByMaintain: '/maintainPlan/saveWorkorderByMaintain',

  getAllFeedBackDate: '/maintainPlan/getAllFeedBackDate',
  modifyFeedback: '/maintainPlan/modifyFeedback',
  queryMaintainTaskGroupPart: '/maintainPlan/queryFeedbackGroupPart',
  queryMaintainTask: '/maintainPlan/queryFeedback'
}
export function queryMaintainTask (data) {
  return axios({
    url: api.queryMaintainTask,
    method: 'post',
    data: data
  })
}

export function queryMaintainTaskGroupPart (data) {
  return axios({
    url: api.queryMaintainTaskGroupPart,
    method: 'post',
    data: data
  })
}

export function modifyFeedback (data) {
  return axios({
    url: api.modifyFeedback,
    method: 'post',
    data: data
  })
}

export function saveWorkorderByMaintain (data) {
  return axios({
    url: api.saveWorkorderByMaintain,
    method: 'post',
    data: data
  })
}

export function queryAssetMainPlan (data) {
  return axios({
    url: api.queryAssetMainPlan,
    method: 'post',
    data: data
  })
}

export function modifyAssetMainPlan (data) {
  return axios({
    url: api.modifyAssetMainPlan,
    method: 'post',
    data: data
  })
}

export function deleteAssetMainPlan (data) {
  return axios({
    url: api.deleteAssetMainPlan,
    method: 'post',
    data: data
  })
}

export function modifyAssetMainArea (data) {
  return axios({
    url: api.modifyAssetMainArea,
    method: 'post',
    data: data
  })
}

export function deleteAssetMainArea (data) {
  return axios({
    url: api.deleteAssetMainArea,
    method: 'post',
    data: data
  })
}

export function queryAssetMainItem (data) {
  return axios({
    url: api.queryAssetMainItem,
    method: 'post',
    data: data
  })
}

export function modifyAssetMainItem (data) {
  return axios({
    url: api.modifyAssetMainItem,
    method: 'post',
    data: data
  })
}

export function deleteAssetMainItem (data) {
  return axios({
    url: api.deleteAssetMainItem,
    method: 'post',
    data: data
  })
}

export function queryAssetMainTask (data) {
  return axios({
    url: api.queryAssetMainTask,
    method: 'post',
    data: data
  })
}

export function exportMainFeekback (data) {
  return axios({
    url: api.exportMainFeekback,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryAssetMainFeedback (data) {
  return axios({
    url: api.queryAssetMainFeedback,
    method: 'post',
    data: data
  })
}

export function modifyAssetMainFeedback (data) {
  return axios({
    url: api.modifyAssetMainFeedback,
    method: 'post',
    data: data
  })
}

export function getAllFeedBackDate (data) {
  return axios({
    url: api.getAllFeedBackDate,
    method: 'post',
    data: data
  })
}

export default api
