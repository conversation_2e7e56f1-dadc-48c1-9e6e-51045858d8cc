// qs.stringify 将对象或者数组序列化成URL的格式 -- get请求中使用, 以便后台获取参数
// qs.stringify({ key: [1,2,3] }， { indices: false }) ---> key=1&key=2&key=3
// qs.stringify({ key: [1,2,3] }) ---> key[0]=1&key[1]=2&key[2]=3
// import { qs, axios } from '@/utils/request'
import { axios } from '@/utils/request'

/**
 *  queryAssetCheckPlan: 获取点检计划
 *  modifyAssetCheckPlan: 修改点检计划
 *  deleteAssetCheckPlan: 删除点检计划
 *
 *  modifyAssetCheckArea: 修改点检计划区域
 *  deleteAssetCheckArea: 删除点检计划区域
 *
 *  queryAssetCheckItem: 查询点检计划项目
 *  modifyAssetCheckItem: 修改点检计划项目
 *  deleteAssetCheckItem: 删除点检计划项目
 *
 *  queryAssetCheckTask: 获取点检计划/任务
 *  queryAssetCheckFeedback: 查询点检计划反馈
 *  modifyAssetCheckFeedback: 修改点检计划反馈
 *  createCheckFeedbackWorkorder: 点检日历选择设备生成工单
 *  getAllAssetCheckPlanDate: 获取所有点检计划日期
 *
 *  queryAssetCheckInfo: 大地图查询设备点检情况
 *  queryAssetCheckPlanFeedbackFuture： 待生成点检任务
 *  queryFailureAssetCheckDetail: 查询故障率高的项目明细
 *
 */
const api = {
  queryAssetCheckPlan: '/assetCheckPlan/queryAssetCheckPlan',
  modifyAssetCheckPlan: '/assetCheckPlan/modifyAssetCheckPlan',
  deleteAssetCheckPlan: '/assetCheckPlan/modifyAssetCheckPlan',
  saveFeedbackByMobile: '/assetCheckPlan/saveFeedbackByMobile',

  modifyAssetCheckArea: '/assetCheckPlan/updateAssetCheckPlanAreaOnly',
  deleteAssetCheckArea: '/assetCheckPlan/removeAssetCheckPlanAreaOnly',

  queryAssetCheckItem: '/assetCheckPlan/queryItem',
  modifyAssetCheckItem: '/assetCheckPlan/modifyItem',
  deleteAssetCheckItem: '/assetCheckPlan/modifyItem',

  queryAssetCheckTask: '/assetCheckPlan/queryFeedback',
  queryAssetCheckFeedback: '/assetCheckPlan/queryFeedbackItem',
  modifyAssetCheckFeedback: '/assetCheckPlan/modifyFeedback',
  queryAssetCheckTaskGroupPart: '/assetCheckPlan/queryFeedbackGroupPart',

  createCheckFeedbackWorkorder: '/assetCheckPlan/saveWorkorderByAssetCheck',
  getAllFeedBackDate: '/assetCheckPlan/getAllFeedBackDate',
  approveCheckFeedback: '/assetCheckPlan/approveCheckFeedback',
  queryAssetCheckPartByFeedBack: '/assetCheckObject/queryAssetCheckPartByFeedBack',
  queryAssetCheckStatistics: '/assetCheckPlan/queryAssetCheckStatistics',
  queryRegularRepairPlanStatisticsDetail: '/regularRepair/queryRegularRepairPlanStatisticsDetail',
  queryRegularRepairPlanStatistics: '/regularRepair/queryRegularRepairPlanStatistics',
  queryAssetCheckInfo: '/assetCheckPlan/queryAssetCheckInfo',
  queryAssetCheckPlanFeedbackFuture: '/assetCheckPlan/queryAssetCheckPlanFeedbackFuture',
  queryAssetCheckPlanFeedback: '/assetCheckPlan/queryAssetCheckPlanFeedback',
  queryFailureAssetCheckDetail: '/assetCheckPlan/queryFailureAssetCheckDetail',
  exportCheckRecord: '/assetCheckPlan/exportCheckRecord',
  queryAssetCheckTaskTypeCount: '/assetCheckPlan/queryAssetCheckTaskTypeCount'
}
export function queryAssetCheckTaskTypeCount (data) {
  return axios({
    url: api.queryAssetCheckTaskTypeCount,
    method: 'post',
    data: data
  })
}

export function exportCheckRecord (data) {
  return axios({
    url: api.exportCheckRecord,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryFailureAssetCheckDetail (data) {
  return axios({
    url: api.queryFailureAssetCheckDetail,
    method: 'post',
    data: data
  })
}

export function queryAssetCheckPlanFeedback (data) {
  return axios({
    url: api.queryAssetCheckPlanFeedback,
    method: 'post',
    data: data
  })
}

export function queryAssetCheckPlanFeedbackFuture (data) {
  return axios({
    url: api.queryAssetCheckPlanFeedbackFuture,
    method: 'post',
    data: data
  })
}

export function queryAssetCheckInfo (data) {
  return axios({
    url: api.queryAssetCheckInfo,
    method: 'post',
    data: data
  })
}

export function saveFeedbackByMobile (data) {
  return axios({
    url: api.saveFeedbackByMobile,
    method: 'post',
    data: data
  })
}

export function queryRegularRepairPlanStatisticsDetail (data) {
  return axios({
    url: api.queryRegularRepairPlanStatisticsDetail,
    method: 'post',
    data: data
  })
}

export function queryRegularRepairPlanStatistics (data) {
  return axios({
    url: api.queryRegularRepairPlanStatistics,
    method: 'post',
    data: data
  })
}

export function queryAssetCheckStatistics (data) {
  return axios({
    url: api.queryAssetCheckStatistics,
    method: 'post',
    data: data
  })
}

export function queryAssetCheckPartByFeedBack (data) {
  return axios({
    url: api.queryAssetCheckPartByFeedBack,
    method: 'post',
    data: data
  })
}

export function queryAssetCheckPlan (data) {
  return axios({
    url: api.queryAssetCheckPlan,
    method: 'post',
    data: data
  })
}

export function modifyAssetCheckPlan (data) {
  return axios({
    url: api.modifyAssetCheckPlan,
    method: 'post',
    data: data
  })
}

export function deleteAssetCheckPlan (data) {
  return axios({
    url: api.deleteAssetCheckPlan,
    method: 'post',
    data: data
  })
}

export function modifyAssetCheckArea (data) {
  return axios({
    url: api.modifyAssetCheckArea,
    method: 'post',
    data: data
  })
}

export function deleteAssetCheckArea (data) {
  return axios({
    url: api.deleteAssetCheckArea,
    method: 'post',
    data: data
  })
}

export function queryAssetCheckItem (data) {
  return axios({
    url: api.queryAssetCheckItem,
    method: 'post',
    data: data
  })
}

export function modifyAssetCheckItem (data) {
  return axios({
    url: api.modifyAssetCheckItem,
    method: 'post',
    data: data
  })
}

export function deleteAssetCheckItem (data) {
  return axios({
    url: api.deleteAssetCheckItem,
    method: 'post',
    data: data
  })
}

export function queryAssetCheckTask (data) {
  return axios({
    url: api.queryAssetCheckTask,
    method: 'post',
    data: data
  })
}

export function queryAssetCheckTaskGroupPart (data) {
  return axios({
    url: api.queryAssetCheckTaskGroupPart,
    method: 'post',
    data: data
  })
}

export function queryAssetCheckFeedback (data) {
  return axios({
    url: api.queryAssetCheckFeedback,
    method: 'post',
    data: data
  })
}

export function modifyAssetCheckFeedback (data) {
  return axios({
    url: api.modifyAssetCheckFeedback,
    method: 'post',
    data: data
  })
}

export function createCheckFeedbackWorkorder (data) {
  return axios({
    url: api.createCheckFeedbackWorkorder,
    method: 'post',
    data: data
  })
}

export function getAllFeedBackDate (data) {
  return axios({
    url: api.getAllFeedBackDate,
    method: 'post',
    data: data
  })
}

export function approveCheckFeedback (data) {
  return axios({
    url: api.approveCheckFeedback,
    method: 'post',
    data: data
  })
}

export default api
