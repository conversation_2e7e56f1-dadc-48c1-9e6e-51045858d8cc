import { axios } from '@/utils/request'

/**
 *  queryProject: 特种作业查询
 *  modifyProject: 特种作业增删改
 */

const api = {
  // 查询车辆信息
  queryVehicleInfo: '/vehicle/queryVehicleInfo',
  modifyVehicleInfo: '/vehicle/modifyVehicleInfo',
  // 查询车辆保养信息
  queryVehicleMaintenance: '/vehicle/queryVehicleMaintenance',
  modifyVehicleMaintenance: '/vehicle/modifyVehicleMaintenance',
  // 查询车辆年检信息
  queryVehicleInspection: '/vehicle/queryVehicleInspection',
  modifyVehicleInspection: '/vehicle/modifyVehicleInspection',
  // 查询车辆里程信息
  queryVehicleMileage: '/vehicle/queryVehicleMileage',
  modifyVehicleMileage: '/vehicle/modifyVehicleMileage',
  // 过期车辆卡片
  queryVehicleInfoMap: '/vehicle/queryVehicleInfoMap',
  // 导入车辆信息
  uploadTemplateVehicle: '/vehicle/uploadTemplateVehicle',
  // 导入模板
  exportTemplateVehicle: '/file/modelDownload?fileName=车辆导入模板',
  // 导入维修记录
  uploadTemplateVehicleRepair: '/vehicle/uploadTemplateVehicleRepair',
  // 维修记录导入模板
  exportTemplateVehicleRepair: '/file/modelDownload?fileName=车辆维修记录导入模板',
  // 导入加油记录
  uploadTemplateVehicleGas: '/vehicle/uploadTemplateVehicleGas',
  // 加油记录导入模板
  exportTemplateVehicleGas: '/file/modelDownload?fileName=车辆加油记录导入模板',
  // 查询车辆加油记录
  queryVehicleGas: '/vehicle/queryVehicleGas',
  modifyVehicleGas: '/vehicle/modifyVehicleGas',
  // 查询车辆维修记录
  queryVehicleRepair: '/vehicle/queryVehicleRepair',
  modifyVehicleRepair: '/vehicle/modifyVehicleRepair',
  exportVehicleInfo: '/vehicle/exportVehicleInfo',
  exportVehicleInspection: '/vehicle/exportVehicleInspection',
  exportVehicleMaintenance: '/vehicle/exportVehicleMaintenance',
  exportVehicleMileage: '/vehicle/exportVehicleMileage',
  exportVehicleRepair: '/vehicle/exportVehicleRepair',
  exportVehicleGas: '/vehicle/exportVehicleGas',
  exportVehicleInspectionExpire: '/vehicle/exportVehicleInspectionExpire',
  exportVehicleMaintenanceExpire: '/vehicle/exportVehicleMaintenanceExpire',
  handleSendNotification: '/vehicle/handleSendNotification',
  // 查询历史通知记录
  queryNotificationHistory: '/vehicle/queryNotificationHistory'
}

export function handleSendNotification (data) {
  return axios({
    url: api.handleSendNotification,
    method: 'post',
    data: data
  })
}

export function queryNotificationHistory (data) {
  return axios({
    url: api.queryNotificationHistory,
    method: 'post',
    data: data
  })
}
export function exportVehicleMaintenanceExpire (data) {
  return axios({
    url: api.exportVehicleMaintenanceExpire,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function exportVehicleInspectionExpire (data) {
  return axios({
    url: api.exportVehicleInspectionExpire,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function exportVehicleGas (data) {
  return axios({
    url: api.exportVehicleGas,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function exportVehicleRepair (data) {
  return axios({
    url: api.exportVehicleRepair,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function exportVehicleMileage (data) {
  return axios({
    url: api.exportVehicleMileage,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function exportVehicleMaintenance (data) {
  return axios({
    url: api.exportVehicleMaintenance,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function exportVehicleInspection (data) {
  return axios({
    url: api.exportVehicleInspection,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function exportVehicleInfo (data) {
  return axios({
    url: api.exportVehicleInfo,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}
export function queryVehicleRepair (data) {
  return axios({
    url: api.queryVehicleRepair,
    method: 'post',
    data: data
  })
}

export function modifyVehicleRepair (data) {
  return axios({
    url: api.modifyVehicleRepair,
    method: 'post',
    data: data
  })
}

export function queryVehicleGas (data) {
  return axios({
    url: api.queryVehicleGas,
    method: 'post',
    data: data
  })
}

export function modifyVehicleGas (data) {
  return axios({
    url: api.modifyVehicleGas,
    method: 'post',
    data: data
  })
}
export function exportTemplateVehicle (data) {
  return axios({
    url: api.exportTemplateVehicle,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

export function uploadTemplateVehicle (data) {
  return axios({
    url: api.uploadTemplateVehicle,
    responseType: 'json',
    method: 'post',
    data: data
  })
}

// 导出维修记录模板
export function exportTemplateVehicleRepair (data) {
  return axios({
    url: api.exportTemplateVehicleRepair,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

// 导入维修记录
export function uploadTemplateVehicleRepair (data) {
  return axios({
    url: api.uploadTemplateVehicleRepair,
    responseType: 'json',
    method: 'post',
    data: data
  })
}

// 导出加油记录模板
export function exportTemplateVehicleGas (data) {
  return axios({
    url: api.exportTemplateVehicleGas,
    responseType: 'blob',
    method: 'get',
    data: data
  })
}

// 导入加油记录
export function uploadTemplateVehicleGas (data) {
  return axios({
    url: api.uploadTemplateVehicleGas,
    responseType: 'json',
    method: 'post',
    data: data
  })
}

export function queryVehicleInfoMap (data) {
  return axios({
    url: api.queryVehicleInfoMap,
    method: 'post',
    data: data
  })
}

export function queryVehicleMileage (data) {
  return axios({
    url: api.queryVehicleMileage,
    method: 'post',
    data: data
  })
}

export function modifyVehicleMileage (data) {
  return axios({
    url: api.modifyVehicleMileage,
    method: 'post',
    data: data
  })
}

export function queryVehicleInfo (data) {
  return axios({
    url: api.queryVehicleInfo,
    method: 'post',
    data: data
  })
}

export function modifyVehicleInfo (data) {
  return axios({
    url: api.modifyVehicleInfo,
    method: 'post',
    data: data
  })
}
export function queryVehicleMaintenance (data) {
  return axios({
    url: api.queryVehicleMaintenance,
    method: 'post',
    data: data
  })
}

export function modifyVehicleMaintenance (data) {
  return axios({
    url: api.modifyVehicleMaintenance,
    method: 'post',
    data: data
  })
}
export function queryVehicleInspection (data) {
  return axios({
    url: api.queryVehicleInspection,
    method: 'post',
    data: data
  })
}

export function modifyVehicleInspection (data) {
  return axios({
    url: api.modifyVehicleInspection,
    method: 'post',
    data: data
  })
}

export function saveVehicleFile (data) {
  return axios({
    url: api.saveVehicleFile,
    method: 'post',
    data: data
  })
}

export default api
