import { axios } from '@/utils/request'

/**
 */

const api = {
  queryYearPlanPurchase: '/planYearPurchase/queryYearPlanPurchase',
  modifyYearPlanPurchase: '/planYearPurchase/modifyYearPlanPurchase',
  queryYearPlanPurchaseDetail: '/planYearPurchase/queryYearPlanPurchaseDetail',
  modifyYearPlanPurchaseDetail: '/planYearPurchase/modifyYearPlanPurchaseDetail',
  queryYearPlanPurchaseHasPushed: '/planYearPurchase/queryYearPlanPurchaseHasPushed',
  pushYearPlanPurchase: '/planYearPurchase/pushYearPlanPurchase',
  doExport: '/planYearPurchase/doExport'
}

export function pushYearPlanPurchase (data) {
  return axios({
    url: api.pushYearPlanPurchase,
    method: 'post',
    data: data
  })
}

export function doExport (data) {
  return axios({
    url: api.doExport,
    responseType: 'blob',
    method: 'post',
    data: data
  })
}

export function queryYearPlanPurchaseHasPushed (data) {
  return axios({
    url: api.queryYearPlanPurchaseHasPushed,
    method: 'post',
    data: data
  })
}

export function queryYearPlanPurchase (data) {
  return axios({
    url: api.queryYearPlanPurchase,
    method: 'post',
    data: data
  })
}

export function modifyYearPlanPurchase (data) {
  return axios({
    url: api.modifyYearPlanPurchase,
    method: 'post',
    data: data
  })
}

export function queryYearPlanPurchaseDetail (data) {
  return axios({
    url: api.queryYearPlanPurchaseDetail,
    method: 'post',
    data: data
  })
}

export function modifyYearPlanPurchaseDetail (data) {
  return axios({
    url: api.modifyYearPlanPurchaseDetail,
    method: 'post',
    data: data
  })
}

export default api
