<template>
  <print-area ref="printArea">
    <section class="excel-container">
      <div
        v-if="!IS_TS_ENV"
        class="excel-item"
        v-for="(item, index) of list"
        break-avoid
        break-after
        :key="index"
      >

        <div class="excel-group">
          <div class="excel-header">
            <div class="mainTitle">
              <div class="orgName">{{ item.orgName }}</div>
              <div class="textName">
                <span style="letter-spacing: 1.5pt">{{ item.subTitle }}</span>
              </div>
            </div>
            <div class="subTitle">
              <div v-if="!(IS_JX_ENV || IS_MD)">
                <span style="margin: 0 3pt">单证类别:</span>
                {{ item.materialTypeName || '普通物资' }}
              </div>
              <div>
                <span style="margin: 0 3pt">领用部门:</span>
                {{ item.deptName }}
              </div>
              <div v-if="!IS_WZG" :style="{ marginRight: item.printNum ? '5pt' : '10pt' }">
                <span style="margin: 0 3pt">领用单号:</span>
                {{ item.prlineNum }}
              </div>
              <div v-if="IS_YZ_ENV" style="margin-right: 10pt">
                <span style="margin: 0 3pt">打印日期:</span>
                {{ item.printDate || printDate }}
              </div>
              <div v-if="item.printNum">
                <span style="margin: 0 8pt">打印编号:</span>
                {{ item.printNum }}
              </div>
            </div>
          </div>
          <div class="excel-content">
            <a-table
              class="table table-th-2 table-td-3"
              :columns="columns"
              :data-source="item.maplist"
              :pagination="false"
              rowKey="index"
              bordered
            />
          </div>
          <div class="excel-footer">

            <div>
              <span style="margin: 0 3pt">{{ IS_TS_ENV ? '发料员签章:李斌' : '发料员签章:' }}</span>
            </div>
            <div>
              <span style="margin: 0 3pt">领料人签章:</span>
            </div>
            <div>
              <span style="margin: 0 3pt"/>
            </div>
            <div v-if="item.totalCount">
              <span style="margin: 0 3pt">总数量:</span>
              {{ item.totalCount }}
            </div>
            <div>
              <span style="margin: 0 3pt">总计:</span>
              {{ item.totalCurrency }}
            </div>
            <div v-if="IS_YZ_ENV">
              <span style="margin: 0 3pt">税额:</span>
              {{ item.polinese }}
            </div>
            <div v-if="IS_YZ_ENV">
              <span style="margin: 0 3pt">税后总金额:</span>
              {{ item.afterTaxPolineNumCurrency }}
            </div>
            <div v-if="!IS_YZ_ENV" style="margin-right: 10pt">
              <span style="margin: 0 3pt">打印日期:</span>
              {{ item.printDate || printDate }}
            </div>
          </div>
        </div>

      </div>
      <div
        v-if="IS_TS_ENV"
        class="excel-item"
        v-for="(item, index) of list"
        break-avoid
        break-after
        :key="index"
      >
        <div class="excel-group">
          <div class="excel-header">
            <div class="mainTitle">
              <div class="orgName">{{ item.orgName }}</div>
              <div class="textName">
                <span style="letter-spacing: 1.5pt">{{ item.subTitle }}</span>
              </div>
            </div>
            <div class="subTitle">
              <span style="margin: 0 3pt">单证类别:</span>
              {{ item.materialTypeName || '普通物资' }}
              <span style="margin: 0 3pt">领用部门:</span>
              {{ item.deptName }}
              <div v-if="!IS_WZG" :style="{ marginRight: item.printNum ? '5pt' : '10pt' }">
                <span style="margin: 0 3pt">领用单号:</span>
                {{ item.prlineNum }}
              </div>
            </div>
          </div>
          <div class="excel-content">
            <a-table
              class="table table-th-2 table-td-3"
              :columns="columns"
              :data-source="item.maplist"
              :pagination="false"
              rowKey="index"
              bordered
            />
          </div>
          <div class="excel-footer">
            <div v-if="IS_TS_ENV" style="width: 100%;">
              <div style="line-height: 1.2; margin: 10pt 1pt 1pt 1pt">{{ '发料记录:'+item.remarkTS }}</div>
              <span style="margin: 0 0pt">{{'发料员签章:李斌\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0
              领料人签章:\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0 总数量:'+item.totalCount
              +'\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0总计:'+item.totalCurrency
              +'\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0打印日期:'+printDate }}
              </span>
            </div>
          </div>
        </div>

      </div>
    </section>
  </print-area>
</template>

<script>
import { PrintArea } from '@/components'
import '@/components/print.less'

// 定制化
import Vue from 'vue'
import { ORG_ID } from '@/store/mutation-types'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_BYJ_ENV = USER_ORG_ID === '1.100.101'
// 铁司
const IS_TS_ENV = USER_ORG_ID === '1.100.122'
const IS_WZG = USER_ORG_ID.substr(0, 5) === '1.101'
// 嘉兴乍浦
const IS_MD = USER_ORG_ID === '1.100.104'
const IS_JX_ENV = USER_ORG_ID === '1.102'
const IS_TCWF_ENV = USER_ORG_ID === '1.100.114'
const IS_YZ_ENV = USER_ORG_ID === '1.100.107'
const IS_JYS = USER_ORG_ID === '1.100.117'
export default {
  components: {
    PrintArea
  },
  data () {
    return {
      IS_JX_ENV,
      IS_WZG,
      IS_JYS,
      IS_TS_ENV,
      IS_MD,
      IS_TCWF_ENV,
      IS_YZ_ENV,
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          customRender: (text, row, index) => {
            return {
              children: row.materialNum ? (IS_MD ? (index + 1) : text) : ''
            }
          },
          width: '5%',
          align: 'center'
        },
        ...(IS_BYJ_ENV || IS_TS_ENV ? [{
          title: '\xa0仓\xa0\xa0\xa0库\xa0',
          dataIndex: 'storehouseName',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '5%',
          align: 'center'
        }]
          : []),
        {
          title: '物资代码',
          dataIndex: 'materialNum',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '9%',
          align: 'center'
        },
        ...(IS_JX_ENV ? [{
          title: '类别',
          dataIndex: 'materialTypeName',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '8%',
          align: 'center'
        }] : []),
        ...(IS_MD ? [{
          title: '二级类别',
          dataIndex: 'materialClass',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '8%',
          align: 'center'
        }] : []),
        {
          title: '物资名称',
          dataIndex: 'materialName',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '9%',
          align: 'center'
        },
        {
          title: '规格型号',
          dataIndex: 'jModel',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '15%',
          align: 'center'
        },
        {
          title: '计量单位',
          dataIndex: 'orderUnit',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '6%',
          align: 'center'
        },
        ...(!IS_WZG ? [{

          title: '应发数量',
          dataIndex: 'quantity',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '6%',
          align: 'center'
        }]
          : []),
        ...(!IS_TS_ENV ? [{
          title: '实发数量',
          dataIndex: 'actReqNum',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '6%',
          align: 'center'
        }]
          : []),
        {
          title: '单价',
          dataIndex: 'unitCost',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '7%',
          align: 'center'
        },
        ...(IS_TS_ENV ? [
          {
            title: '\xa0\xa0\xa0\xa0金\xa0\xa0\xa0\xa0额\xa0\xa0\xa0\xa0',
            dataIndex: 'lineCost',
            customRender: (text, row, index) => {
              return {
                children: text
              }
            },
            width: '7%',
            align: 'right'
          }]
          : [{
            title: '\xa0\xa0金\xa0\xa0\xa0\xa0额\xa0\xa0',
            dataIndex: 'lineCost',
            customRender: (text, row, index) => {
              return {
                children: text
              }
            },
            width: '7%',
            align: 'center'
          }]),
        ...(!(IS_WZG || IS_MD || IS_TCWF_ENV) ? [{
          title: '使用方向',
          dataIndex: 'usedFor',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '25%',
          align: 'center'

        }]
          : []),
        ...(IS_TS_ENV ? [{
          title: '移库仓库',
          dataIndex: 'ejkTsRemark',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '8%',
          align: 'center'
        }]
          : []),
        ...((IS_WZG || IS_TCWF_ENV) ? [{
          title: '使用方向',
          dataIndex: 'usedFor',
          customRender: (text, row, index) => {
            return {
              children: text
            }
          },
          width: '10%',
          align: 'center'

        }]
          : [])
      ],
      // 打印配置
      excelData: '',
      printDate: ''
    }
  },
  computed: {
    list () {
      return this.toSort(this.excelData)
    }
  },
  methods: {
    toSort (data) {
      const group = (IS_WZG ? 7 : (IS_JYS ? 30 : 5))
      const cache = {}
      for (const item of data) {
        item.oldlist = item.maplist || []
        item.totalCurrency = 0
        item.maplist = []
        if (!cache[item.prlineNum]) {
          cache[item.prlineNum] = {
            index: 1,
            temp: 1
          }
        }
        for (let i = 0; i < group; i++) {
          item.maplist.push({
            ...(item.oldlist[i] || {}),
            index: item.oldlist[i] ? cache[item.prlineNum].index++ : 'temp_' + cache[item.prlineNum].temp++
          })

          item.totalCurrency += (item.maplist[i] || {}).lineCost || 0
          item.totalCurrency = +item.totalCurrency.toFixed(8) || 0
          for (const i of item.maplist) {
            if (i.lineCost) {
              i.lineCost = (+i.lineCost || 0).toFixed(2)
            }
          }
        }
      }
      return data
    },
    toHandle (data) {
      let index = 0
      const list = []
      const maps = {}
      const cache = {}
      const array = data.maplist || []

      // 按采购计划分类
      for (const item of array) {
        if (!maps[item.prlineNum]) {
          maps[item.prlineNum] = {
            prlineNum: item.prlineNum,
            lotNum: data.lotNum,
            orgName: data.orgName,
            deptName: data.deptName,
            createDate: data.createDate,
            printDate: data.printDate,
            createName: data.createName,
            remarkTS: data.remarkTS,
            totalCurrency: data.totalCurrency,
            totalCount: data.totalCount,
            maplist: []
          }
        }
        maps[item.prlineNum].maplist.push({ ...item })
      }

      // 按物资数量排版
      for (const key in maps) {
        const item = maps[key]
        const array = item.maplist
        const length = array.length

        let count = length > (IS_WZG ? 7 : (IS_JYS ? 30 : 5)) ? length : (IS_WZG ? 7 : (IS_JYS ? 30 : 5))

        while (count % (IS_WZG ? 7 : (IS_JYS ? 30 : 5))) {
          count++
        }

        // 拆分页面
        let xh = 0
        let temp = 0
        for (let i = 0; i < count; i++) {
          const floor = Math.floor(i / (IS_WZG ? 7 : (IS_JYS ? 30 : 5)))
          const code = key + '-' + floor
          if (cache[code] === undefined) {
            cache[code] = index++
          }
          if (!list[cache[code]]) {
            list[cache[code]] = {
              prlineNum: item.prlineNum,
              lotNum: data.lotNum,
              orgName: item.orgName,
              deptName: item.deptName,
              createDate: item.createDate,
              printDate: item.printDate,
              createName: item.createName,
              remarkTS: item.remarkTS,
              totalCurrency: 0,
              maplist: []
            }
          }

          list[cache[code]].maplist.push({
            ...(item.maplist[i] || {}),
            index: item.maplist[i] ? ++xh : 'temp_' + ++temp
          })

          list[cache[code]].totalCurrency += (item.maplist[i] || {}).lineCost || 0
          list[cache[code]].totalCurrency = +list[cache[code]].totalCurrency.toFixed(8) || 0

          // 新增合计功能
          // if (i % 5 === 4) {
          //   list[cache[code]].maplist.push({
          //     materialNum: '合 计:',
          //     lineCost: list[cache[code]].totalCurrency,
          //     index: 'total_index'
          //   })
          // }
        }
      }
      return list
    },
    toFormat (date, format) {
      function itType (val) {
        return Object.prototype.toString.call(val).replace(/^\[[^\s\]]+\s*([^\s\]]+)]$/, '$1')
      }
      function isDate (date) {
        if (itType(date) === 'Date') {
          return true
        }
        return false
      }
      function isString (str) {
        if (itType(str) === 'String') {
          return true
        }
        return false
      }
      if (isDate(date, true)) {
        const handle = function (i) {
          return (i < 10 ? '0' : '') + i
        }
        if (!isString(format, true)) {
          format = 'yyyy/MM/dd HH:mm:ss'
        }
        return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (f) {
          switch (f) {
            case 'yyyy': {
              return handle(date.getFullYear())
            }
            case 'MM': {
              return handle(date.getMonth() + 1)
            }
            case 'dd': {
              return handle(date.getDate())
            }
            case 'HH': {
              return handle(date.getHours())
            }
            case 'mm': {
              return handle(date.getMinutes())
            }
            case 'ss': {
              return handle(date.getSeconds())
            }
          }
        })
      }
      return ''
    },
    toPrint (data = [], media = {}) {
      this.excelData = data
      this.printDate = this.toFormat(new Date(), 'yyyy年MM月dd日')
      this.$nextTick(() => {
        switch (USER_ORG_ID) {
          case '1.100.118': {
            this.$refs.printArea.doPrint(
              Object.assign(
                {
                  size: '381mm 139.7mm',
                  margin: '2cm 3cm .5cm'
                },
                media
              )
            )
            break
          }
          case '1.100.101': {
            this.$refs.printArea.doPrint(
              Object.assign(
                {
                  size: '381mm 139.7mm',
                  margin: '2cm 3cm .5cm'
                },
                media
              )
            )
            break
          }
          case '1.100.117': {
            this.$refs.printArea.doPrint(
              Object.assign(
                {
                  size: '210mm 297mm',
                  margin: '1cm .6cm .3cm .6cm'
                },
                media
              )
            )
            break
          }
          default: {
            this.$refs.printArea.doPrint(
              Object.assign(
                {
                  size: '241mm 139.7mm',
                  margin: '1.5cm 1.2cm .5cm .5cm'
                },
                media
              )
            )
          }
        }
      })
      if (IS_WZG) {
        this.$nextTick(() => {
          this.$refs.printArea.doPrint(
            Object.assign(
              {
                size: '241mm 139.7mm',
                margin: '0.8cm 0.8cm 0.2cm 0.8cm'
              },
              media
            )
          )
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.excel-container {
  width: 100%;
  & > .excel-item {
    width: 100%;
    & > .excel-group {
      width: 100%;
      position: relative;
      &:not(:last-child) {
        &::after {
          content: '';
          width: calc(100% + 10pt);
          height: 2pt;
          border-bottom: dashed 1pt #000;
          position: absolute;
          bottom: -10pt;
          left: -5pt;
        }
      }
      & > .excel-header {
        width: 100%;
        & > .mainTitle {
          color: #000;
          font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
          text-align: center;
          & > .orgName {
            font-size: 16pt;
            font-weight: 500;
          }
          & > .textName {
            font-size: 14pt;
            font-weight: 500;
          }
        }
        & > .subTitle {
          height: 16pt;
          line-height: 16pt;
          padding: 0 50pt 0 20pt;
          color: #000;
          font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
          font-size: 9pt;
          font-weight: 400;
          display: flex;
          justify-content: space-between;
        }
      }
      & > .excel-content {
        width: 100%;
        display: flex;
        & > .table {
          width: 100%;
        }
      }
      & > .excel-footer {
        width: 100%;
        height: 22pt;
        line-height: 22pt;
        padding: 0 30pt;
        color: #000;
        font-family: Arial, Microsoft YaHei, SimHei, SimSun, SimHei, serif;
        font-size: 9pt;
        font-weight: 400;
        display: flex;
        justify-content: space-between;
      }
    }
  }
}
</style>
