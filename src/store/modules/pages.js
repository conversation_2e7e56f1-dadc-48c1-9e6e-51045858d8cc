import { queryPersonalTableRecord, queryPersonalCockpitRecord } from '@/api/material/table'
import { requestBuilder } from '@/utils/util'

const MARK_TODO_LIST = 'TodoList'
const REGEX_TODO_CARD = /^TodoCard\w/
const REGEX_TODO_REPORT = /^TodoReport\w/
const REGEX_TODO_KPI_VIEW = /^TodoKpiView\w/

const pages = {
  state: {
    todoCardGroup: [],
    todoReportGroup: [],
    todoKpiViewGroup: [],
    todoTableGroup: [],
    todoCockpitGroup: []
  },

  getters: {
    todoCardGroup: state => state.todoCardGroup,
    todoReportGroup: state => state.todoReportGroup,
    todoKpiViewGroup: state => state.todoKpiViewGroup,
    todoTableGroup: state => state.todoTableGroup,
    todoCockpitGroup: state => state.todoCockpitGroup
  },

  mutations: {
    SET_TODO_CARD_GROUP: (state, cards) => {
      state.todoCardGroup = [...cards]
    },
    SET_TODO_REPORT_GROUP: (state, reports) => {
      state.todoReportGroup = [...reports]
    },
    SET_TODO_KPI_VIEW_GROUP: (state, kpiViews) => {
      state.todoKpiViewGroup = [...kpiViews]
    },
    SET_TODO_TABLE_GROUP: (state, tables) => {
      state.todoTableGroup = [...tables]
    },
    SET_TODO_COCKPIT_GROUP: (state, group) => {
      state.todoCockpitGroup = [...group]
    }
  },

  actions: {
    SetTodoTableGroup ({ commit }, role) {
      queryPersonalTableRecord(requestBuilder('', {})).then(res => {
        commit('SET_TODO_TABLE_GROUP', res.result)
      })
      // commit('SET_TODO_TABLE_GROUP', [])
    },
    SettodoCockpitGroup ({ commit }, role) {
      queryPersonalCockpitRecord(requestBuilder('', {})).then(res => {
        commit('SET_TODO_COCKPIT_GROUP', res.result)
      })
      // commit('SET_TODO_TABLE_GROUP', [])
    },
    SetTodoGroup ({ commit }, role) {
      const todoCardGroup = []
      const todoReportGroup = []
      const todoKpiViewGroup = []
      const { permissions = [] } = role
      for (const item of permissions) {
        if (MARK_TODO_LIST === item.permissionId && item.actionEntitySet) {
          for (const entitySet of item.actionEntitySet) {
            if (REGEX_TODO_CARD.test(entitySet.action)) {
              todoCardGroup.push({
                name: entitySet.action,
                describe: entitySet.describe
              })
            }
            if (REGEX_TODO_REPORT.test(entitySet.action)) {
              todoReportGroup.push({
                name: entitySet.action,
                describe: entitySet.describe
              })
            }
            if (REGEX_TODO_KPI_VIEW.test(entitySet.action)) {
              todoKpiViewGroup.push({
                name: entitySet.action,
                describe: entitySet.describe
              })
            }
          }
        }
      }
      commit('SET_TODO_CARD_GROUP', todoCardGroup)
      commit('SET_TODO_REPORT_GROUP', todoReportGroup)
      commit('SET_TODO_KPI_VIEW_GROUP', todoKpiViewGroup)
    }
  }
}

export default pages
