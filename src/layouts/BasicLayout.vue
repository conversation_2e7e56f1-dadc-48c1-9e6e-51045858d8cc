<template>
  <a-layout :class="['layout', device]">
    <!-- 侧边栏 -->
    <a-drawer
      v-if="isMobile()"
      :width="256"
      placement="left"
      :closable="false"
      :wrapClassName="`drawer-sider ${navTheme}`"
      :visible="collapsed"
      @close="close"
    >
      <side-menu
        mode="inline"
        :menus="menus"
        :theme="navTheme"
        :collapsed="false"
        :collapsible="true"
      />
    </a-drawer>

    <side-menu
      v-else-if="isSideMenu()"
      mode="inline"
      :menus="menus"
      :theme="navTheme"
      :collapsed="collapsed"
      :collapsible="true"
      :device="device"
      @toggle="toggle"
    />

    <a-layout
      :class="[layoutMode, `content-width-${contentWidth}`]"
      :style="{ paddingLeft: contentPaddingLeft, minHeight: '100vh' }"
    >
      <!-- 头部布局 -->
      <global-header
        :mode="layoutMode"
        :menus="menus"
        :theme="navTheme"
        :isMobile="isMobile"
        :collapsed="collapsed"
        :device="device"
        @toggle="toggle"
      />

      <!-- 内容主体 -->
      <a-layout-content
        :style="{
          height: '100%',
          margin: '20px 18px 0px',
          paddingTop: fixedHeader ? '64px' : '0',
          flexDirection: 'column',
          display: 'flex'
        }"
        class="layout-content-main"
      >
        <multi-tab
          v-if="multiTab"
          style="padding-top: 5px; flex: 0 0 auto;"
        />
        <transition name="page-transition">
          <keep-alive>
            <router-view />
          </keep-alive>
        </transition>
      </a-layout-content>

      <!-- 底部布局 -->
      <a-layout-footer>
        <global-footer />
      </a-layout-footer>

      <!-- 样式设置(必要时打开) -->
      <setting-drawer v-if="isPreview" />
    </a-layout>
  </a-layout>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { triggerWindowResizeEvent } from '@/utils/util'
import { mixin, mixinDevice } from '@/utils/mixin'

import SideMenu from '@/components/Menu/SideMenu'
import GlobalHeader from '@/components/GlobalHeader'
import GlobalFooter from '@/components/GlobalFooter'
import SettingDrawer from '@/components/SettingDrawer'

export default {
  name: 'BasicLayout',
  mixins: [mixin, mixinDevice],
  components: {
    SideMenu,
    GlobalHeader,
    GlobalFooter,
    SettingDrawer
  },
  data () {
    return {
      isPreview: process.env.VUE_APP_PREVIEW === true,
      collapsed: true,
      menus: []
    }
  },
  computed: {
    contentPaddingLeft () {
      if (!this.fixSiderbar || this.isMobile()) {
        return '0'
      }
      if (this.sidebarOpened) {
        return '256px'
      }
      return '60px'
    },
    ...mapGetters(['multiTab', 'keepAlive', 'addRouters'])
  },
  watch: {
    sidebarOpened (val) {
      this.collapsed = !val
    }
  },
  created () {
    this.collapsed = !this.sidebarOpened
    this.menus = this.addRouters.find(item => item.path === '/').children
    this.cache()
  },
  mounted () {
    const userAgent = navigator.userAgent
    if (userAgent.indexOf('Edge') > -1) {
      this.$nextTick(() => {
        this.collapsed = !this.collapsed
        setTimeout(() => {
          this.collapsed = !this.collapsed
        }, 16)
      })
    }
  },
  methods: {
    close () {
      this.collapsed = false
    },
    toggle () {
      this.collapsed = !this.collapsed
      this.setSidebar(!this.collapsed)
      triggerWindowResizeEvent()
    },
    cache () {
      const findChild = (menus, cached) => {
        if (Array.isArray(menus)) {
          for (const menu of menus) {
            cached.push({ ...menu })
            findChild(menu.children, cached)
          }
        }
        return cached
      }
      const find = item => item.path === '/'
      const router = this.addRouters.find(find)
      this.AddCachedTags(findChild(router.children, []))
    },
    ...mapActions(['setSidebar', 'AddCachedTags'])
  }
}
</script>

<style lang="less">
/*
 * The following styles are auto-applied to elements with
 * transition="page-transition" when their visibility is toggled
 * by Vue.js.
 *
 * You can easily play with the page transition by editing
 * these styles.
 */
.page-transition-enter {
  opacity: 0;
}

.page-transition-leave-active {
  opacity: 0;
}

.page-transition-enter .page-transition-container,
.page-transition-leave-active .page-transition-container {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

/*
 * side-menu collapsed
 */
.ant-menu-inline-collapsed {
  width: 60px;
  & > .ant-menu-item,
  & > .ant-menu-submenu > .ant-menu-submenu-title,
  & > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item,
  & > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-submenu > .ant-menu-submenu-title {
    left: 0;
    text-align: center;
    padding: 0 10px !important;
    text-overflow: clip;
  }
}
</style>
