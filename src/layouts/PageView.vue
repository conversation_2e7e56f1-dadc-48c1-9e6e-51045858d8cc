<template>
  <div style="margin: -18px -18px 0; display: flex; flex-direction: column; flex: 1 0 auto;">
    <page-header
      v-if="!$route.meta.hiddenHeaderContent"
      :title="pageTitle"
      :avatar="avatar"
      :logo="logo"
    >
      <slot
        slot="action"
        name="action"
      />
      <slot
        slot="content"
        name="headerContent"
      />
      <div
        slot="content"
        v-if="!this.$slots.headerContent && description"
      >
        <p style="font-size: 14px;color: rgba(0,0,0,.65)">{{ description }}</p>
        <div class="link">
          <template v-for="(link, index) in linkList">
            <a
              :key="index"
              @click="() => { link.callback && link.callback() }"
            >
              <a-icon :type="link.icon" />
              <span>{{ link.title }}</span>
            </a>
          </template>
        </div>
      </div>
      <slot
        slot="extra"
        name="extra"
      >
        <div class="extra-img">
          <img
            v-if="typeof extraImage !== 'undefined'"
            :src="extraImage"
          >
        </div>
      </slot>
      <div slot="pageMenu">
        <div
          class="page-menu-search"
          v-if="search"
        >
          <a-input-search
            style="width: 80%; max-width: 522px;"
            placeholder="请输入..."
            size="large"
            enterButton="搜索"
          />
        </div>
        <div
          class="page-menu-tabs"
          v-if="tabs && tabs.items"
        >
          <!-- @change="callback" :activeKey="activeKey" -->
          <a-tabs
            :tabBarStyle="{margin: 0}"
            :activeKey="tabs.active()"
            @change="tabs.callback"
          >
            <a-tab-pane
              v-for="item in tabs.items"
              :tab="item.title"
              :key="item.key"
            />
          </a-tabs>
        </div>
      </div>
    </page-header>
    <div
      class="content"
      style="width: 100%; display: flex; flex-direction: column; flex: 1 0 auto;"
    >
      <div
        class="page-header-index-wide"
        style="display: flex; flex-direction: column; flex: 1 0 auto; position: relative;"
      >
        <slot>
          <!-- keep-alive  -->
          <keep-alive
            v-if="multiTab && keepAlive"
            :include="cachedTags"
          >
            <router-view
              ref="content"
              :key="key"
            />
          </keep-alive>
          <router-view
            v-else
            ref="content"
          />
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
import PageHeader from '@/components/PageHeader'
import { ExternalLink } from '@/utils/mixin'
import { mapState } from 'vuex'

export default {
  name: 'PageView',
  mixins: [ExternalLink],
  components: {
    PageHeader
  },
  props: {
    avatar: {
      type: String,
      default: null
    },
    title: {
      type: [String, Boolean],
      default: true
    },
    logo: {
      type: String,
      default: null
    },
    directTabs: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      extraImage: '',
      description: null,
      pageTitle: null,
      search: false,
      linkList: [],
      tabs: {}
    }
  },
  computed: {
    key () {
      const route = this.$route
      const meta = route.meta || {}
      const match = meta.match || 'path'
      const isExternal = match === 'external'
      const external = this.getExternal(route)
      return !isExternal ? route.fullPath : external
    },
    ...mapState({
      multiTab: state => state.app.multiTab,
      keepAlive: state => state.app.keepAlive,
      cachedTags: state => state.tags.cachedTags
    })
  },
  mounted () {
    this.tabs = this.directTabs
    this.getPageMeta()
  },
  updated () {
    this.getPageMeta()
  },
  methods: {
    // 获取路由meta
    getPageMeta () {
      const content = this.$refs.content
      if (content) {
        if (content.pageMeta) {
          Object.assign(this, content.pageMeta)
        } else {
          this.description = content.description
          this.linkList = content.linkList
          this.extraImage = content.extraImage
          this.search = content.search === true
          this.tabs = content.tabs
        }
      }
      this.pageTitle = (typeof this.title === 'string' && this.title) || this.$route.meta.title
    }
  }
}
</script>

<style lang="less" scoped>
.content {
  margin: 0px 0px 0;
  .link {
    margin-top: 16px;
    &:not(:empty) {
      margin-bottom: 16px;
    }
    a {
      margin-right: 32px;
      height: 24px;
      line-height: 24px;
      display: inline-block;
      i {
        font-size: 24px;
        margin-right: 8px;
        vertical-align: middle;
      }
      span {
        height: 24px;
        line-height: 24px;
        display: inline-block;
        vertical-align: middle;
      }
    }
  }
}

.page-menu-search {
  text-align: center;
  margin-bottom: 16px;
}
.page-menu-tabs {
  margin-top: 48px;
}

.extra-img {
  margin-top: -60px;
  text-align: center;
  width: 195px;

  img {
    width: 100%;
  }
}

.mobile {
  .extra-img {
    margin-top: 0;
    text-align: center;
    width: 96px;

    img {
      width: 100%;
    }
  }
}
</style>
