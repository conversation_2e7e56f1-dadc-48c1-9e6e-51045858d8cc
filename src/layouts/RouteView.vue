
<script>
import { ExternalLink } from '@/utils/mixin'

export default {
  name: 'RouteView',
  mixins: [ExternalLink],
  computed: {
    key () {
      const route = this.$route
      const meta = route.meta || {}
      const match = meta.match || 'path'
      const isExternal = match === 'external'
      const external = this.getExternal(route)
      return !isExternal ? route.fullPath : external
    }
  },
  render () {
    const { key } = this
    const { getters } = this.$store
    const notKeep = <router-view />
    const inKeep = (
      <keep-alive include={getters.cachedTags}>
        <router-view key={key} />
      </keep-alive>
    )
    if (getters.multiTab && getters.keepAlive) {
      return inKeep
    }
    return notKeep
  }
}
</script>
