import {
  BasicLayout,
  UserLayout,
  PageView,
  PageFrame
} from '@/layouts'

export const rootRouter = {
  key: 'Basic',
  name: 'Basic',
  path: '/',
  redirect: '/dashboard',
  component: BasicLayout,
  meta: { title: '首页' }
}

export const externalRouter = {
  key: 'External',
  name: 'External',
  path: '/external',
  redirect: '/external/link',
  component: PageView,
  children: [
    {
      key: 'ExternalLink',
      name: 'ExternalLink',
      path: '/external/link',
      component: PageFrame,
      meta: {
        title: '外部链接',
        match: 'external',
        external: '',
        componentName: 'ExternalLink',
        hiddenHeaderContent: true,
        noCache: false
      }
    }
  ],
  meta: {
    title: '外部链接',
    componentName: 'External',
    hiddenHeaderContent: true,
    noCache: false
  },
  hidden: true
}

export const notFoundRouter = [
  {
    path: '/403',
    name: '403',
    component: () => import(`@/views/system/error/403`),
    hidden: true
  },
  {
    path: '/404',
    name: '404',
    component: () => import(`@/views/system/error/404`),
    hidden: true
  },
  {
    path: '/500',
    name: '500',
    component: () => import(`@/views/system/error/500`),
    hidden: true
  },
  {
    path: '*',
    name: '*',
    redirect: '/404',
    hidden: true
  }
]

export const constantRouterMap = [
  {
    path: '/user',
    component: UserLayout,
    redirect: '/user/login',
    hidden: false,
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import(`@/views/system/user/Login`)
      },
      {
        path: 'loginSys',
        name: 'loginSys',
        component: () => import(`@/views/system/user/LoginSys`)
      }
    ]
  }
]
