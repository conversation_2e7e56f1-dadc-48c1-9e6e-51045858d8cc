import { mapGetters } from 'vuex'

const tableMixin = {
  computed: {
    ...mapGetters(['todoTableGroup'])
  },
  methods: {
    initTable (columns, tableId) {
      const objectTable = this.todoTableGroup.find(item => item.tableId === tableId)
      const tableList = objectTable ? objectTable.tableRecordList : []
      this[columns].forEach((item, indexco) => {
        const obj = tableList.find(info => info.dataIndex === item.dataIndex)
        if (obj) {
          item.check = !!obj.check
          item.fixed = obj.fixed ? obj.fixed : false
          item.index = obj.index
          item.sorter = !!obj.sorter
          item.ellipsis = !!obj.ellipsis
          item.title = obj.title || item.title
          item.width = obj.width || item.width || 120
          item.disabled = item.dataIndex === 'action' || item.key === 'action'
        } else {
          item.check = true
          item.edit = false
          item.ellipsis = !!item.ellipsis
          item.sorter = !!item.sorter
          item.index = indexco
          item.disabled = item.dataIndex === 'action' || item.key === 'action'
        }
      })
      this[columns].sort((a, b) => a.index - b.index)
    }
  }
}
export { tableMixin }
