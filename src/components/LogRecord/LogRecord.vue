<template>
  <a-modal
    title="审计"
    :visible="visible"
    :footer="null"
    width="50vw"
    :maskClosable="false"
    :centered="true"
    :destroyOnClose="true"
    @cancel="handleCancel"
  >
    <LogRecordTable ref="LogRecordTable" />
    <div v-if="hasFile">
      <label class="title">附件：</label>
      <FileLogRecordTable ref="FileLogRecordTable" />
    </div>
  </a-modal>
</template>
<script>
import LogRecordTable from './LogRecordTable'
import FileLogRecordTable from './FileLogRecordTable'
export default {
  name: 'LogRecord',
  components: {
    LogRecordTable, FileLogRecordTable
  },
  data () {
    return {
      visible: false,
      hasFile: false
    }
  },
  methods: {
    showModal (params) {
      this.visible = true
      this.hasFile = params.hasFile || false
      this.$nextTick(() => {
        if (this.$refs.LogRecordTable) {
          this.$refs.LogRecordTable.show(params)
        }
        if (this.hasFile && this.$refs.FileLogRecordTable) {
          this.$refs.FileLogRecordTable.show({
            tableName: 'file',
            keyValue: params.fileKeyValue
          })
        }
      })
    },
    handleCancel (e) {
      this.visible = false
      this.$refs.LogRecordTable.clear()
      if (this.$refs.FileLogRecordTable) {
        this.$refs.FileLogRecordTable.clear()
      }
    }
  }
}
</script>
<style lang="less" scoped>
.title {
  font-size: 15px;
    line-height: 3;
    font-weight: 600;
}
 ::v-deep {
   .selected-row{
      td {
        background: #f0f4ff !important;
      }
    }
    .row {
      td {
        height: auto !important;
      }
    }
 }
</style>
