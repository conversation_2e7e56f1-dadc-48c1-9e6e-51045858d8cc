@import './index.less';

html,
body {
  height: 100%;
  ::-webkit-scrollbar-track-piece {
    border-radius: 16px;
    background: rgba(211, 220, 230, 0.3);
  }
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  ::-webkit-scrollbar-thumb {
    border-radius: 16px;
    background: rgba(153, 169, 191, 0.6);
  }
}

body {
  &.colorWeak {
    filter: invert(80%);
  }
  &.userLayout {
    overflow: auto;
  }
}

.ant-table-column-title {
  font-weight: 700;
}

// 布局样式
.resize-table-th {
  position: relative;
  .ant-table-header-column {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .table-draggable-handle {
    position: absolute !important;
    transform: translate(-50%, -50%) !important;
    width: 10px !important;
    height: 100% !important;
    right: -5px;
    cursor: col-resize;
    touch-action: none;
    border: none;
  }
}
.layout.ant-layout {
  overflow-x: hidden;
  height: auto;

  &.mobile .ant-layout-content,
  &.tablet .ant-layout-content {
    display: flex;
    flex-flow: column;
  }

  &.mobile .ant-table-wrapper,
  &.tablet .ant-table-wrapper {
    .ant-table-content {
      overflow-y: auto;
    }
  }

  &.ant-layout-has-sider {
    flex-direction: row;
  }

  // ant-layout-sider 样式
  .ant-layout-sider {
    &.ant-layout-sider-light {
      background: #fff;
    }
    &.ant-layout-sider-dark {
      background: #183a78;
      box-shadow: none;
    }
  }

  // trigger 样式
  .trigger {
    padding: 0 20px;
    cursor: pointer;
    transition: color 0.3s;
    font-size: 20px;
    line-height: 64px;
    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  // topmenu 样式
  .topmenu {
    .ant-header-fixedHeader {
      position: fixed;
      z-index: 9;
      top: 0;
      right: 0;
      width: 100%;
      transition: width 0.2s;
      &.ant-header-side-opened {
        width: 100%;
      }
      &.ant-header-side-closed {
        width: 100%;
      }
    }
    .page-header-index-wide {
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
    }
    // 流式布局
    &.content-width-Fluid {
      .header-index-wide {
        max-width: unset;
        .header-index-left {
          flex: 1 1 1000px;
          .logo {
            margin-left: 25px;
          }
          .ant-menu.ant-menu-horizontal {
            flex: 1 1 calc(100vw - 190px - 238px - 25px);
            max-width: calc(100vw - 190px - 238px - 25px);
          }
        }
        .header-index-right {
          margin-right: 25px;
        }
      }
      .page-header-index-wide {
        max-width: unset;
      }
    }
  }
  &.mobile .topmenu,
  &.tablet .topmenu {
    &.content-width-Fluid {
      .header-index-wide {
        margin-left: 0;
      }
    }
  }

  // sidemenu 样式
  .sidemenu {
    .ant-header-fixedHeader {
      position: fixed;
      z-index: 9;
      top: 0;
      right: 0;
      width: 100%;
      transition: width 0.2s;
      &.ant-header-side-opened {
        width: calc(100% - 256px);
      }
      &.ant-header-side-closed {
        width: calc(100% - 60px);
      }
    }
  }
  &.mobile .sidemenu {
    .ant-header-fixedHeader {
      &.ant-header-side-opened,
      &.ant-header-side-closed {
        width: 100%;
      }
    }
  }

  // 顶部区域 样式
  .header {
    position: relative;
    height: 64px;
    padding: 0 12px 0 0;
    background: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    .user-wrapper {
      float: right;
      height: 100%;
      .action {
        display: inline-block;
        height: 100%;
        padding: 0 12px;
        cursor: pointer;
        transition: all 0.3s;
        color: rgba(0, 0, 0, 0.65);
        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
        .avatar {
          margin: 20px 8px 20px 0;
          vertical-align: middle;
          color: #1890ff;
          background: hsla(0, 0%, 100%, 0.85);
        }
        .icon {
          padding: 4px;
          font-size: 16px;
        }
      }
    }
  }
  .top-nav-header-index {
    position: relative;
    transition: background 0.3s, width 0.2s;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    .user-wrapper {
      float: right;
      height: 100%;
      .action {
        display: inline-block;
        height: 100%;
        padding: 0 12px;
        cursor: pointer;
        transition: all 0.3s;

        color: rgba(0, 0, 0, 0.65);
        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
        .avatar {
          margin: 20px 8px 20px 0;
          vertical-align: middle;
          color: #1890ff;
          background: hsla(0, 0%, 100%, 0.85);
        }
        .icon {
          padding: 4px;
          font-size: 16px;
        }
      }
    }
    .header-index-wide {
      display: flex;
      width: 100%;
      max-width: 1200px;
      height: 64px;
      margin: auto;
      padding-left: 0;
      .ant-menu.ant-menu-horizontal {
        flex: 0 1 835px;
        max-width: 835px;
        height: 64px;
        border: none;
        line-height: 64px;
      }
      .header-index-left {
        display: flex;
        flex: 0 1 1000px;
        .logo.top-nav-header {
          position: relative;
          overflow: hidden;
          flex: 0 0 265px;
          width: 265px;
          height: 64px;
          transition: all 0.3s;
          line-height: 64px;
          img,
          svg {
            display: inline-block;
            width: auto;
            height: auto;
          }
          h1 {
            display: inline-block;
            margin: 0 0 0 12px;
            vertical-align: top;
            color: #fff;
            font-size: 16px;
            font-weight: 400;
          }
        }
      }
      .header-index-right {
        overflow: hidden;
        align-self: flex-end;
        flex: 0 0 238px;
        height: 64px;
        .content-box {
          float: right;
          .action {
            overflow: hidden;
            max-width: 140px;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
    }
    &.dark {
      background-color: rgb(2, 64, 152);
      .user-wrapper {
        .action {
          color: rgba(255, 255, 255, 0.85);
          a {
            color: rgba(255, 255, 255, 0.85);
          }
          &:hover {
            background-color: rgb(2, 64, 152);
          }
        }
      }
    }
    &.light {
      background-color: #fff;
      .header-index-wide {
        .header-index-left {
          .logo {
            h1 {
              color: #002140;
            }
          }
        }
      }
    }
  }
  &.mobile .top-nav-header-index {
    .header-index-wide {
      .header-index-left {
        .trigger {
          padding: 0 12px;
          color: rgba(255, 255, 255, 0.85);
        }
        .logo.top-nav-header {
          flex: 0 0 120px;
          text-align: center;
          line-height: 58px;
          h1 {
            display: none;
          }
        }
      }
    }
    &.light {
      .header-index-wide {
        .header-index-left {
          .trigger {
            color: rgba(0, 0, 0, 0.65);
          }
        }
      }
    }
  }
  &.tablet .top-nav-header-index {
    .header-index-wide {
      .header-index-left {
        .ant-menu.ant-menu-horizontal {
          flex: 1 1 auto;
          white-space: normal;
        }
        .trigger {
          padding: 0 12px;
          color: rgba(255, 255, 255, 0.85);
        }
        .logo {
          & > a {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          &.top-nav-header {
            flex: 0 0 120px;
            text-align: center;
            line-height: 58px;
            h1 {
              display: none;
            }
          }
        }
      }
    }
    &.light {
      .header-index-wide {
        .header-index-left {
          .trigger {
            color: rgba(0, 0, 0, 0.65);
          }
        }
      }
    }
  }

  // 内容区域 样式
  .layout-content {
    height: 100%;
    height: 64px;
    margin: 24px 24px 0;
    padding: 0 12px 0 0;
  }

  // 底部区域 样式
  .ant-layout-footer {
    padding: 0;
  }
}

// 抽屉自定义
.ant-drawer.drawer-sider {
  .ant-layout-sider {
    box-shadow: none;
    &.ant-layout-sider-light {
      background: #fff;
    }
    &.ant-layout-sider-dark {
      background: #183a78;
    }
  }
  .ant-drawer-body {
    padding: 0;
  }
  &.dark {
    .ant-drawer-content {
      background-color: rgb(24, 58, 120);
    }
  }
  &.light {
    box-shadow: none;
    .ant-drawer-content {
      background-color: #fff;
    }
  }
}

// 菜单样式
.sider {
  position: relative;
  z-index: @ant-global-sider-zindex;
  min-height: 100vh;
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  .ant-layout-sider-children {
    overflow-y: hidden;
    &:hover {
      overflow-y: auto;
    }
  }
  &.ant-fixed-sidemenu {
    position: fixed;
    height: 100%;
  }
  .logo {
    position: relative;
    overflow: hidden;
    height: 64px;
    transition: all 0.3s;
    background: #002140;
    line-height: 64px;

    img,
    svg {
      display: inline-block;
      width: auto;
      height: auto;
    }

    h1 {
      display: inline-block;
      margin: 0 0 0 12px;
      vertical-align: middle;
      color: #fff;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      font-size: 20px;
      font-weight: 600;
    }
  }
  &.light {
    background-color: #fff;
    box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
    .logo {
      background: #fff;
      box-shadow: 1px 1px 0 0 #e8e8e8;

      h1 {
        color: unset;
      }
    }
    .ant-menu-light {
      border-right-color: transparent;
    }
  }
  &.dark {
    .logo {
      background-color: #024098;
    }
    .logo > .trigger {
      color: #fff;
    }
  }
}

// 菜单元素 样式
.ant-menu {
  &.ant-menu-dark.ant-menu-inline,
  &.ant-menu-dark.ant-menu-vertical,
  &.ant-menu-dark.ant-menu-vertical-left,
  &.ant-menu-dark.ant-menu-vertical-right {
    border-right: 0;
  }
  &.ant-menu-dark .ant-menu-inline {
    &.ant-menu-sub {
      box-shadow: none;
    }
  }
  &.ant-menu-dark .ant-menu-submenu {
    &.ant-menu-submenu-inline,
    &.ant-menu-submenu-vertical,
    &.ant-menu-submenu-vertical-left,
    &.ant-menu-submenu-vertical-right {
      & > .ant-menu-submenu-title {
        height: 40px;
        line-height: 44px;
        .ant-menu-submenu-arrow {
          top: calc(50% + 3px);
          opacity: 1;
          &::before {
            background: #fff;
          }
          &::after {
            background: #fff;
          }
        }
      }
    }
  }
  &.ant-menu-dark,
  &.ant-menu-dark .ant-menu-sub {
    color: #fff;
    background: rgb(24, 58, 120);
    & > .ant-menu-item:hover,
    & > .ant-menu-item-active,
    & > .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
    & > .ant-menu-submenu-active,
    & > .ant-menu-submenu-title:hover {
      color: #fff;
    }
    & > .ant-menu-item > a {
      color: rgba(255, 255, 255, 0.5);
      &.router-link-active {
        color: rgba(255, 255, 255, 0.85);
      }
    }
  }
  // horizontal
  &.ant-menu-dark.ant-menu-horizontal,
  &.ant-menu-dark.ant-menu-horizontal .ant-menu-sub {
    color: rgba(255, 255, 255, 0.45);
    background-color: rgb(2, 64, 152);
    & > .ant-menu-item,
    & > .ant-menu-submenu {
      top: 0;
      margin-top: 0;
      border-bottom: 0;
    }
    & > .ant-menu-item:hover,
    & > .ant-menu-submenu:hover,
    & > .ant-menu-item-active,
    & > .ant-menu-submenu-active,
    & > .ant-menu-item-open,
    & > .ant-menu-submenu-open,
    & > .ant-menu-item-selected,
    & > .ant-menu-submenu-selected {
      top: 0;
      margin-top: 0;
      color: #fff;
      border-bottom: 0;
      & > .ant-menu-submenu-title {
        color: #fff;
      }
    }
  }
  // 仅layout中启用
  .layout.ant-layout > .ant-fixed-sidemenu & {
    &.ant-menu-dark,
    &.ant-menu-dark .ant-menu-sub {
      & > .ant-menu-item-selected,
      & > .ant-menu-submenu-selected {
        background: rgb(10, 41, 97);
        .ant-menu-sub {
          background: rgb(10, 41, 97);
        }
      }
      & > .ant-menu-submenu-selected {
        position: relative;
        &::before {
          position: absolute;
          top: 2px;
          left: 0;
          width: 2.5px;
          height: 40px;
          content: '';
          background-color: rgb(233, 56, 32);
        }
        &:not(.ant-menu-submenu-open) {
          &::after {
            position: absolute;
            top: 16.5px;
            right: 0;
            width: 0;
            height: 0;
            content: '';
            border: solid 5.6px #fff;
            border-top-color: transparent;
            border-bottom-color: transparent;
            border-left: none;
          }
        }
        &.ant-menu-submenu-open {
          & > .ant-menu-sub > .ant-menu-item-selected {
            position: relative;
            &::after {
              position: absolute;
              top: 14.5px;
              right: 0;
              width: 0;
              height: 0;
              content: '';
              border: solid 5.6px #fff;
              border-top-color: transparent;
              border-bottom-color: transparent;
              border-left: none;
            }
          }
        }
      }
    }
  }
}

// 下拉菜单
.ant-menu-submenu-popup {
  &.ant-menu-dark,
  &.ant-menu-dark .ant-menu-sub {
    color: rgba(255, 255, 255, 0.65);
    background: rgb(24, 58, 120);
  }
  &.ant-menu-dark .ant-menu-item-selected {
    background-color: #1890ff;
    & > a,
    & > a:hover {
      color: #fff;
    }
  }
}

// 外置的样式控制
.user-dropdown-menu {
  span {
    user-select: none;
  }
}
.user-dropdown-menu-wrapper.ant-dropdown-menu {
  padding: 4px 0;
  .ant-dropdown-menu-item {
    width: 160px;
  }
  .ant-dropdown-menu-item > .anticon:first-child,
  .ant-dropdown-menu-item > a > .anticon:first-child,
  .ant-dropdown-menu-submenu-title > .anticon:first-child .ant-dropdown-menu-submenu-title > a > .anticon:first-child {
    min-width: 12px;
    margin-right: 8px;
  }
}

// 数据列表样式
.table-alert {
  margin-bottom: 10px;
}
.table-page-search-wrapper {
  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 13px;
    white-space: nowrap;
  }
}

// 样式自定义 --- modal
.small-modal {
  .ant-modal {
    top: calc(50% - 255px);
  }
  .ant-modal-body {
    padding: 20px 25px 10px;
  }
  .ant-form-inline {
    .ant-form-item {
      display: flex;
      & > .ant-form-item-label {
        width: 55px;
      }
    }
  }
}

// 样式自定义 --- card样式
.ant-card {
  margin: 0 0 10px 0;
}
.ant-card-body {
  overflow: hidden;
  padding: 10px 8px 5px;
  border-radius: 3px;
}
.ant-tabs.ant-tabs-card {
  .ant-tabs-card-bar {
    .ant-tabs-nav-container {
      height: 35px;
    }
    .ant-tabs-tab,
    .ant-tabs-tab-active {
      height: 35px;
      margin-right: 5px;
      line-height: 35px;
    }
  }
}

// 样式自定义 --- 抽屉组件样式
.no-transform {
  .ant-drawer.ant-drawer-right.ant-drawer-open {
    transform: translateX(0) !important;
  }
}
.ant-drawer-header {
  position: absolute;
  z-index: 2;
  top: 0;
  left: 0;
  overflow: hidden;
  width: 100%;
  height: 55px;
}
.ant-drawer-body {
  width: 100%;
  padding: 65px 20px 10px;
}
.drawer-footer {
  width: 100%;
  height: 55px;
  .footer-fixed {
    position: absolute;
    z-index: 2;
    right: 0;
    bottom: 0;
    overflow: hidden;
    width: 100%;
    height: 55px;
    padding: 10px 16px;
    text-align: right;
    border-top: 1px solid #e9e9e9;
    background: #fff;
  }
}

// 样式自定义 --- 气泡弹出框
.ant-popover-message {
  display: flex;
  & > .anticon {
    position: relative;
    top: 5px;
    padding: 0 6px;
  }
  & > .ant-popover-message-title {
    flex: 1 1 auto;
    padding: 0;
  }
}

// 样式自定义 --- 空元素间距
.ant-empty-normal {
  margin: 10px 0;
}

// 样式自定义 --- form表单
.ant-form-horizontal,
.ant-form-vertical,
.ant-form-inline {
  .ant-form-item {
    display: flex;
    margin: 2px 0 9px;
    padding: 0;
    & > .ant-form-item-label {
      display: inline-block;
      flex: 0 0 auto;
      width: 75px;
      margin-right: 2px;
      line-height: 27px;
    }
    & > .ant-form-item-control-wrapper {
      display: inline-block;
      flex: 1 1 auto;
      vertical-align: middle;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      .ant-form-item-control {
        line-height: 27px;
        .ant-form-item-children {
          .ant-calendar-picker {
            width: 100%;
            min-width: 0 !important;
          }
        }
      }
    }
  }
}

// 样式自定义 --- form input
.ant-input,
.ant-input-sm,
.ant-input-lg {
  height: 26px;
  padding: 3px 7px;
  background-color: transparent;
}

// 样式自定义 --- form input-number
.ant-input-number input,
.ant-input-number-sm input,
.ant-input-number-lg input {
  height: 26px;
  padding: 3px 7px;
  background-color: transparent;
}

// 样式自定义 --- form select
.ant-select,
.ant-select-sm,
.ant-select-lg {
  .ant-select-selection--multiple {
    height: auto;
    min-height: 26px;
    padding: 2px 0;
  }
  .ant-select-selection--single {
    height: auto;
    min-height: 26px;
    padding: 2px 0;
  }
  .ant-select-selection__rendered {
    margin-left: 7px;
    line-height: 20px;
  }
  .ant-select-selection--multiple > ul > li,
  .ant-select-selection--multiple .ant-select-selection__rendered > ul > li {
    height: 18px;
    margin-top: 2px;
    line-height: 18px;
    // &.ant-select-search {
    //   width: 0;
    // }
  }
  &.ant-select-auto-complete {
    .ant-select-selection--multiple {
      height: auto;
      min-height: 26px;
      padding: 0;
    }
    .ant-select-selection--single {
      height: auto;
      min-height: 26px;
      padding: 0;
    }
    .ant-select-selection__rendered {
      margin-left: 0px;
      line-height: 26px;
    }
    .ant-select-search__field {
      height: 26px;
      padding: 2px 7px;
      line-height: 18px;
    }
  }
}
.ant-table-row-cell-ellipsis {
  z-index: 100 !important;
}

// 样式自定义 --- 按钮
.ant-btn,
.ant-btn-sm,
.ant-btn-lg {
  height: 26px;
  margin: 3px 4px;
  padding: 2px 8px;
  border-radius: 4px;
  &.ant-input-search-button {
    margin: 0;
  }
  & > .anticon + span,
  & > span + .anticon {
    margin-left: 3px;
  }
  .drawer-footer &,
  .table-operator & {
    margin-right: 8px;
  }
}

.tableTitle {
  display: flex;
}
.title-icons {
  cursor: pointer;
  font-size: 14px;
  font-weight: 700;
  padding: 0 10px;
  border-right: 1px solid #9f9f9f;
}

// 样式自定义 --- 表格
.table-wrapper,
.ant-table-wrapper {
  .ant-table-pagination.ant-pagination {
    margin: 5px 0 2px;
    text-align: right;
  }

  .ant-table-fixed-left table,
  .ant-table-fixed-right table {
    width: -webkit-fit-content;
    background: #fff;
  }
  .ant-table,
  .ant-table-small,
  .ant-table-large {
    & > .ant-table-title,
    & > .ant-table-content > .ant-table-footer {
      padding: 6px 8px 8px;
    }

    & > .ant-table-content > .ant-table-body {
      margin: 0;
    }

    & > .ant-table-content > .ant-table-header > table > .ant-table-thead > tr > th,
    & > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th,
    & > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-thead > tr > th,
    & > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-thead > tr > th,
    & > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-thead > tr > th,
    & > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-thead > tr > th,
    &
      > .ant-table-content
      > .ant-table-fixed-left
      > .ant-table-body-outer
      > .ant-table-body-inner
      > table
      > .ant-table-thead
      > tr
      > th,
    &
      > .ant-table-content
      > .ant-table-fixed-right
      > .ant-table-body-outer
      > .ant-table-body-inner
      > table
      > .ant-table-thead
      > tr
      > th {
      background-color: #fafafd;
    }

    & > .ant-table-content > .ant-table-body > table > .ant-table-tbody > tr > td,
    & > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th,
    &
      > .ant-table-content
      > .ant-table-fixed-left
      > .ant-table-body-outer
      > .ant-table-body-inner
      > table
      > .ant-table-tbody
      > tr
      > td,
    &
      > .ant-table-content
      > .ant-table-fixed-left
      > .ant-table-body-outer
      > .ant-table-body-inner
      > table
      > .ant-table-thead
      > tr
      > th,
    & > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-tbody > tr > td,
    & > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-thead > tr > th,
    &
      > .ant-table-content
      > .ant-table-fixed-right
      > .ant-table-body-outer
      > .ant-table-body-inner
      > table
      > .ant-table-tbody
      > tr
      > td,
    &
      > .ant-table-content
      > .ant-table-fixed-right
      > .ant-table-body-outer
      > .ant-table-body-inner
      > table
      > .ant-table-thead
      > tr
      > th,
    & > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-tbody > tr > td,
    & > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-thead > tr > th,
    & > .ant-table-content > .ant-table-header > table > .ant-table-tbody > tr > td,
    & > .ant-table-content > .ant-table-header > table > .ant-table-thead > tr > th,
    & > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-tbody > tr > td,
    & > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-thead > tr > th,
    & > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-tbody > tr > td,
    & > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-thead > tr > th {
      height: 45px;
      padding: 4px 8px;
    }
  }
}

.table-wrapper:not([no-striped]) {
  .ant-table,
  .ant-table-small,
  .ant-table-large {
    & > .ant-table-content > .ant-table-body > table > .ant-table-tbody > tr,
    &
      > .ant-table-content
      > .ant-table-fixed-left
      > .ant-table-body-outer
      > .ant-table-body-inner
      > table
      > .ant-table-tbody
      > tr,
    & > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-tbody > tr,
    &
      > .ant-table-content
      > .ant-table-fixed-right
      > .ant-table-body-outer
      > .ant-table-body-inner
      > table
      > .ant-table-tbody
      > tr,
    & > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-tbody > tr,
    & > .ant-table-content > .ant-table-header > table > .ant-table-tbody > tr,
    & > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-tbody > tr,
    & > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-tbody > tr {
      & > td {
        background-color: #ffffff;
      }
      &:nth-child(odd) > td:not([rowspan]) {
        background-color: #ffffff;
      }
      &:nth-child(even) > td:not([rowspan]) {
        background-color: #e4f2ff;
      }
    }
  }
}

// 样式自定义 --- 分页样式
.ant-pagination-prev,
.ant-pagination-next,
.ant-pagination-jump-prev,
.ant-pagination-jump-next,
.ant-pagination-total-text,
.ant-pagination-item {
  min-width: 26px;
  height: 26px;
  margin-top: 5px;
  margin-bottom: 5px;
  line-height: 26px;
}

// 样式自定义 --- 统一字体
.ant-btn,
.ant-form,
.ant-table,
.ant-input,
.ant-input-number,
.ant-select,
.ant-form label,
.ant-pagination,
.ant-notification,
.ant-message,
.ant-popover,
.ant-drawer,
.ant-modal,
.ant-alert {
  font-size: @font-size-base;
}

// 样式自定义 --- 单行省略
.single-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// 样式自定义 --- 手势鼠标
.cursor-pointer {
  cursor: pointer;
  user-select: none;
}

// 样式自定义 --- 上传禁用
.ant-upload {
  &.ant-upload-disabled {
    cursor: default;
  }
}

// 样式自定义 --- 动画禁用
// .ant-btn,
// .ant-form,
// .ant-table,
// .ant-input,
// .ant-select,
// .ant-input-number,
// .ant-switch,
// .ant-radio,
// .ant-checkbox,
// .ant-radio-group,
// .ant-checkbox-group,
// .ant-pagination {
//   * {
//     transition: none !important;
//   }
//   transition: none !important;
// }

// 消除 hope.min 样式影响
.layout.ant-layout {
  h1,
  h2,
  h3,
  .h1,
  .h2,
  .h3 {
    margin-top: 0;
    margin-bottom: 0;
  }
  label {
    display: inline-block;
    max-width: inherit;
    margin-bottom: inherit;
    font-weight: inherit;
  }
  .container {
    margin: 0;
    padding: 0;
  }
  .label {
    display: inherit;
    margin: 0;
    padding: 0;
    font-size: inherit;
    font-weight: inherit;
    line-height: inherit;
    color: inherit;
    text-align: inherit;
    white-space: inherit;
    vertical-align: inherit;
    border-radius: inherit;
  }
}

// 禁用样式
.ant-input[disabled] {
  color: rgba(0, 0, 0, 0.6);
  background-color: #f9f9f9;
}
.ant-select-disabled {
  color: rgba(0, 0, 0, 0.6);
}
.ant-select-disabled .ant-select-selection {
  background: #f9f9f9;
  cursor: not-allowed;
}
