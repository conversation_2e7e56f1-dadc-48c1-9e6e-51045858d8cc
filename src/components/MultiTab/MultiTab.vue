<script>
import { mapGetters } from 'vuex'
import { ExternalLink } from '@/utils/mixin'
import { setDocumentTitle, domTitle } from '@/utils/domUtil'

export default {
  name: 'MultiTab',
  mixins: [ExternalLink],
  data () {
    return {
      activeKey: ''
    }
  },
  computed: {
    ...mapGetters(['multiTab', 'keepAlive', 'visitedTags'])
  },
  created () {
    this.add()
    const route = this.$route
    const meta = route.meta || {}
    const match = meta.match || 'path'
    const isExternal = match === 'external'
    this.activeKey = !isExternal ? route.fullPath : this.getExternal(route)
    this.$store.dispatch('InjectMultipleTabs', this)
  },
  methods: {
    // 新增
    add () {
      if (this.$route.name && this.multiTab) {
        const route = this.$route
        const meta = route.meta || {}
        const query = route.query || {}
        const external = this.getExternal(route)
        const tabTitle = query.title || meta.title
        const tags = {
          ...this.$route,
          meta: {
            ...meta,
            external,
            tabTitle
          }
        }
        if (tabTitle) {
          setDocumentTitle(`${tabTitle} - ${domTitle}`)
        } else {
          setDocumentTitle(`${domTitle}`)
        }
        if (this.keepAlive) {
          this.$store.dispatch('AddCachedTags', tags)
        }
        this.$store.dispatch('AddVisitedTags', tags)
      }
      return false
    },
    // 激活
    active () {
      for (const tag of this.visitedTags) {
        const meta = tag.meta || {}
        const match = meta.match || 'path'
        const isExternal = match === 'external'
        if (!isExternal && tag[match] === this.$route[match] && tag.fullPath !== this.$route.fullPath) {
          this.$store.dispatch('UpdateVisitedTags', this.$route)
          break
        }
      }
    },
    // 获取
    target (key) {
      const arr = []
      const keys = [].concat(key)
      for (const tag of this.visitedTags) {
        const meta = tag.meta || {}
        const match = meta.match || 'path'
        const isExternal = match === 'external'
        const fullPath = !isExternal ? tag.fullPath : meta.external
        if (keys.some(key => tag === key || fullPath === key)) {
          arr.push(tag)
        }
      }
      return arr
    },
    // 删除
    remove (tags) {
      if (tags.length > 0) {
        this.$store.dispatch('DelTags', tags).then(({ stackTags }) => {
          if (
            tags.some(tag => {
              const meta = tag.meta || {}
              const match = meta.match || 'path'
              const isExternal = match === 'external'
              const activeKey = !isExternal ? tag.fullPath : this.getExternal(tag)
              return activeKey === this.activeKey
            })
          ) {
            const tag = stackTags[0] || {}
            const meta = tag.meta || {}
            const match = meta.match || 'path'
            const isExternal = match === 'external'
            const fullPath = !isExternal ? tag.fullPath : meta.external
            this.activeKey = fullPath || '/'
          }
        })
      }
    },
    // 关闭当前
    closeThis (key) {
      if (this.visitedTags.length > 1) {
        const tags = this.target(key)
        const find = tag => tags.includes(tag)
        const tag = this.visitedTags.find(find)
        tag && this.remove([tag])
      } else {
        this.$message.info('这是最后一个标签了, 无法被关闭')
      }
    },
    // 关闭左侧
    closeLeft (key) {
      const arr = []
      const tags = this.target(key)
      const find = tag => tags.includes(tag)
      const limit = this.visitedTags.findIndex(find)
      if (limit > 0) {
        this.visitedTags.forEach((item, index) => {
          if (index < limit) {
            arr.push(item)
          }
        })
        this.remove(arr)
      } else {
        this.$message.info('左侧没有标签')
      }
    },
    // 关闭右侧
    closeRight (key) {
      const arr = []
      const tags = this.target(key)
      const find = tag => tags.includes(tag)
      const limit = this.visitedTags.findIndex(find)
      if (limit < this.visitedTags.length - 1) {
        this.visitedTags.forEach((item, index) => {
          if (index > limit) {
            arr.push(item)
          }
        })
        this.remove(arr)
      } else {
        this.$message.info('右侧没有标签')
      }
    },
    // 关闭其他
    closeOther (key) {
      const arr = []
      const tags = this.target(key)
      const find = tag => tags.includes(tag)
      const limit = this.visitedTags.findIndex(find)
      this.visitedTags.forEach((item, index) => {
        if (index !== limit) {
          arr.push(item)
        }
      })
      this.remove(arr)
    },
    // 关闭指定
    closeArray (key) {
      this.remove(this.target(key))
    }
  },
  watch: {
    $route (route) {
      this.add()
      this.active()
      const meta = route.meta || {}
      const match = meta.match || 'path'
      const isExternal = match === 'external'
      this.activeKey = !isExternal ? route.fullPath : this.getExternal(route)
    },
    activeKey (fullPath) {
      // 优先在 route fullPath 里匹配
      for (const tag of this.visitedTags) {
        const meta = tag.meta || {}
        const match = meta.match || 'path'
        const isExternal = match === 'external'
        if (!isExternal && fullPath === tag.fullPath) {
          this.$router.push({ path: fullPath })
          return
        }
      }
      // 其次在 meta external 里匹配
      for (const tag of this.visitedTags) {
        const meta = tag.meta || {}
        const match = meta.match || 'path'
        const isExternal = match === 'external'
        if (isExternal && fullPath === meta.external) {
          this.$router.push({ path: tag.fullPath })
          return
        }
      }
      // 默认路由
      this.$router.push({ path: fullPath })
    }
  },
  render () {
    const onTagEdit = (targetKey, action) => {
      action === 'remove' ? this.closeThis(targetKey) : this[action](targetKey)
    }

    const menu = keyPath => {
      return (
        <a-menu
          {...{
            on: {
              click: ({ key, item, domEvent }) => {
                this[key](keyPath)
              }
            }
          }}
        >
          <a-menu-item key="closeThis">关闭当前标签</a-menu-item>
          <a-menu-item key="closeRight">关闭右侧标签</a-menu-item>
          <a-menu-item key="closeLeft">关闭左侧标签</a-menu-item>
          <a-menu-item key="closeOther">关闭其他标签</a-menu-item>
        </a-menu>
      )
    }

    const pane = (title, keyPath) => {
      return (
        <a-dropdown overlay={menu(keyPath)} trigger={['contextmenu']}>
          <span style={{ userSelect: 'none' }} title={title.length > 20 ? title : false}>
            {title.length > 20 ? title.substr(0, 20) + '...' : title}
          </span>
        </a-dropdown>
      )
    }

    const panes = this.visitedTags.map(tag => {
      const meta = tag.meta || {}
      const match = meta.match || 'path'
      const isExternal = match === 'external'
      const fullPath = !isExternal ? tag.fullPath : meta.external
      return (
        <a-tab-pane
          ref="tag"
          style={{ height: 0 }}
          tab={pane(meta.tabTitle || meta.title, fullPath)}
          key={fullPath}
          closable={this.visitedTags.length > 1}
        ></a-tab-pane>
      )
    })

    return (
      <div class="ant-pro-multi-tab">
        <div class="ant-pro-multi-tab-wrapper">
          <a-tabs
            hideAdd
            type={'editable-card'}
            v-model={this.activeKey}
            tabBarStyle={{ background: '#FFF', margin: 0, paddingLeft: '16px', paddingTop: '1px' }}
            {...{ on: { edit: onTagEdit } }}
          >
            {panes}
          </a-tabs>
        </div>
      </div>
    )
  }
}
</script>
