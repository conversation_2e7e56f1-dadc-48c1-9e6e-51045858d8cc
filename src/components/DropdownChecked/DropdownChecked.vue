<template>
  <section class="contain">
    <a-card class="card" size="small">
      <template v-if="showTitle" slot="extra">
        <slot name="extra">
          <a href="javascript:void(0)">{{ more }}</a>
        </slot>
      </template>
      <template v-if="showTitle" slot="title">
        <slot name="title">
          <div>{{ title }}</div>
        </slot>
      </template>
      <SlickList
        :lockToContainerEdges="true"
        :pressDelay="pressDelay"
        v-model="infoList"
        helper-class="slick-item"
        class="ul"
        @input="griderChange"
        axis="xy"
      >
        <SlickItem
          v-for="(item, index) in infoList"
          style="z-index: 10000;"
          :index="index"
          :disabled="item.disabled || disabled"
          :key="item[keyId]"
          class="li item"
        >
          <slot name="text">
            <span style="margin-right: 5px;">
              <a-checkbox :disabled="item.disabled || (checkNum <= 5 && item.check)" v-model="item.check" @change="checkChange"/>
            </span>
            <div class="text" >
              <div class="textDiv" v-show="!item.edit">{{ item[label] }}</div>
              <a-input size="small" v-show="item.edit" v-model="item[label]" />
            </div>
          </slot>
          <span v-show="!disabled && !item.disabled">
            <!-- edit -->
            <a href="javascript:void(0)" v-if="isSystemManager">
              <a-icon v-show="!item.edit" type="edit" class="icon" @click.stop="statusChange('edit', item, index)"/>
              <a-icon v-show="item.edit" type="check" class="icon" @click.stop="statusChange('edit', item, index)" />
            </a>
            <!-- sort -->
            <a href="javascript:void(0)" v-if="showSort">
              <a-icon type="sort-ascending" class="icon" :style="{ color: item.sorter ? '#1890ff' : '#666' }" @click.stop="statusChange('sort', item, index)"/>
            </a>
            <!-- fixed -->
            <a href="javascript:void(0)" v-if="showFixed">
              <a-icon v-show="item.fixed === 'right'" class="icon" type="vertical-left" @click.prevent="statusChange('right', item, index)"/>
              <a-icon v-show="item.fixed === 'left' || item.fixed === true" class="icon" type="vertical-right" @click.prevent="statusChange('left', item, index)"/>
              <a-icon v-show="!item.fixed" class="icon" type="vertical-align-bottom" @click.prevent="statusChange('fixed', item, index)"/>
            </a>
          </span>
        </SlickItem>
      </SlickList>
    </a-card>
    <slot name="footer">
      <div class="footer">
        <!-- <a href="javascript:void(0)" @click="selectAll">全选</a> -->
        <a-button type="link" @click="selectAll" size="small">
          全选
        </a-button>
        <!-- <a href="javascript:void(0)" @click="save">保存</a> -->
        <a-button type="link" @click="save" :loading="loading" size="small">
          保存
        </a-button>
      </div>
    </slot>
  </section>
</template>
<script>
import { SlickList, SlickItem } from 'vue-slicksort'
import { requestBuilder } from '@/utils/util'
import { ORG_ID, PERSON_ID } from '@/store/mutation-types'
import Vue from 'vue'
import * as tableApi from '@/api/material/table.js'
import store from '@/store'
const personId = Vue.ls.get(PERSON_ID)
const orgId = Vue.ls.get(ORG_ID)
export default {
  name: 'DropdownChecked',
  props: {
    // 双向绑定column
    value: {
      type: Array,
      default: () => {
        return [
          {
            title: '可拖拽',
            dataIndex: 'action',
            check: true,
            edit: false,
            sorter: true,
            fixed: true,
            disabled: false,
            uuid: 1
          }
        ]
      }
    },
    // 标题
    title: {
      type: String,
      default: () => {
        return 'title'
      }
    },
    // name
    tableId: {
      type: String,
      default: () => {
        return 'approvePrlineTable'
      }
    },
    // 右标题
    rightIcon: {
      type: String,
      default: () => {
        return 'more'
      }
    },
    pressDelay: {
      type: Number,
      default: () => {
        return 300
      }
    },
    disabled: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    // 唯一值
    keyId: {
      type: String,
      default: () => {
        return 'uuid'
      }
    },
    // 各列显示的字段
    label: {
      type: String,
      default: () => {
        return 'title'
      }
    },
    showTitle: {
      type: Boolean,
      default: () => {
        return true
      }
    },
    showFixed: {
      type: Boolean,
      default: () => {
        return true
      }
    },
    showSort: {
      type: Boolean,
      default: () => {
        return true
      }
    }
  },
  components: {
    SlickList,
    SlickItem
  },
  data () {
    return {
      orgId,
      personId,
      infoList: this.value || [],
      originList: this.value || [],
      loading: false
    }
  },
  watch: {
    value (val) {
      this.infoList = this.value || []
    }
  },
  computed: {
    checkNum () {
      const arr = this.infoList.filter(item => {
        return item.check
      })
      return arr.length
    },
    isSystemManager () {
      const roleNames = store.getters.roleNames
      return roleNames && roleNames.includes('系统管理员')
    }

  },
  methods: {
    griderChange () {
      this.$emit('input', this.infoList)
    },
    statusChange (key, item, index) {
      if (key === 'edit') {
        if (!item.title.trim()) {
          item.title = item.dataIndex
        }
        this.$set(item, 'edit', !item.edit)
        // item.edit = !item.edit
      } else if (key === 'sort') {
        this.$set(item, 'sorter', !(item.sorter || false))
        // item.sorter = !item.sorter
      } else if (key === 'left') {
        this.$set(item, 'fixed', 'right')
        // item.fixed = 'right'
      } else if (key === 'right') {
        this.$set(item, 'fixed', false)
        // item.fixed = false
      } else if (key === 'fixed') {
        this.$set(item, 'fixed', 'left')
        // item.fixed = 'left'
      }

      this.$forceUpdate()
      this.checkChange()
    },
    checkChange () {
      console.log(111)
      this.$emit('input', this.infoList)
    },
    selectAll () {
      this.infoList.forEach(item => {
        item.check = true
      })
      this.$emit('input', this.infoList)
    },
    save () {
      // this.$message.info('正在开发中。。。')
      this.loading = true
      this.infoList.forEach(item => {
        if (item.fixed === 'left' || item.fixed === true) {
          item.sortNum = 0
        } else if (item.fixed === 'right') {
          item.sortNum = 2
        } else {
          item.sortNum = 1
        }
      })
      this.infoList.sort((a, b) => {
        return a.sortNum - b.sortNum
      })
      this.$emit('input', this.infoList)
      const arr = this.infoList.map((item, index) => {
        return {
          ...item,
          index: index,
          width: item.dataIndex === 'action' ? 0 : item.width,
          fixed: item.fixed ? item.fixed === true ? 'left' : item.fixed : ''
          // disabled: null
        }
      })
      const param = {
        tableId: this.tableId,
        tableRecordList: arr
      }
      tableApi.modifyPersonalTableRecord(requestBuilder('update', param)).then(res => {
        if (res.code === '0000') {
          this.$notification.success({
            message: '系统消息',
            description: res.message || '保存成功！'
          })
        } else {
          this.$notification.error({
            message: '系统消息',
            description: res.message || '保存失败！'
          })
        }
      }).finally(() => {
        this.loading = false
        this.$store.dispatch('SetTodoTableGroup', { orgId, personId })
      })
    },
    translate (value) {
      if (typeof value === 'function') {
        return value()
      }
      return value
    }
  }
}
</script>
<style lang="less" scoped>
.contain {
  width: 100%;
  max-height: 300px;
  .card {
    max-height: 300px;
    overflow: scroll;
    position: relative;
  }
}
.footer {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 30px;
  background: #ffffff;
  padding: 0 10px;
  display: flex;
  align: right;
  font-size: 12px;
  text-align: right;
  justify-content: space-between;
}
.ul{
  z-index: 1000;
  width: 100%;
  .li{
    width: 100%;
    height: 26px;
    margin: 9px 4px;
    /* padding:0 4px; */
    z-index: 1000;
  }
  .item {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    /* background: #fffb29; */
  }
  .text {
    flex: 1;
    width: 100px;
    .textDiv {
      width: 100px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
.slick-item {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  /* background: #fffb29; */
  .text {
    flex: 1;
    width: 100px;
    .textDiv {
      width: 100px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
.icon {
  margin-left: 5px;
}
</style>
