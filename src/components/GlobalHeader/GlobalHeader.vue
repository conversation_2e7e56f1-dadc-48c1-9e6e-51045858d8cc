<template>
  <transition name="showHeader">
    <div
      v-if="visible"
      class="header-animat"
    >
      <a-layout-header
        :class="[fixedHeader && 'ant-header-fixedHeader', sidebarOpened ? 'ant-header-side-opened' : 'ant-header-side-closed', ]"
        :style="{ padding: '0' }"
      >
        <div
          v-if="mode === 'sidemenu'"
          class="header"
          :class="theme"
        >
          <a-icon
            v-if="isMobile() && device === 'mobile'"
            class="trigger"
            :type="collapsed ? 'menu-fold' : 'menu-unfold'"
            @click="toggle"
          />
          <h1
            v-else
            :style="{ color: '#2e3650', fontSize: '20px', padding: !collapsed ? '0 10px 0 0' : '0 10px 0 25px', display: 'inline-block' }"
          >
            <a-icon
              v-if="!collapsed"
              class="trigger"
              style="padding-right: 10px;"
              :type="!collapsed ? 'menu-fold' : 'menu-unfold'"
              @click="toggle"
            />
            {{ headerTitle() }}
          </h1>
          <user-menu />
        </div>
        <div
          v-else
          :class="['top-nav-header-index', theme]"
        >
          <div class="header-index-wide">
            <div class="header-index-left">
              <logo
                :layoutMode="mode"
                :device="device"
                :show-title="device !== 'mobile'"
                class="top-nav-header"
              />
              <s-menu
                v-if="device !== 'mobile'"
                mode="horizontal"
                :menu="menus"
                :theme="theme"
              />
              <a-icon
                v-else
                class="trigger"
                :type="collapsed ? 'menu-fold' : 'menu-unfold'"
                @click="toggle"
              />
            </div>
            <user-menu class="header-index-right" />
          </div>
        </div>
      </a-layout-header>
    </div>
  </transition>
</template>

<script>
import { mixin } from '@/utils/mixin'
import { ORG_ID } from '@/store/mutation-types'
import UserMenu from '../tools/UserMenu'
import Logo from '../tools/Logo'
import Vue from 'vue'
import SMenu from '../Menu/'
const USER_ORG_ID = Vue.ls.get(ORG_ID)
const IS_HJ_ENV = USER_ORG_ID === '1.100.131'
export default {
  name: 'GlobalHeader',
  components: {
    UserMenu,
    SMenu,
    Logo
  },
  mixins: [mixin],
  props: {
    mode: {
      type: String,
      default: 'sidemenu'
    },
    menus: {
      type: Array,
      required: true
    },
    theme: {
      type: String,
      required: false,
      default: 'dark'
    },
    isMobile: {
      type: Function,
      required: false,
      default: function () {}
    },
    collapsed: {
      type: Boolean,
      required: false,
      default: false
    },
    device: {
      type: String,
      required: false,
      default: 'desktop'
    }
  },
  data () {
    return {
      IS_HJ_ENV,
      USER_ORG_ID: Vue.ls.get(ORG_ID),
      visible: true,
      oldScrollTop: 0
    }
  },
  computed: {
    headerTitle () {
      return function () {
        return '欢迎登录数字设备协同管理平台'
      }
    }
  },
  mounted () {
    document.addEventListener('scroll', this.handleScroll, { passive: true })
  },
  methods: {
    handleScroll () {
      if (!this.autoHideHeader) {
        return
      }

      const scrollTop = document.body.scrollTop + document.documentElement.scrollTop
      if (!this.ticking) {
        this.ticking = true
        requestAnimationFrame(() => {
          if (this.oldScrollTop > scrollTop) {
            this.visible = true
          } else if (scrollTop > 300 && this.visible) {
            this.visible = false
          } else if (scrollTop < 300 && !this.visible) {
            this.visible = true
          }
          this.oldScrollTop = scrollTop
          this.ticking = false
        })
      }
    },
    toggle () {
      this.$emit('toggle')
    }
  },
  beforeDestroy () {
    document.body.removeEventListener('scroll', this.handleScroll, true)
  }
}
</script>

<style lang="less">
@import '../index.less';

.header-animat {
  position: relative;
  z-index: @ant-global-header-zindex;
  .trigger {
    .dark > & {
      &.menu-fold {
        color: rgba(255, 255, 255, 0.65);
      }
      &.menu-unfold {
        color: rgba(255, 255, 255, 0.65);
      }
    }
  }
}
.showHeader-enter-active {
  transition: all 0.25s ease;
}
.showHeader-leave-active {
  transition: all 0.25s ease;
}
.showHeader-enter,
.showHeader-leave-to {
  opacity: 0;
}
</style>
