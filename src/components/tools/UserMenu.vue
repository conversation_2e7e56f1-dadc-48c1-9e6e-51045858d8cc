<template>
  <div class="user-wrapper">
    <div class="content-box">
      <a
        v-if="true"
        href="javascript:;"
        @click="showDrawer"
      >
        <span class="action">个性化</span>
      </a>
      <!-- <a
        v-if="true"
        href=""
        target="_blank"
      >
        <span class="action">用户操作速成手册</span>
      </a> -->
      <a-dropdown>
        <span class="action ant-dropdown-link user-dropdown-menu">
          <a-avatar
            class="avatar"
            size="small"
            :src="avatar"
          />
          <span>{{ nickname }}</span>
        </span>

        <a-menu
          slot="overlay"
          class="user-dropdown-menu-wrapper"
        >
          <a-menu-item
            v-if="false"
            key="0"
          >
            <router-link :to="{ name: 'center' }">
              <a-icon type="user" />
              <span>个人中心</span>
            </router-link>
          </a-menu-item>

          <a-menu-item
            v-if="false"
            key="1"
          >
            <router-link :to="{ name: 'settings' }">
              <a-icon type="setting" />
              <span>账户设置</span>
            </router-link>
          </a-menu-item>

          <a-menu-divider v-if="false" />

          <a-menu-item key="2">
            <a
              href="javascript:;"
              @click="showModal"
            >
              <a-icon type="setting" />
              <span>修改密码</span>
            </a>
          </a-menu-item>

          <a-menu-item key="3">
            <a
              href="javascript:;"
              @click="handleLogout"
            >
              <a-icon type="logout" />
              <span>退出登录</span>
            </a>
          </a-menu-item>
        </a-menu>
      </a-dropdown>

      <a-modal
        v-model="visible"
        title="修改密码"
        @click="showModal"
      >
        <template slot="footer">
          <a-button
            key="back"
            @click="handleCancel"
          >取消</a-button>
          <a-button
            key="submit"
            type="primary"
            @click="handleOk()"
          >提交</a-button>
        </template>

        <a-form
          :form="form"
          layout="vertical"
          hide-required-mark
        >
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="原密码">
                <a-input-password
                  v-decorator="['password', {
                    rules: [{ required: true, message: '原密码不能为空' }],
                  }]"
                  placeholder="请输入原密码"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item
                label="新密码"
                has-feedback
              >
                <a-input-password
                  v-decorator="['newPassword', {
                    rules: [
                      { required: true, message: '密码不能为空' },
                      { validator: validateToNextPassword }
                    ]
                  }]"
                  placeholder="请输入新密码"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item
                label="确认新密码"
                has-feedback
              >
                <a-input-password
                  v-decorator="['confirmPassword', {
                    rules: [
                      { required: true, message: '密码不能为空' },
                      { validator: compareToFirstPassword }
                    ]
                  }]"
                  placeholder="请确认新密码"
                  @blur="handleConfirmBlur"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-modal>
      <a-drawer
        title="个性化配置"
        width="360"
        placement="right"
        :closable="false"
        :visible="visibleDrawer"
        @close="onCloseDrawer"
      >
        <div>
          <div style="margin-bottom: 30px;">主题色切换</div>
          <div class="topicCol">
            <div class="lights" @click="changeTheme('light')"/>
            <div class="darks" @click="changeTheme('dark')"/>
          </div>
        </div>
      </a-drawer>
    </div>
  </div>
</template>

<script>
// import md5 from 'md5'
import Vue from 'vue'
import { mapActions, mapGetters } from 'vuex'
import { OPERATOR, DEFAULT_THEME } from '@/store/mutation-types'
import NoticeIcon from '@/components/NoticeIcon'
import { changePassword } from '@/api/system/login'
import { mixin } from '@/utils/mixin'

export default {
  name: 'UserMenu',
  mixins: [mixin],
  data () {
    return {
      confirmDirty: false,
      visible: false,
      visible2: false,
      form: this.$form.createForm(this),
      visibleDrawer: false,
      theme: Vue.ls.get(DEFAULT_THEME)
      // password: '',
      // newpassword: '',
      // confirmPassword: ''
    }
  },
  components: {
    NoticeIcon
  },
  computed: {
    ...mapGetters(['nickname', 'avatar'])
  },
  methods: {
    changeTheme (style) {
      Vue.ls.set(DEFAULT_THEME, style)
      this.doChangeTheme(style)
    },
    showDrawer () {
      this.visibleDrawer = true
    },
    onCloseDrawer () {
      this.visibleDrawer = false
    },
    showError () {
      this.visible2 = true
    },
    handleOk2 (e) {
      this.visible2 = false
    },
    showModal () {
      this.visible = true
    },
    handleConfirmBlur (e) {
      const value = e.target.value
      this.confirmDirty = this.confirmDirty || !!value
    },
    compareToFirstPassword (rule, value, callback) {
      const form = this.form
      if (value && value !== form.getFieldValue('newPassword')) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    },
    validateToNextPassword (rule, value, callback) {
      const form = this.form
      var regex = new RegExp('(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z]).{8,30}')
      if (!regex.test(value)) {
        callback(new Error('您的密码复杂度太低，密码长度必须8位以上，包含大小写字母和数字！'))
      }
      if (value && this.confirmDirty) {
        form.validateFields(['confirmPassword'], { force: true })
      }
      callback()
    },
    handleOk () {
      const param = {
        userNo: Vue.ls.get(OPERATOR),
        password: this.form.getFieldValue('password'),
        newPassword: this.form.getFieldValue('newPassword')
      }
      changePassword({ param: param })
        .then(res => {
          if (res.code === '0000') {
            this.$message.success('修改成功，即将返回登录界面')
            this.Logout({})
              .then(() => {
                setTimeout(() => {
                  window.location.reload()
                }, 16)
              })
              .catch(err => {
                this.$message.error({
                  title: '错误',
                  description: err.message
                })
              })
          } else {
            this.$message.error(res.message)
          }
        })
        .catch(err => {
          this.$message.error({
            title: '错误',
            description: err.message
          })
        })
    },
    handleCancel (e) {
      this.visible = false
    },
    ...mapActions(['Logout']),
    handleLogout () {
      this.$confirm({
        title: '提示',
        content: '真的要注销登录吗 ?',
        onOk: () => {
          return this.Logout({})
            .then(() => {
              setTimeout(() => {
                window.location.reload()
              }, 16)
            })
            .catch(err => {
              this.$message.error({
                title: '错误',
                description: err.message
              })
            })
        },
        onCancel () {}
      })
    }
  }
}
</script>
<style lang="less" scoped>
  .topicCol {
    display: flex;
  }
  .lights {
    background-color: #ececec;
    width: 50px;
    height: 50px;
    border-radius: 5px;
    box-shadow: 0px 1px 4px rgba(0,0,0,0.3),0px 0px 50px #eaeaea inset;
    cursor: pointer;
    margin-right: 15px;
  }
  .darks {
    background-color: #292d5e;
    width: 50px;
    height: 50px;
    border-radius: 5px;
    box-shadow: 0px 1px 4px rgba(0,0,0,0.3),0px 0px 50px #000b3e inset;
    cursor: pointer;
  }
  .dark {
    color: red;
  }
</style>
