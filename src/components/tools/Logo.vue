<template>
  <div v-if="device !== 'mobile'" class="logo">
    <img
      v-if="!collapsed"
      :style="getStyle"
      :src="imgUrl"
    >
    <a-icon
      v-if="collapsed && layoutMode === 'sidemenu'"
      :type="!collapsed ? 'menu-fold' : 'menu-unfold'"
      class="trigger"
      @click="toggle"
    />
  </div>
  </div>
</template>

<script>
export default {
  name: 'Logo',
  props: {
    layoutMode: {
      type: String,
      required: false,
      default: 'sidemenu'
    },
    collapsed: {
      type: Boolean,
      required: false,
      default: false
    },
    device: {
      type: String,
      required: false,
      default: 'desktop'
    }
  },
  data () {
    return {
      imgUrl: require('@/assets/logo/logo.png')
    }
  },
  computed: {
    getStyle () {
      if (this.layoutMode === 'topmenu') {
        return {
          'height': '100%',
          'margin': '0 auto',
          'display': 'block'
        }
      }
      return {
        'width': '100%',
        'margin': '0 auto',
        'display': 'block'
      }
    }
  },
  methods: {
    toggle () {
      this.$emit('toggle')
    }
  }
}
</script>

<style lang="less" scoped>
.logo {
  position: relative;
  .trigger {
    width: 60px;
    height: 64px;
    line-height: 66px;
    padding: 0 20px;
    text-align: center;
    vertical-align: middle;
    display: inline-block;
    position: absolute;
    top: 0;
    right: 0;
  }
}
</style>
