<template>
  <section print-area>
    <slot/>
  </section>
</template>

<script>
import Print from './print'

export default {
  name: 'PrintArea',
  data () {
    return {
      // size: 'A4 landscape',
      size: 'A4 portrait',
      margin: '0 0 0 0',
      padding: '0 0 0 0',
      noPrint: '.no-print'
    }
  },
  methods: {
    doPrint (media = {}, callback) {
      Print(this.$el, {
        'media': [
          'size:' + (media.size || this.size),
          'margin:' + (media.margin || this.margin),
          'padding:' + (media.padding || this.padding)
        ],
        'noPrint': (media.noPrint || this.noPrint),
        'callback': callback
      })
    }
  }
}
</script>

<style lang="less">
  @media print {
    [print-area] {
      [break-avoid] {
        page-break-inside: avoid;
      }
      [break-both],
      [break-before] {
        break-before: always;
        page-break-before: always;
      }
      [break-both],
      [break-after] {
        break-after: always;
        page-break-after: always;
      }
    }
  }
</style>

<style lang="less" scoped>
  [print-area] {
    display: none;
  }
  @media print {
    [print-area] {
      display: block;
    }
  }
</style>
