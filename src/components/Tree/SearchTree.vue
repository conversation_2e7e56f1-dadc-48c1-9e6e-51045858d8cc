<template>
  <div class="down-tree">
    <a-input-search
      style="margin-bottom: 2px;width: 95%;"
      placeholder="搜索"
      @change="searchContent"
      :allowClear="true"
      v-model="searchAssetSb"
    />
    <a-tree
      class="tree_box"
      ref="tree"
      @select="onSelect"
      :tree-data="currentTree"
      :showIcon="showIcon"
      :type="type"
      :replaceFields="replaceFields" >
      <a-icon slot="appstore" type="appstore" />
      <a-icon slot="child" type="apartment" />
    </a-tree>
    <div id="ActivePopulation" ref="ActivePopulation" style="width: 100%; height: 20%;" />
  </div>
</template>

<script>
import * as baseApi from '@/api/system/base'
export default {
  name: 'SearchTree',
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  created () {
    this.initOptions(this.type)
  },
  data () {
    return {
      showIcon: true,
      searchAssetSb: '',
      treeList: [],
      currentTree: this.treeList,
      replaceFields: {
        children: 'children',
        title: 'label',
        key: 'value'
      }
    }
  },
  methods: {
    initOptions () {
      baseApi.getTreeById({ id: 'deptAssetSb' }).then(res => {
        if (res.code === '0000') {
          const data = res.result.sort((a, b) => { return a.value - b.value })
          data.forEach(item => {
            item.slots = { icon: 'appstore' }
            if (item.children) {
              item.children.forEach(item2 => {
                item2.slots = { icon: 'child' }
              })
            }
          })
          this.treeList = data
          this.currentTree = data
        }
      })
      this.$nextTick(() => {
        setTimeout(() => {
          this.getLoadEcharts()
        }, 100)
      })
    },
    searchContent () {
      clearTimeout(this.timer) // 清除延迟执行
      this.timer = setTimeout(() => { // 设置延迟执行
        this.currentTree = this.searchTree(this.treeList, this.searchAssetSb, true)
      }, 500)
    },
    async onSelect (selectedKeys, info) {
      console.log('selectedKeys', selectedKeys)
      console.log('info', info)
    },
    searchTree (tree, keyword, includeChildren = false) {
      const newTree = []
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.label.includes(keyword)) {
          // 如果当前节点符合条件，则将其复制到新的树形结构中，并根据 includeChildren 参数决定是否将其所有子节点也复制到新的树形结构中
          newTree.push({ ...node, children: includeChildren ? this.searchTree(node.children || [], '', true) : [] })
        } else if (node.children) {
          // 如果当前节点不符合条件且存在子节点，则递归遍历子节点，以继续搜索
          const result = this.searchTree(node.children, keyword, true)
          if (result.length > 0) {
            // 如果子节点中存在符合条件的节点，则将其复制到新的树形结构中
            newTree.push({ ...node, children: result })
          }
        }
      }
      return newTree
    },
    getLoadEcharts () {
      this.dom = this.$echarts.init(
        document.getElementById('ActivePopulation')
      )
      this.dom.setOption({
        title: {
          text: '各部门活跃人数统计',
          left: 'center',
          top: 5,
          textStyle: {
            color: '#59A5CD',
            fontStyle: 'normal',
            fontWeight: 'bold',
            fontSize: 8
          }
        },
        legend: {
          orient: 'vertical',
          right: 60,
          top: '15%',
          textStyle: {
            color: '#59A5CD'
          }
        },
        toolbox: {
          show: true,
          feature: {
            mark: { show: true }
          }
        },
        series: [
          {
            name: 'Nightingale Chart',
            type: 'pie',
            radius: [8, 30],
            center: ['10%', '20%'],
            // roseType: 'area',
            label: {
              color: '#59A5CD',
              formatter: '{b}: {c}人'
            },
            labelLine: {
              lineStyle: {
                color: '#59A5CD'
              },
              smooth: 0.2,
              length: 10,
              length2: 20
            },
            itemStyle: {
              borderRadius: 8
            },
            data: [] || []
          }
        ]
      })
      window.addEventListener('resize', () => {
        // 第六步，执行echarts自带的resize方法，即可做到让echarts图表自适应
        this.dom.resize()
        // 如果有多个echarts，就在这里执行多个echarts实例的resize方法,不过一般要做组件化开发，即一个.vue文件只会放置一个echarts实例
        /*
        this.myChart2.resize();
        this.myChart3.resize();
        ......
        */
      })
    }
  }
}
</script>
<style scoped lang="less">
.down-tree{
  height: 600px;
  display: block;
  overflow-y: scroll;
}
</style>
