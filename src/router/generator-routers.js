import { rootRouter, externalRouter, notFoundRouter } from '@/config/router.config'
import { requestBuilder } from '@/utils/util'
import * as loginApi from '@/api/system/login'

/**
 * 转换树形结构
 * @param list 源数组
 * @param tree 树
 * @param parent 父节点
 */
const listToTree = (list, tree, parent) => {
  list.forEach(item => {
    if (item.parentId === parent.id) {
      // 创建 route
      const key = item.key || item.name
      const route = { ...item, key: key, children: [] }

      // 迭代 list，找到符合的子菜单
      listToTree(list, route.children, item)

      // 删掉不存在 children 属性
      if (route.children.length <= 0) {
        delete route.children
      }

      // push route
      tree.push(route)
    }
  })
}

/**
 * 动态生成菜单
 * @param params
 * @param components
 * @returns {Promise<Router>}
 */
export const generatorDynamicRouter = ({ params, components }) => {
  return loginApi.getCurrentUserNav(requestBuilder('generateRoutes', params, '0', '0')).then(res => {
    if (res.code !== '0000') {
      return Promise.reject(res)
    }

    // 创建节点组
    const { result = [] } = res
    const { children = [] } = {}

    // 生成树型数组
    listToTree(result, children, { id: '0' })

    // 生成路由表
    const tree = [{ ...rootRouter, children }]
    const routers = generator(tree, {}, components)

    // 添加静态路由
    routers[0].children.unshift(externalRouter)
    routers.push(...notFoundRouter)

    // 菜单路由
    return routers
  })
}

/**
 * 动态生成路由路径
 * @param parent
 * @param item
 * @returns {路由路径}
 */
export const generatorDynamicPath = (parent = {}, item = {}) => {
  const keyPath = item.key || ''
  const itemPath = item.path || ''
  const parentPath = parent.path || ''
  const isNotIframeView = item.component !== 'PageFrame'

  return !isNotIframeView || !itemPath
    ? (parentPath + '/' + keyPath).replace(/^\/*([^/].*)/, '/$1')
    : itemPath
}

/**
 * 动态加载路由对应页面的组件
 * @param parent
 * @param item
 * @returns { 组件 }
 */
export const loadDynamicComponent = (parent = {}, item = {}, components = {}) => {
  // 组件路径
  const itemPath = item.path || ''
  const parentPath = parent.path || ''
  const componentPath = item.component || ''
  const isNotIframeView = item.component !== 'PageFrame'
  const tempViewPath = !(isNotIframeView && itemPath) ? parentPath + '/' + componentPath : itemPath
  const currentPath = tempViewPath.replace(/^\/*([^/].*)/, '/$1')

  // component
  if (String(componentPath) !== componentPath) {
    return componentPath
  }

  // layout
  if (components[componentPath]) {
    return components[componentPath]
  }

  // views
  if (components[currentPath]) {
    return components[currentPath]
  }

  // 默认组件
  return () => import(`@/views/system/${currentPath.replace(/^\/+/, '')}`)
}

/**
 *  布局页面路由生成器
 * @param context
 * @returns components
 */
export const generatorLayoutRouter = context => {
  const routerComponents = {}
  const replaceKeyRegex = /^.*\/([^/]+)\.vue$/
  context.keys().forEach(item => {
    const key = item.replace(replaceKeyRegex, '$1')
    routerComponents[key] = context(item).default
  })
  return routerComponents
}

/**
 *  业务页面路由生成器
 * @param context
 * @returns components
 */
export const generatorViewsRouter = (...contexts) => {
  const routerComponents = {}
  const replaceKeyRegex = /^\.{0,2}(\/([^/]+\/?)+)\.vue$/
  for (const context of contexts) {
    context.keys().forEach(item => {
      const key = item.replace(replaceKeyRegex, '$1')
      if (!routerComponents[key]) {
        routerComponents[key] = () => Promise.resolve(context(item))
      }
    })
  }
  return routerComponents
}

/**
 *  生成层级路由表
 * @param routerMap
 * @param parent
 * @returns {*}
 */
export const generator = (routerMap, parent = {}, components = {}) => {
  return routerMap.map(item => {
    const { title, show, noCache, hideChildren, hiddenHeaderContent, target, icon } = item.meta || {}
    const isNotIframeView = item.component !== 'PageFrame'
    const match = isNotIframeView ? 'path' : 'external'
    const currentRouter = {
      // 路由id
      id: item.id,

      // 路由path
      path: generatorDynamicPath(parent, item),

      // 路由名称
      name: item.name || item.key || '',

      // 路由组件
      component: loadDynamicComponent(parent, item, components),

      // 路由meta
      meta: {
        icon: icon,
        show: show,
        match: match,
        title: title,
        target: target,
        groupId: (parent.meta || {}).groupId || item.id,
        noCache: (parent.meta || {}).noCache === true || noCache === true,
        external: (!isNotIframeView && item.path) || '',
        permission: [item.name || item.key || ''],
        componentName: item.component || item.name || item.key || '',
        hiddenHeaderContent: hiddenHeaderContent !== undefined
          ? hiddenHeaderContent !== false
          : (parent.meta || {}).hiddenHeaderContent !== false
      }
    }

    // 是否设置了隐藏菜单
    if (show === false) {
      currentRouter.hidden = true
    }

    // PageFrame 使用别名
    if (!isNotIframeView) {
      currentRouter.alias = currentRouter.path + '/*'
    }

    // 为了防止出现后端返回结果不规范，处理有可能出现拼接出两个 反斜杠
    if (!currentRouter.path.startsWith('http')) {
      currentRouter.path = currentRouter.path.replace('//', '/')
    }

    // 重定向
    if (item.redirect) {
      currentRouter.redirect = item.redirect
    }

    // 是否有子菜单，并递归处理
    if (item.children && item.children.length > 0) {
      currentRouter.children = generator(item.children, currentRouter, components)
    }

    // 是否设置了隐藏子菜单
    if (
      hideChildren === true ||
      !currentRouter.children ||
      !currentRouter.children.some(route => route.meta.show !== false)
    ) {
      currentRouter.hideChildrenInMenu = true
    }

    // 路由表
    return currentRouter
  })
}
